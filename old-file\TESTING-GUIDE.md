# Testing Guide for Product Price Fixes

This guide will help you test all the fixes we've implemented to ensure that:
1. All products from all stores are fetched correctly
2. BH Store regular prices are calculated properly
3. All stores show the correct number of products

## Prerequisites

Make sure you have Node.js installed and all dependencies are up to date:

```bash
npm install
```

## Step 1: Verify Fixes

First, let's verify that all the fixes have been applied correctly:

```bash
node verify-fixes.js
```

This script will check all the relevant files to ensure they contain the fixed code. If any fixes are missing, the script will tell you which ones need to be applied.

## Step 2: Clean the Database

To start with a clean slate, run the database cleanup script:

```bash
node clean-database.js
```

This script will:
1. Remove all products from the database
2. Remove all product prices and quantities
3. Reset the ID sequences
4. Provide a verification that the database is empty

## Step 3: Use the Fetch Button

Now, use the fetch button in the UI to fetch all products from all stores:

1. Start the application:
   ```bash
   npm run dev
   ```

2. Open your browser and navigate to the application (usually http://localhost:3021)

3. Log in to the application

4. Go to the dashboard or products page

5. Click the "Fetch Products" button

6. Wait for the fetch process to complete (this may take several minutes)

## Step 4: Verify the Results

After the fetch is complete, check the following:

1. **Total Product Count**: You should see a message indicating the total number of products fetched. This should be significantly higher than before, with approximately:
   - BH Store: ~1500+ products
   - Alkhunaizan: ~1000+ products
   - Tamkeen: ~200+ products
   - Zagzoog: ~500+ products
   - Bin Momen: ~1000+ products

2. **BH Store Regular Prices**: Navigate to the products page and filter for BH Store products. Check that:
   - All products have a regular price
   - Regular prices are higher than current prices
   - Discount percentages are displayed correctly

3. **Database Verification**: You can run the following script to verify the database state:

```bash
node verify-database.js
```

## Troubleshooting

If you encounter any issues:

1. **Fetch fails or times out**: Try fetching one store at a time by using the store-specific fetch buttons or scripts.

2. **Regular prices still incorrect**: Run the specific fix script:
   ```bash
   node fix-bhstore-regular-prices.js
   ```

3. **Missing products from specific stores**: Check the store-specific scraper files and run the individual fixer scripts:
   ```bash
   node fix-alkhunaizan-scraper.js
   node fix-tamkeen-scraper.js
   node fix-zagzoog-scraper.js
   node fix-binmomen-scraper.js
   ```

## Expected Results

After completing all steps, you should have:

1. A database with 4000+ products from all 5 stores
2. All BH Store products with correct regular prices (at least 10% higher than current prices)
3. All products with appropriate discount calculations
4. No errors or warnings in the console

If you see these results, congratulations! All the fixes have been successfully applied and tested.

## Additional Notes

- The fetch process may take 10-15 minutes to complete for all stores
- You may want to monitor the server logs during the fetch to see progress and catch any errors
- If you need to make any adjustments to the fixes, remember to run the verification script again before testing
