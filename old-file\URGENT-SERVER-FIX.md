# 🚨 URGENT: Fix Your Production Server

## **CURRENT ISSUE**
Your Excel API at `https://ir-ksa.dream4soft.co.uk/api/excel/products?limit=100` is returning **502 Bad Gateway**.

This means:
- ✅ **<PERSON>inx is running** (good)
- ❌ **Your Node.js app is DOWN** (needs immediate fix)

## **IMMEDIATE ACTION REQUIRED**

### **Option 1: If you have SSH access to your server**

1. **SSH into your production server:**
   ```bash
   ssh your-username@your-server-ip
   ```

2. **Navigate to your app directory:**
   ```bash
   cd /var/www/irksa
   # or wherever your app is deployed
   ```

3. **Check PM2 status:**
   ```bash
   pm2 status
   pm2 logs irksa --lines 20
   ```

4. **Restart your application:**
   ```bash
   pm2 restart irksa
   # or if it's not running:
   pm2 start ecosystem.config.js --env production
   pm2 save
   ```

5. **Test if it's working:**
   ```bash
   curl http://localhost:3021/health
   curl http://localhost:3021/api/excel/products?limit=5
   ```

### **Option 2: If you have a hosting control panel**

1. **Log into your hosting control panel** (cPanel, Plesk, etc.)
2. **Find "Node.js Apps" or "Application Manager"**
3. **Restart your Node.js application**
4. **Check application logs for errors**

### **Option 3: If you're using a cloud service (AWS, DigitalOcean, etc.)**

1. **Log into your cloud dashboard**
2. **Find your server/droplet/instance**
3. **Restart the server or the application service**
4. **Check the application logs**

## **QUICK DIAGNOSTIC COMMANDS**

Run these on your production server to diagnose:

```bash
# Check if anything is running on port 3021
netstat -tulpn | grep :3021

# Check all node processes
ps aux | grep node

# Check PM2 processes
pm2 list

# Check recent logs
pm2 logs irksa --lines 50

# Check system resources
free -h
df -h
```

## **COMMON FIXES**

### **Fix 1: Database Connection Issue**
```bash
# Check your .env file
cat .env | grep DATABASE

# Test database connection
npm run test:db
```

### **Fix 2: Memory Issues**
```bash
# Check memory usage
free -h

# Restart with memory limit
pm2 restart irksa --max-memory-restart 1G
```

### **Fix 3: Port Conflict**
```bash
# Kill any process using port 3021
sudo lsof -ti:3021 | xargs sudo kill -9

# Start fresh
pm2 start ecosystem.config.js --env production
```

### **Fix 4: Nuclear Option - Complete Restart**
```bash
# Stop everything
pm2 stop all
pm2 delete all

# Kill any remaining processes
pkill -f node

# Start fresh
pm2 start ecosystem.config.js --env production
pm2 save
```

## **AFTER FIXING**

Once your server is running, test these URLs:

1. **Health Check**: https://ir-ksa.dream4soft.co.uk/health
2. **Excel API**: https://ir-ksa.dream4soft.co.uk/api/excel/products?limit=100
3. **Dashboard**: https://ir-ksa.dream4soft.co.uk/login

## **PREVENTION**

Add this to your crontab to auto-restart if the server goes down:
```bash
# Edit crontab
crontab -e

# Add this line to check every 5 minutes
*/5 * * * * curl -f http://localhost:3021/health || pm2 restart irksa
```

## **NEED IMMEDIATE HELP?**

If you can't access your server or these steps don't work:

1. **Check your hosting provider's status page**
2. **Contact your hosting provider's support**
3. **Check if your domain DNS is pointing to the right server**
4. **Verify your server hasn't been suspended or has billing issues**

## **EXCEL API ENDPOINTS (Once Fixed)**

After your server is running, these will work:

- **Basic**: `https://ir-ksa.dream4soft.co.uk/api/excel/products?limit=100`
- **Paginated**: `https://ir-ksa.dream4soft.co.uk/api/excel/products?limit=50&offset=0`
- **All products**: `https://ir-ksa.dream4soft.co.uk/api/excel/products`

The Excel API code is already deployed - you just need to get your server running!
