import React, { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { API_BASE_URL } from "@/lib/constants";
import { Sidebar } from "@/components/ui/sidebar";
import { TopNav } from "@/components/ui/top-nav";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { AlertCircle, Bell, Clock, Database, Mail, Plus, RefreshCw, Trash2, Settings as SettingsIcon, Activity, Key, Eye, Shield } from "lucide-react";
import { DataTable } from "@/components/ui/data-table";
import { ColumnDef } from "@tanstack/react-table";
import BearerTokenManagement from "@/components/BearerTokenManagement";
import { format, formatDistanceToNow, parseISO } from "date-fns";
import { DEFAULT_EMAIL_TEMPLATE } from "@/lib/constants";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { DisplaySettingsTab } from "@/components/display-settings-tab";

// Schemas
const createScheduleSchema = z.object({
  type: z.enum(["fetch", "email"]),
  cronExpression: z.string()
    .min(1, "Cron expression is required")
    .refine((val) => {
      // Simple validation for cron expression format
      const parts = val.split(' ');
      if (parts.length !== 5) return false;

      // Validate minute (0-59 or *)
      if (parts[0] !== '*' && !/^([0-9]|[1-5][0-9])$/.test(parts[0])) return false;

      // Validate hour (0-23 or *)
      if (parts[1] !== '*' && !/^([0-9]|1[0-9]|2[0-3])$/.test(parts[1])) return false;

      // Validate day of month (1-31 or *)
      if (parts[2] !== '*' && !/^([1-9]|[12][0-9]|3[01])$/.test(parts[2])) return false;

      // Validate month (1-12 or *)
      if (parts[3] !== '*' && !/^([1-9]|1[0-2])$/.test(parts[3])) return false;

      // Validate day of week (0-6 or *)
      if (parts[4] !== '*' && !/^[0-6]$/.test(parts[4])) return false;

      return true;
    }, "Invalid cron expression format. Use standard 5-part format (e.g., '0 2 * * *' for 2 AM daily)"),
  enabled: z.boolean().default(true),
  settings: z.record(z.any()).optional(),

  // New fields for time picker UI (not sent to server)
  scheduleType: z.enum(["hourly", "daily", "weekly", "monthly", "custom"]).default("daily").optional(),
  hour: z.string().optional(),
  minute: z.string().optional(),
  dayOfWeek: z.string().optional(),
  dayOfMonth: z.string().optional(),
  hourInterval: z.string().optional(), // For hourly schedules
});

const updateScheduleSchema = createScheduleSchema.partial();

const emailTemplateSchema = z.object({
  name: z.string().min(1, "Name is required"),
  subject: z.string().min(1, "Subject is required"),
  body: z.string().min(1, "Body is required"),
});

const emailRecipientSchema = z.object({
  email: z.string().email("Invalid email address"),
  name: z.string().optional(),
  type: z.enum(["to", "cc", "bcc"]).default("to"),
  receiveScheduledReports: z.boolean().default(true),
  receiveChangeNotifications: z.boolean().default(true),
  receiveNewProductNotifications: z.boolean().default(true),
});

const changeNotificationSettingsSchema = z.object({
  enablePriceChangeNotifications: z.boolean().default(true),
  enableStockChangeNotifications: z.boolean().default(true),
  enableNewProductNotifications: z.boolean().default(true),
  minimumPriceChangePercentage: z.string().default("0.00"),
  emailSubject: z.string().default("Product Changes Summary"),
});

export default function Settings() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();
  const isAdmin = user?.role === 'admin';

  // State for active tab and forms
  const [activeTab, setActiveTab] = useState("scheduling");
  const [editingSchedule, setEditingSchedule] = useState<any>(null);
  const [editingTemplate, setEditingTemplate] = useState<any>(null);
  const [isAddingRecipient, setIsAddingRecipient] = useState(false);
  const [testEmailAddress, setTestEmailAddress] = useState("");
  const [clearDatabaseDialogOpen, setClearDatabaseDialogOpen] = useState(false);
  const [clearCategoriesDialogOpen, setClearCategoriesDialogOpen] = useState(false);

  // Interface settings state
  const [showAllProductsTable, setShowAllProductsTable] = useState(true);
  const [showWishlists, setShowWishlists] = useState(true);
  const [showSavedSearches, setShowSavedSearches] = useState(true);
  const [showRecommendations, setShowRecommendations] = useState(true);
  const [showReportBuilder, setShowReportBuilder] = useState(true);

  // User Activity Logs state
  const [activityLogsPage, setActivityLogsPage] = useState(1);
  const [activityLogsLimit, setActivityLogsLimit] = useState(50);
  const [selectedActivityType, setSelectedActivityType] = useState<string>('all');
  const [selectedUserId, setSelectedUserId] = useState<string>('all');

  // JWT Token state
  const [alkhunaizanToken, setAlkhunaizanToken] = useState("");
  const [isUpdatingToken, setIsUpdatingToken] = useState(false);

  // Fetch interface settings
  const { data: interfaceSettings, refetch: refetchInterfaceSettings } = useQuery({
    queryKey: ['settings', 'interface'],
    queryFn: async () => {
      const [
        allProductsTableResponse,
        wishlistsResponse,
        savedSearchesResponse,
        recommendationsResponse,
        reportBuilderResponse
      ] = await Promise.all([
        apiRequest('/api/settings/show_all_products_table'),
        apiRequest('/api/settings/show_wishlists'),
        apiRequest('/api/settings/show_saved_searches'),
        apiRequest('/api/settings/show_recommendations'),
        apiRequest('/api/settings/show_report_builder')
      ]);

      return {
        showAllProductsTable: allProductsTableResponse,
        showWishlists: wishlistsResponse,
        showSavedSearches: savedSearchesResponse,
        showRecommendations: recommendationsResponse,
        showReportBuilder: reportBuilderResponse
      };
    },
  });

  // Fetch wishlists setting separately
  const { data: wishlistsSettings, refetch: refetchWishlistsSettings } = useQuery({
    queryKey: ['settings', 'wishlists'],
    queryFn: async () => {
      const response = await apiRequest('/api/settings/show_wishlists');
      return response;
    },
  });

  // Update local state when data changes
  React.useEffect(() => {
    if (interfaceSettings?.showAllProductsTable?.value !== undefined && interfaceSettings?.showAllProductsTable?.value !== null) {
      setShowAllProductsTable(interfaceSettings.showAllProductsTable.value);
    } else if (interfaceSettings?.showAllProductsTable?.value === null) {
      setShowAllProductsTable(true); // Default to true for null values
    }

    if (interfaceSettings?.showWishlists?.value !== undefined && interfaceSettings?.showWishlists?.value !== null) {
      setShowWishlists(interfaceSettings.showWishlists.value);
    } else if (interfaceSettings?.showWishlists?.value === null) {
      setShowWishlists(true); // Default to true for null values
    }

    if (interfaceSettings?.showSavedSearches?.value !== undefined && interfaceSettings?.showSavedSearches?.value !== null) {
      setShowSavedSearches(interfaceSettings.showSavedSearches.value);
    } else if (interfaceSettings?.showSavedSearches?.value === null) {
      setShowSavedSearches(true); // Default to true for null values
    }

    if (interfaceSettings?.showRecommendations?.value !== undefined && interfaceSettings?.showRecommendations?.value !== null) {
      setShowRecommendations(interfaceSettings.showRecommendations.value);
    } else if (interfaceSettings?.showRecommendations?.value === null) {
      setShowRecommendations(true); // Default to true for null values
    }

    if (interfaceSettings?.showReportBuilder?.value !== undefined && interfaceSettings?.showReportBuilder?.value !== null) {
      setShowReportBuilder(interfaceSettings.showReportBuilder.value);
    } else if (interfaceSettings?.showReportBuilder?.value === null) {
      setShowReportBuilder(true); // Default to true for null values
    }
  }, [interfaceSettings]);

  React.useEffect(() => {
    if (wishlistsSettings?.value !== undefined) {
      setShowWishlists(wishlistsSettings.value);
    }
  }, [wishlistsSettings]);

  // Update interface settings mutation
  const updateInterfaceSettings = useMutation({
    mutationFn: async (settings: {
      showAllProductsTable?: boolean;
      showWishlists?: boolean;
      showSavedSearches?: boolean;
      showRecommendations?: boolean;
      showReportBuilder?: boolean;
    }) => {
      const promises = [];

      if (settings.showAllProductsTable !== undefined) {
        promises.push(
          apiRequest('/api/settings/show_all_products_table', {
            method: 'POST',
            body: JSON.stringify({ value: settings.showAllProductsTable }),
          })
        );
      }

      if (settings.showWishlists !== undefined) {
        promises.push(
          apiRequest('/api/settings/show_wishlists', {
            method: 'POST',
            body: JSON.stringify({ value: settings.showWishlists }),
          })
        );
      }

      if (settings.showSavedSearches !== undefined) {
        promises.push(
          apiRequest('/api/settings/show_saved_searches', {
            method: 'POST',
            body: JSON.stringify({ value: settings.showSavedSearches }),
          })
        );
      }

      if (settings.showRecommendations !== undefined) {
        promises.push(
          apiRequest('/api/settings/show_recommendations', {
            method: 'POST',
            body: JSON.stringify({ value: settings.showRecommendations }),
          })
        );
      }

      if (settings.showReportBuilder !== undefined) {
        promises.push(
          apiRequest('/api/settings/show_report_builder', {
            method: 'POST',
            body: JSON.stringify({ value: settings.showReportBuilder }),
          })
        );
      }

      await Promise.all(promises);
    },
    onSuccess: () => {
      toast({
        title: "Settings Updated",
        description: "Interface settings have been saved successfully.",
      });
      refetchInterfaceSettings();
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update interface settings.",
        variant: "destructive",
      });
    },
  });

  // Schedule form
  const scheduleForm = useForm<z.infer<typeof createScheduleSchema>>({
    resolver: zodResolver(createScheduleSchema),
    defaultValues: {
      type: "fetch",
      cronExpression: "0 0 * * *", // Daily at midnight
      enabled: true,
      settings: {},
      scheduleType: "daily",
      hour: "0",
      minute: "0",
      hourInterval: "1",
    },
  });

  // Update form resolver when editing state changes
  React.useEffect(() => {
    if (editingSchedule) {
      // When editing, use the more lenient updateScheduleSchema
      scheduleForm.clearErrors();
    }
  }, [editingSchedule, scheduleForm]);

  // Helper function to convert UI selections to cron expression
  const generateCronExpression = (data: any) => {
    const { scheduleType, minute, hour, dayOfWeek, dayOfMonth, hourInterval } = data;

    // Default values
    const min = minute || "0";
    const hr = hour || "0";

    // Validate minute and hour
    const validatedMin = /^([0-9]|[1-5][0-9])$/.test(min) ? min : "0";
    const validatedHr = /^([0-9]|1[0-9]|2[0-3])$/.test(hr) ? hr : "0";

    switch (scheduleType) {
      case "hourly":
        // For hourly schedules, use minute and hour interval
        const interval = hourInterval || "1";
        const validatedInterval = /^([1-9]|1[0-2])$/.test(interval) ? interval : "1";
        // For interval of 1, use * instead of */1
        const hourPart = validatedInterval === "1" ? "*" : `*/${validatedInterval}`;
        return `${validatedMin} ${hourPart} * * *`;
      case "daily":
        return `${validatedMin} ${validatedHr} * * *`;
      case "weekly":
        const validDayOfWeek = /^[0-6]$/.test(dayOfWeek || "") ? dayOfWeek : "0";
        return `${validatedMin} ${validatedHr} * * ${validDayOfWeek}`;
      case "monthly":
        const validDayOfMonth = /^([1-9]|[12][0-9]|3[01])$/.test(dayOfMonth || "") ? dayOfMonth : "1";
        return `${validatedMin} ${validatedHr} ${validDayOfMonth} * *`;
      case "custom":
        // For custom, validate the format
        try {
          const parts = data.cronExpression.split(' ');
          if (parts.length !== 5) {
            return "0 0 * * *"; // Default to midnight if invalid
          }
          return data.cronExpression;
        } catch (e) {
          return "0 0 * * *"; // Default to midnight if any error
        }
      default:
        return `${validatedMin} ${validatedHr} * * *`; // Default to daily
    }
  };

  // Email template form
  const templateForm = useForm<z.infer<typeof emailTemplateSchema>>({
    resolver: zodResolver(emailTemplateSchema),
    defaultValues: {
      name: "",
      subject: "Product Monitoring Report",
      body: DEFAULT_EMAIL_TEMPLATE,
    },
  });

  // Email recipient form
  const recipientForm = useForm<z.infer<typeof emailRecipientSchema>>({
    resolver: zodResolver(emailRecipientSchema),
    defaultValues: {
      email: "",
      name: "",
      type: "to",
      receiveScheduledReports: true,
      receiveChangeNotifications: true,
      receiveNewProductNotifications: true,
    },
  });

  // Change notification settings form
  const changeNotificationForm = useForm<z.infer<typeof changeNotificationSettingsSchema>>({
    resolver: zodResolver(changeNotificationSettingsSchema),
    defaultValues: {
      enablePriceChangeNotifications: true,
      enableStockChangeNotifications: true,
      enableNewProductNotifications: true,
      minimumPriceChangePercentage: "0.00",
      emailSubject: "Product Changes Summary",
    },
  });

  // Fetch schedules
  const { data: schedules, isLoading: isLoadingSchedules } = useQuery({
    queryKey: ["/api/schedules"],
    enabled: isAdmin,
  });

  // Fetch email templates
  const { data: emailTemplates, isLoading: isLoadingTemplates } = useQuery({
    queryKey: ["/api/email/templates"],
    enabled: isAdmin,
  });

  // Fetch email recipients
  const { data: emailRecipients, isLoading: isLoadingRecipients } = useQuery({
    queryKey: ["/api/email/recipients"],
    enabled: isAdmin,
  });

  // Fetch change notification settings
  const { data: changeNotificationSettings, isLoading: isLoadingChangeSettings } = useQuery({
    queryKey: ["/api/change-notifications/settings"],
    enabled: isAdmin,
  });

  // Fetch user activity logs
  const { data: userActivityLogs, isLoading: isLoadingActivityLogs } = useQuery({
    queryKey: ["/api/user-activity-logs", {
      page: activityLogsPage,
      limit: activityLogsLimit,
      activityType: selectedActivityType === 'all' ? undefined : selectedActivityType,
      userId: selectedUserId === 'all' ? undefined : selectedUserId
    }],
    enabled: isAdmin && activeTab === 'activity-logs',
    queryFn: async () => {
      const params = new URLSearchParams();
      params.append('page', activityLogsPage.toString());
      params.append('limit', activityLogsLimit.toString());

      if (selectedActivityType !== 'all') {
        params.append('activityType', selectedActivityType);
      }

      if (selectedUserId !== 'all') {
        params.append('userId', selectedUserId);
      }

      return apiRequest(`/api/user-activity-logs?${params.toString()}`);
    },
  });

  // Fetch all users for the activity logs filter
  const { data: allUsers } = useQuery({
    queryKey: ["/api/users"],
    enabled: isAdmin && activeTab === 'activity-logs',
    queryFn: async () => {
      return apiRequest("/api/users");
    },
  });

  // Fetch Alkhunaizan JWT token
  const { data: alkhunaizanTokenData, refetch: refetchAlkhunaizanToken } = useQuery({
    queryKey: ["/api/settings/jwt/alkhunaizan"],
    enabled: isAdmin && activeTab === 'jwt-tokens',
    queryFn: async () => {
      return apiRequest("/api/settings/jwt/alkhunaizan");
    },
  });

  // Update local token state when data changes
  React.useEffect(() => {
    if (alkhunaizanTokenData?.token) {
      setAlkhunaizanToken(alkhunaizanTokenData.token);
    }
  }, [alkhunaizanTokenData]);

  // Update change notification form when data loads
  React.useEffect(() => {
    if (changeNotificationSettings) {
      changeNotificationForm.reset(changeNotificationSettings);
    }
  }, [changeNotificationSettings, changeNotificationForm]);

  // Create schedule mutation
  const createSchedule = useMutation({
    mutationFn: async (data: z.infer<typeof createScheduleSchema>) => {
      console.log("Creating schedule:", data);
      try {
        // Use direct fetch API instead of apiRequest
        const response = await fetch(`${API_BASE_URL}/api/schedules`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify(data),
          credentials: 'include'
        });

        // Check if the response is JSON
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          return await response.json();
        } else {
          // If not JSON, throw an error with the text content
          const textContent = await response.text();
          throw new Error(`Server returned non-JSON response: ${textContent.substring(0, 200)}...`);
        }
      } catch (error) {
        console.error("Error in createSchedule mutation:", error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/schedules"] });
      scheduleForm.reset();
      toast({
        title: "Schedule Created",
        description: "The schedule has been created successfully",
        variant: "success",
      });
    },
    onError: (error: any) => {
      console.error("Create schedule error:", error);

      // Extract a more user-friendly error message
      let errorMessage = String(error);
      if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Error Creating Schedule",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  // Update schedule mutation
  const updateSchedule = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: z.infer<typeof updateScheduleSchema> }) => {
      console.log("Updating schedule:", id, data);
      try {
        // Use direct fetch API instead of apiRequest
        const response = await fetch(`/api/schedules/${id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify(data),
          credentials: 'include'
        });

        console.log("Update response status:", response.status);
        console.log("Update response headers:", response.headers);

        // Check if response is ok
        if (!response.ok) {
          const errorText = await response.text();
          console.error("Update failed with status:", response.status, "Error:", errorText);
          throw new Error(`HTTP ${response.status}: ${errorText}`);
        }

        // Check if the response is JSON
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const result = await response.json();
          console.log("Update successful, result:", result);
          return result;
        } else {
          // If not JSON, throw an error with the text content
          const textContent = await response.text();
          console.error("Non-JSON response:", textContent);
          throw new Error(`Server returned non-JSON response: ${textContent.substring(0, 200)}...`);
        }
      } catch (error) {
        console.error("Error in updateSchedule mutation:", error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/schedules"] });
      setEditingSchedule(null);
      scheduleForm.reset();
      toast({
        title: "Schedule Updated",
        description: "The schedule has been updated successfully",
        variant: "success",
      });
    },
    onError: (error: any) => {
      console.error("Update schedule error:", error);

      // Extract a more user-friendly error message
      let errorMessage = String(error);
      if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Error Updating Schedule",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  // Run schedule now mutation
  const runScheduleNow = useMutation({
    mutationFn: async (id: number) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/schedules/${id}/run-now`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          credentials: 'include'
        });

        // Check if the response is JSON
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          return await response.json();
        } else {
          // If not JSON, throw an error with the text content
          const textContent = await response.text();
          throw new Error(`Server returned non-JSON response: ${textContent.substring(0, 100)}...`);
        }
      } catch (error) {
        console.error("Error running schedule:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      toast({
        title: "Schedule Executed",
        description: "The scheduled task was executed successfully",
        variant: "success",
      });

      // Refresh data
      queryClient.invalidateQueries({ queryKey: ['/api/schedules'] });
      queryClient.invalidateQueries({ queryKey: ['/api/dashboard/stats'] });
      queryClient.invalidateQueries({ queryKey: ['/api/products'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to execute schedule: ${error}`,
        variant: "destructive",
      });
    },
  });

  // Create email template mutation
  const createEmailTemplate = useMutation({
    mutationFn: async (data: z.infer<typeof emailTemplateSchema>) => {
      return apiRequest("/api/email/templates", {
        method: "POST",
        body: data
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/email/templates"] });
      templateForm.reset();
      toast({
        title: "Template Created",
        description: "The email template has been created successfully",
        variant: "success",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to create template: ${error}`,
        variant: "destructive",
      });
    },
  });

  // Update email template mutation
  const updateEmailTemplate = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: z.infer<typeof emailTemplateSchema> }) => {
      return apiRequest(`/api/email/templates/${id}`, {
        method: "PUT",
        body: data
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/email/templates"] });
      setEditingTemplate(null);
      toast({
        title: "Template Updated",
        description: "The email template has been updated successfully",
        variant: "success",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to update template: ${error}`,
        variant: "destructive",
      });
    },
  });

  // Create email recipient mutation
  const createEmailRecipient = useMutation({
    mutationFn: async (data: z.infer<typeof emailRecipientSchema>) => {
      console.log("Creating email recipient:", data);
      try {
        // Use direct fetch API instead of apiRequest for more control
        const response = await fetch(`${API_BASE_URL}/api/email/recipients`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache'
          },
          body: JSON.stringify(data),
          credentials: 'include'
        });

        console.log(`Response status: ${response.status} ${response.statusText}`);
        console.log(`Response headers:`, Object.fromEntries([...response.headers.entries()]));

        // Check if the response is JSON
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          return await response.json();
        } else {
          // If not JSON, throw an error with the text content
          const textContent = await response.text();
          console.error("Non-JSON response:", textContent.substring(0, 500));

          if (textContent.includes('<!DOCTYPE html>') || textContent.includes('<html>')) {
            throw new Error(`Server returned HTML instead of JSON. Status: ${response.status}. This might indicate a server-side error or authentication issue.`);
          } else {
            throw new Error(`Server returned non-JSON response: ${textContent.substring(0, 200)}...`);
          }
        }
      } catch (error) {
        console.error("Error in createEmailRecipient mutation:", error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/email/recipients"] });
      recipientForm.reset();
      setIsAddingRecipient(false);
      toast({
        title: "Recipient Added",
        description: "The email recipient has been added successfully",
        variant: "success",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to add recipient: ${error}`,
        variant: "destructive",
      });
    },
  });

  // Delete email recipient mutation
  const deleteEmailRecipient = useMutation({
    mutationFn: async (id: number) => {
      console.log("Deleting email recipient:", id);
      try {
        // Use direct fetch API instead of apiRequest for more control
        const response = await fetch(`${API_BASE_URL}/api/email/recipients/${id}`, {
          method: 'DELETE',
          headers: {
            'Accept': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache'
          },
          credentials: 'include'
        });

        console.log(`Response status: ${response.status} ${response.statusText}`);
        console.log(`Response headers:`, Object.fromEntries([...response.headers.entries()]));

        // Check if the response is JSON
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          return await response.json();
        } else {
          // If not JSON, throw an error with the text content
          const textContent = await response.text();
          console.error("Non-JSON response:", textContent.substring(0, 500));

          if (textContent.includes('<!DOCTYPE html>') || textContent.includes('<html>')) {
            throw new Error(`Server returned HTML instead of JSON. Status: ${response.status}. This might indicate a server-side error or authentication issue.`);
          } else {
            throw new Error(`Server returned non-JSON response: ${textContent.substring(0, 200)}...`);
          }
        }
      } catch (error) {
        console.error("Error in deleteEmailRecipient mutation:", error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/email/recipients"] });
      toast({
        title: "Recipient Deleted",
        description: "The email recipient has been deleted successfully",
        variant: "success",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to delete recipient: ${error}`,
        variant: "destructive",
      });
    },
  });

  // State for selected store
  const [selectedStore, setSelectedStore] = useState<string>("all");

  // Get stores
  const { data: stores } = useQuery({
    queryKey: ['/api/stores'],
    queryFn: () => apiRequest('/api/stores')
  });

  // Mutation for fetching products
  const { mutate: fetchProducts, isPending: isFetching } = useMutation({
    mutationFn: async () => {
      console.log("Starting product fetch with direct fetch API");
      console.log("Selected store:", selectedStore || "all stores");

      try {
        // Use direct fetch API instead of apiRequest
        const response = await fetch(`${API_BASE_URL}/api/admin/fetch-products`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify({
            storeSlug: selectedStore === "all" ? undefined : selectedStore
          }),
          credentials: 'include'
        });

        console.log(`Fetch products response status: ${response.status}`);
        console.log(`Fetch products response headers:`, Object.fromEntries([...response.headers.entries()]));

        // Check if the response is JSON
        const contentType = response.headers.get('content-type');
        console.log(`Fetch products response content-type:`, contentType);

        if (contentType && contentType.includes('application/json')) {
          const data = await response.json();
          console.log("Fetch products response data:", data);
          return data;
        } else {
          // If not JSON, throw an error with the text content
          const textContent = await response.text();
          console.error("Non-JSON response from fetch products:", textContent.substring(0, 500));
          throw new Error(`Server returned non-JSON response: ${textContent.substring(0, 100)}...`);
        }
      } catch (error) {
        console.error("Error fetching products:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      const storeMessage = selectedStore && selectedStore !== "all"
        ? `Fetched ${data.count || 0} products from ${selectedStore} store.`
        : `Fetched ${data.count || 0} products from all stores.`;

      toast({
        title: "Success!",
        description: storeMessage,
      });
      // Refresh dashboard data
      queryClient.invalidateQueries({ queryKey: ['/api/dashboard/stats'] });
      queryClient.invalidateQueries({ queryKey: ['/api/dashboard/recent-changes'] });

      // Invalidate ALL product-related queries - FIXED to include product catalog
      queryClient.invalidateQueries({ queryKey: ['/api/products'] });
      queryClient.invalidateQueries({ queryKey: ['products'] }); // For product-table page
      queryClient.invalidateQueries({ queryKey: ['products-table'] }); // For product-table page
      queryClient.invalidateQueries({ queryKey: ['product-catalog'] }); // For product-catalog page

      // Also invalidate filter data that might have changed
      queryClient.invalidateQueries({ queryKey: ['/api/stores'] });
      queryClient.invalidateQueries({ queryKey: ['/api/categories'] });
      queryClient.invalidateQueries({ queryKey: ['/api/brands'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to fetch products. Please try again.",
        variant: "destructive",
      });
      console.error("Fetch error:", error);
    }
  });

  // Mutation for sending test email
  const { mutate: sendTestEmail, isPending: isSendingTestEmail } = useMutation({
    mutationFn: async (email: string) => {
      try {
        // Use direct fetch API instead of apiRequest
        const response = await fetch(`${API_BASE_URL}/api/email/test`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache'
          },
          body: JSON.stringify({ email }),
          credentials: 'include'
        });

        // Check if the response is JSON
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          return await response.json();
        } else {
          // If not JSON, throw an error with the text content
          const textContent = await response.text();
          throw new Error(`Server returned non-JSON response: ${textContent.substring(0, 200)}...`);
        }
      } catch (error) {
        console.error("Error sending test email:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      toast({
        title: "Test Email Sent!",
        description: `Email sent successfully to ${data.details.recipient}`,
        variant: "success",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to send test email: ${error}`,
        variant: "destructive",
      });
      console.error("Test email error:", error);
    }
  });

  // Mutation for sending email now with latest Excel sheet
  const { mutate: sendEmailNow, isPending: isSendingEmailNow } = useMutation({
    mutationFn: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/email/send-now`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          credentials: 'include'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to send email');
        }

        return response.json();
      } catch (error) {
        console.error("Error sending email now:", error);
        throw error;
      }
    },
    onSuccess: () => {
      toast({
        title: "Email Sent Successfully!",
        description: "Product report with Excel sheet has been sent to all configured recipients",
        variant: "success",
      });
    },
    onError: (error: any) => {
      console.error("Send email now error:", error);
      toast({
        title: "Error Sending Email",
        description: error.message || "Failed to send email with Excel sheet",
        variant: "destructive",
      });
    },
  });

  // Mutation for clearing the database
  const { mutate: clearDatabase, isPending: isClearingDatabase } = useMutation({
    mutationFn: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/admin/clear-database`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          credentials: 'include'
        });

        // Check if the response is JSON
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          return await response.json();
        } else {
          // If not JSON, throw an error with the text content
          const textContent = await response.text();
          throw new Error(`Server returned non-JSON response: ${textContent.substring(0, 200)}...`);
        }
      } catch (error) {
        console.error("Error clearing database:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      toast({
        title: "Database Cleared",
        description: "The database has been cleared successfully. The application will now start with fresh data.",
        variant: "success",
      });
      setClearDatabaseDialogOpen(false);

      // Refresh all data - FIXED to include all product queries
      queryClient.invalidateQueries({ queryKey: ['/api/dashboard/stats'] });
      queryClient.invalidateQueries({ queryKey: ['/api/dashboard/recent-changes'] });
      queryClient.invalidateQueries({ queryKey: ['/api/products'] });
      queryClient.invalidateQueries({ queryKey: ['products'] }); // For product-table page
      queryClient.invalidateQueries({ queryKey: ['products-table'] }); // For product-table page
      queryClient.invalidateQueries({ queryKey: ['product-catalog'] }); // For product-catalog page
      queryClient.invalidateQueries({ queryKey: ['/api/schedules'] });
      queryClient.invalidateQueries({ queryKey: ['/api/email/templates'] });
      queryClient.invalidateQueries({ queryKey: ['/api/email/recipients'] });
      queryClient.invalidateQueries({ queryKey: ['/api/stores'] });
      queryClient.invalidateQueries({ queryKey: ['/api/categories'] });
      queryClient.invalidateQueries({ queryKey: ['/api/brands'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to clear database: ${error}`,
        variant: "destructive",
      });
      setClearDatabaseDialogOpen(false);
    }
  });

  // Mutation for clearing categories
  const { mutate: clearCategories, isPending: isClearingCategories } = useMutation({
    mutationFn: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/admin/clear-categories`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          credentials: 'include'
        });

        // Check if the response is JSON
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          return await response.json();
        } else {
          // If not JSON, throw an error with the text content
          const textContent = await response.text();
          throw new Error(`Server returned non-JSON response: ${textContent.substring(0, 200)}...`);
        }
      } catch (error) {
        console.error("Error clearing categories:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      toast({
        title: "Categories Cleared",
        description: `Successfully cleared ${data.count} categories from the database.`,
        variant: "success",
      });
      setClearCategoriesDialogOpen(false);

      // Refresh relevant data
      queryClient.invalidateQueries({ queryKey: ['/api/categories'] });
      queryClient.invalidateQueries({ queryKey: ['/api/dashboard/stats'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to clear categories: ${error}`,
        variant: "destructive",
      });
      setClearCategoriesDialogOpen(false);
    }
  });

  // Update change notification settings mutation
  const updateChangeNotificationSettings = useMutation({
    mutationFn: async (data: z.infer<typeof changeNotificationSettingsSchema>) => {
      return apiRequest("/api/change-notifications/settings", {
        method: "POST",
        body: data
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/change-notifications/settings"] });
      toast({
        title: "Settings Updated",
        description: "Change notification settings have been updated successfully",
        variant: "success",
      });
    },
    onError: (error) => {
      console.error("Error updating change notification settings:", error);
      toast({
        title: "Error",
        description: `Failed to update settings: ${error}`,
        variant: "destructive",
      });
    },
  });

  // Send change notifications mutation
  const sendChangeNotifications = useMutation({
    mutationFn: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/change-notifications/send`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          credentials: 'include'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to send notifications');
        }

        return response.json();
      } catch (error) {
        console.error("Error sending change notifications:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      toast({
        title: "Notifications Sent",
        description: data?.message || "Change notification emails have been sent successfully",
      });
    },
    onError: (error: any) => {
      console.error("Error sending change notifications:", error);
      toast({
        title: "Error",
        description: `Failed to send notifications: ${error?.message || error}`,
        variant: "destructive",
      });
    },
  });

  // Create test changes mutation
  const createTestChanges = useMutation({
    mutationFn: async () => {
      return apiRequest("/api/change-notifications/create-test-changes", {
        method: "POST",
      });
    },
    onSuccess: (data) => {
      toast({
        title: "Test Changes Created",
        description: data.message || "Test changes created successfully",
        variant: "success",
      });
    },
    onError: (error) => {
      console.error("Error creating test changes:", error);
      toast({
        title: "Error",
        description: `Failed to create test changes: ${error}`,
        variant: "destructive",
      });
    },
  });

  // Run migration mutation
  const runMigration = useMutation({
    mutationFn: async () => {
      return apiRequest("/api/database/migrate", {
        method: "POST",
      });
    },
    onSuccess: () => {
      toast({
        title: "Migration Completed",
        description: "Database migration completed successfully. Please refresh the page.",
        variant: "success",
      });
      // Refresh the page after a short delay
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    },
    onError: (error) => {
      console.error("Error running migration:", error);
      toast({
        title: "Migration Failed",
        description: `Failed to run migration: ${error}`,
        variant: "destructive",
      });
    },
  });

  // Cleanup old activity logs mutation
  const cleanupActivityLogs = useMutation({
    mutationFn: async (daysToKeep: number = 30) => {
      return apiRequest(`/api/user-activity-logs/cleanup?daysToKeep=${daysToKeep}`, {
        method: "DELETE",
      });
    },
    onSuccess: (data: any) => {
      queryClient.invalidateQueries({ queryKey: ["/api/user-activity-logs"] });
      toast({
        title: "Cleanup Completed",
        description: `Successfully deleted ${data.deletedCount} old activity log entries.`,
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to cleanup activity logs: ${error}`,
        variant: "destructive",
      });
    },
  });

  // Cleanup bad notifications mutation
  const cleanupBadNotifications = useMutation({
    mutationFn: async () => {
      return apiRequest("/api/change-notifications/cleanup", {
        method: "POST",
      });
    },
    onSuccess: (data: any) => {
      toast({
        title: "Cleanup Completed",
        description: `Successfully cleaned up ${data.deletedCount} bad notifications.`,
        variant: "success",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to cleanup bad notifications: ${error}`,
        variant: "destructive",
      });
    },
  });

  // Update Alkhunaizan JWT token mutation
  const updateAlkhunaizanToken = useMutation({
    mutationFn: async (token: string) => {
      setIsUpdatingToken(true);
      try {
        const response = await fetch(`${API_BASE_URL}/api/settings/jwt/alkhunaizan`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify({ token }),
          credentials: 'include'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to update token');
        }

        return response.json();
      } catch (error) {
        console.error("Error updating JWT token:", error);
        throw error;
      } finally {
        setIsUpdatingToken(false);
      }
    },
    onSuccess: (data) => {
      refetchAlkhunaizanToken();
      toast({
        title: "Token Updated Successfully!",
        description: data.message || "Alkhunaizan JWT token has been updated and validated.",
        variant: "success",
      });
    },
    onError: (error: any) => {
      console.error("Update token error:", error);
      toast({
        title: "Error Updating Token",
        description: error.message || "Failed to update JWT token. Please check the token format.",
        variant: "destructive",
      });
    },
  });

  // Handle change notification form submission
  const onSubmitChangeNotifications = (data: z.infer<typeof changeNotificationSettingsSchema>) => {
    updateChangeNotificationSettings.mutate(data);
  };

  // Handle schedule form submission
  const onSubmitSchedule = (data: z.infer<typeof createScheduleSchema>) => {
    console.log("Form submission data:", data);
    console.log("Editing schedule:", editingSchedule);

    // Generate the cron expression from the UI selections if not using custom mode
    if (data.scheduleType !== "custom") {
      data.cronExpression = generateCronExpression(data);
    }

    // Create a clean data object without the UI-specific fields
    const cleanData = {
      type: data.type,
      cronExpression: data.cronExpression,
      enabled: data.enabled,
      settings: data.settings || {},
    };

    console.log("Submitting schedule with cron expression:", data.cronExpression);
    console.log("Clean data being sent:", cleanData);

    if (editingSchedule) {
      console.log("Updating schedule with ID:", editingSchedule.id);
      updateSchedule.mutate({ id: editingSchedule.id, data: cleanData });
    } else {
      console.log("Creating new schedule");
      createSchedule.mutate(cleanData);
    }
  };

  // Handle template form submission
  const onSubmitTemplate = (data: z.infer<typeof emailTemplateSchema>) => {
    if (editingTemplate) {
      updateEmailTemplate.mutate({ id: editingTemplate.id, data });
    } else {
      createEmailTemplate.mutate(data);
    }
  };

  // Handle recipient form submission
  const onSubmitRecipient = (data: z.infer<typeof emailRecipientSchema>) => {
    createEmailRecipient.mutate(data);
  };

  // Handle JWT token update
  const handleUpdateToken = () => {
    if (!alkhunaizanToken.trim()) {
      toast({
        title: "Error",
        description: "Please enter a JWT token",
        variant: "destructive",
      });
      return;
    }

    updateAlkhunaizanToken.mutate(alkhunaizanToken.trim());
  };

  // Parse cron expression into UI fields
  const parseCronExpression = (cronExpression: string) => {
    // Default values
    let scheduleType = "custom";
    let minute = "0";
    let hour = "0";
    let dayOfWeek = "0";
    let dayOfMonth = "1";
    let hourInterval = "1";

    // Try to parse the cron expression
    const parts = cronExpression.split(" ");
    if (parts.length === 5) {
      minute = parts[0];
      hour = parts[1];

      // Check for hourly pattern: minute * * * * or minute */interval * * *
      if (parts[2] === "*" && parts[3] === "*" && parts[4] === "*") {
        if (parts[1] === "*") {
          scheduleType = "hourly";
          hourInterval = "1";
        } else if (parts[1].startsWith("*/")) {
          scheduleType = "hourly";
          hourInterval = parts[1].substring(2);
        } else {
          // Check for daily pattern: minute hour * * *
          scheduleType = "daily";
        }
      }
      // Check for weekly pattern: minute hour * * dayOfWeek
      else if (parts[2] === "*" && parts[3] === "*" && /^[0-6]$/.test(parts[4])) {
        scheduleType = "weekly";
        dayOfWeek = parts[4];
      }
      // Check for monthly pattern: minute hour dayOfMonth * *
      else if (/^([1-9]|[12][0-9]|3[01])$/.test(parts[2]) && parts[3] === "*" && parts[4] === "*") {
        scheduleType = "monthly";
        dayOfMonth = parts[2];
      }
    }

    return { scheduleType, minute, hour, dayOfWeek, dayOfMonth, hourInterval };
  };

  // Edit schedule
  const handleEditSchedule = (schedule: any) => {
    setEditingSchedule(schedule);

    // Parse the cron expression
    const { scheduleType, minute, hour, dayOfWeek, dayOfMonth, hourInterval } = parseCronExpression(schedule.cronExpression);

    scheduleForm.reset({
      type: schedule.type,
      cronExpression: schedule.cronExpression,
      enabled: schedule.enabled,
      settings: schedule.settings || {},
      scheduleType,
      minute,
      hour,
      dayOfWeek,
      dayOfMonth,
      hourInterval,
    });
  };

  // Edit template
  const handleEditTemplate = (template: any) => {
    setEditingTemplate(template);
    templateForm.reset({
      name: template.name,
      subject: template.subject,
      body: template.body,
    });
  };

  // Cancel editing
  const handleCancelEdit = () => {
    if (editingSchedule) {
      setEditingSchedule(null);
      scheduleForm.reset({
        type: "fetch",
        cronExpression: "0 0 * * *", // Daily at midnight
        enabled: true,
        settings: {},
        scheduleType: "daily",
        hour: "0",
        minute: "0",
      });
    }
    if (editingTemplate) {
      setEditingTemplate(null);
      templateForm.reset({
        name: "",
        subject: "Product Monitoring Report",
        body: DEFAULT_EMAIL_TEMPLATE,
      });
    }
  };

  // Schedule columns for data table
  const scheduleColumns: ColumnDef<any>[] = [
    {
      accessorKey: "type",
      header: "Type",
      cell: ({ row }) => (
        <Badge className={row.original.type === "fetch" ? "bg-blue-100 text-blue-800" : "bg-green-100 text-green-800"}>
          {row.original.type === "fetch" ? "Data Fetch (All 5 Stores)" : "Email Report (All 5 Stores)"}
        </Badge>
      ),
    },
    {
      accessorKey: "cronExpression",
      header: "Schedule",
    },
    {
      accessorKey: "lastRun",
      header: "Last Run",
      cell: ({ row }) => (
        row.original.lastRun
          ? format(parseISO(row.original.lastRun), "yyyy-MM-dd HH:mm")
          : "Never"
      ),
    },
    {
      accessorKey: "nextRun",
      header: "Next Run",
      cell: ({ row }) => (
        row.original.nextRun
          ? format(parseISO(row.original.nextRun), "yyyy-MM-dd HH:mm")
          : "Not scheduled"
      ),
    },
    {
      accessorKey: "enabled",
      header: "Status",
      cell: ({ row }) => (
        <Badge className={row.original.enabled ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}>
          {row.original.enabled ? "Enabled" : "Disabled"}
        </Badge>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditSchedule(row.original)}
          >
            Edit
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => runScheduleNow.mutate(row.original.id)}
          >
            Run Now
          </Button>
        </div>
      ),
    },
  ];

  // Email recipient columns for data table
  const recipientColumns: ColumnDef<any>[] = [
    {
      accessorKey: "email",
      header: "Email",
    },
    {
      accessorKey: "name",
      header: "Name",
    },
    {
      accessorKey: "type",
      header: "Type",
      cell: ({ row }) => (
        <Badge className={
          row.original.type === "to" ? "bg-blue-100 text-blue-800" :
          row.original.type === "cc" ? "bg-purple-100 text-purple-800" :
          "bg-green-100 text-green-800"
        }>
          {row.original.type.toUpperCase()}
        </Badge>
      ),
    },
    {
      id: "notifications",
      header: "Notifications",
      cell: ({ row }) => (
        <div className="flex flex-wrap gap-1">
          {row.original.receiveScheduledReports && (
            <Badge variant="outline" className="text-xs">
              📊 Reports
            </Badge>
          )}
          {row.original.receiveChangeNotifications && (
            <Badge variant="outline" className="text-xs">
              📈 Changes
            </Badge>
          )}
          {row.original.receiveNewProductNotifications && (
            <Badge variant="outline" className="text-xs">
              🆕 New Products
            </Badge>
          )}
        </div>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => deleteEmailRecipient.mutate(row.original.id)}
        >
          <Trash2 className="h-4 w-4 text-red-500" />
        </Button>
      ),
    },
  ];

  // Activity logs columns
  const activityLogsColumns: ColumnDef<any>[] = [
    {
      id: "createdAt",
      header: "Date & Time",
      cell: ({ row }) => (
        <div className="text-sm">
          <div>{format(parseISO(row.original.createdAt), "MMM dd, yyyy")}</div>
          <div className="text-gray-500">{format(parseISO(row.original.createdAt), "HH:mm:ss")}</div>
        </div>
      ),
    },
    {
      id: "username",
      header: "User",
      cell: ({ row }) => (
        <div className="text-sm font-medium">
          {row.original.username || row.original.userId}
        </div>
      ),
    },
    {
      id: "activityType",
      header: "Activity Type",
      cell: ({ row }) => (
        <Badge variant="outline" className="text-xs">
          {row.original.activityType}
        </Badge>
      ),
    },
    {
      id: "description",
      header: "Description",
      cell: ({ row }) => (
        <div className="text-sm max-w-md">
          {row.original.description}
        </div>
      ),
    },
    {
      id: "details",
      header: "Details",
      cell: ({ row }) => {
        const renderDetails = () => {
          if (!row.original.details) return null;

          try {
            if (typeof row.original.details === 'string') {
              // Try to parse if it's a JSON string
              try {
                const parsed = JSON.parse(row.original.details);
                return JSON.stringify(parsed, null, 2);
              } catch {
                // If parsing fails, return as-is
                return row.original.details;
              }
            } else if (typeof row.original.details === 'object') {
              // If it's already an object, stringify it
              return JSON.stringify(row.original.details, null, 2);
            } else {
              // For any other type, convert to string
              return String(row.original.details);
            }
          } catch (error) {
            console.error('Error rendering activity log details:', error);
            return 'Error displaying details';
          }
        };

        return (
          <div className="text-xs text-gray-500 max-w-xs">
            {row.original.details && (
              <pre className="whitespace-pre-wrap">
                {renderDetails()}
              </pre>
            )}
          </div>
        );
      },
    },
    {
      id: "ipAddress",
      header: "IP Address",
      cell: ({ row }) => (
        <div className="text-xs text-gray-500">
          {row.original.ipAddress || 'N/A'}
        </div>
      ),
    },
  ];

  // If not admin, show limited settings
  if (!isAdmin) {
    return (
      <div className="flex h-screen overflow-hidden">
        {/* Sidebar */}
        <div className={`${sidebarOpen ? "block" : "hidden"} md:block md:flex-shrink-0`}>
          <Sidebar />
        </div>

        {/* Main Content */}
        <div className="flex flex-col flex-1 w-0 overflow-hidden">
          <TopNav
            onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
            title="Settings"
          />

          <main className="relative flex-1 overflow-y-auto focus:outline-none">
            <div className="py-6">
              <div className="px-4 sm:px-6 md:px-8">
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <AlertCircle className="h-12 w-12 text-amber-500 mx-auto" />
                    <h3 className="mt-2 text-lg font-medium text-gray-900">Admin Access Required</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      You need administrator privileges to access this page.
                    </p>
                    <Button className="mt-4" asChild>
                      <a href="/">Return to Dashboard</a>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden">
      {/* Sidebar */}
      <div className={`${sidebarOpen ? "block" : "hidden"} md:block md:flex-shrink-0`}>
        <Sidebar />
      </div>

      {/* Main Content */}
      <div className="flex flex-col flex-1 w-0 overflow-hidden">
        <TopNav
          onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
          title="Settings"
        />

        <main className="relative flex-1 overflow-y-auto focus:outline-none">
          <div className="py-6">
            <div className="px-4 sm:px-6 md:px-8">
              <div className="flex justify-between items-center mb-6">
                <div>
                  <h1 className="text-2xl font-semibold text-gray-900">Settings</h1>
                  <p className="mt-1 text-sm text-gray-500">
                    Configure system settings, schedules, and email notifications
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={() => setClearDatabaseDialogOpen(true)}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <Database className="h-4 w-4" />
                    Clear Database
                  </Button>

                  <Button
                    onClick={() => setClearCategoriesDialogOpen(true)}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <Database className="h-4 w-4" />
                    Clear Categories
                  </Button>


                  <Button
                    onClick={() => cleanupBadNotifications.mutate()}
                    variant="outline"
                    className="flex items-center gap-2"
                    disabled={cleanupBadNotifications.isPending}
                  >
                    <Trash2 className="h-4 w-4" />
                    {cleanupBadNotifications.isPending ? "Cleaning..." : "Cleanup Bad Notifications"}
                  </Button>
                </div>
              </div>

              {/* Clear Database Confirmation Dialog */}
              <Dialog open={clearDatabaseDialogOpen} onOpenChange={setClearDatabaseDialogOpen}>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Clear Database</DialogTitle>
                    <DialogDescription>
                      Are you sure you want to clear the database? This will remove all products, prices, and related data.
                      This action cannot be undone.
                    </DialogDescription>
                  </DialogHeader>
                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => setClearDatabaseDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={() => clearDatabase()}
                      disabled={isClearingDatabase}
                    >
                      {isClearingDatabase ? "Clearing..." : "Yes, Clear Database"}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              {/* Clear Categories Confirmation Dialog */}
              <Dialog open={clearCategoriesDialogOpen} onOpenChange={setClearCategoriesDialogOpen}>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Clear Categories</DialogTitle>
                    <DialogDescription>
                      Are you sure you want to clear all categories from the database? This will remove all category data.
                      This action cannot be undone.
                    </DialogDescription>
                  </DialogHeader>
                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => setClearCategoriesDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={() => clearCategories()}
                      disabled={isClearingCategories}
                    >
                      {isClearingCategories ? "Clearing..." : "Yes, Clear Categories"}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
                <TabsList className="w-full border-b pb-0">
                  <TabsTrigger value="scheduling" className="flex items-center">
                    <Clock className="mr-2 h-4 w-4" />
                    Scheduling
                  </TabsTrigger>
                  <TabsTrigger value="email" className="flex items-center">
                    <Mail className="mr-2 h-4 w-4" />
                    Email Configuration
                  </TabsTrigger>
                  <TabsTrigger value="notifications" className="flex items-center">
                    <Bell className="mr-2 h-4 w-4" />
                    Change Notifications
                  </TabsTrigger>
                  <TabsTrigger value="interface" className="flex items-center">
                    <SettingsIcon className="mr-2 h-4 w-4" />
                    Interface
                  </TabsTrigger>
                  <TabsTrigger value="display" className="flex items-center">
                    <Eye className="mr-2 h-4 w-4" />
                    Display Settings
                  </TabsTrigger>
                  <TabsTrigger value="activity-logs" className="flex items-center">
                    <Activity className="mr-2 h-4 w-4" />
                    User Activity Logs
                  </TabsTrigger>
                  <TabsTrigger value="jwt-tokens" className="flex items-center">
                    <Key className="mr-2 h-4 w-4" />
                    JWT Tokens
                  </TabsTrigger>
                  <TabsTrigger value="bearer-tokens" className="flex items-center">
                    <Shield className="mr-2 h-4 w-4" />
                    Bearer Token Management
                  </TabsTrigger>
                </TabsList>

                {/* Scheduling Tab */}
                <TabsContent value="scheduling" className="space-y-6">
                  {/* Manual Fetch Section */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Manual Data Fetch</CardTitle>
                      <CardDescription>
                        Manually fetch product data from stores. This will fetch all products from all pages and categories, excluding 'Soon' items, and apply all data fixes automatically.
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center space-x-4">
                          <div className="flex-1">
                            <label htmlFor="store-select" className="block text-sm font-medium text-gray-700 mb-2">
                              Select Store
                            </label>
                            <select
                              id="store-select"
                              value={selectedStore}
                              onChange={(e) => setSelectedStore(e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            >
                              <option value="all">All Stores</option>
                              {stores && stores.map((store: any) => (
                                <option key={store.id} value={store.slug}>
                                  {store.name}
                                </option>
                              ))}
                            </select>
                          </div>
                          <div className="flex-shrink-0">
                            <Button
                              onClick={() => fetchProducts()}
                              disabled={isFetching}
                              className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {isFetching ? (
                                <>
                                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                  </svg>
                                  Fetching...
                                </>
                              ) : (
                                <>
                                  <svg className="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                  </svg>
                                  Fetch Products
                                </>
                              )}
                            </Button>
                          </div>
                        </div>
                        <div className="text-sm text-gray-600">
                          <p>This will:</p>
                          <ul className="list-disc list-inside mt-1 space-y-1">
                            <li>Fetch all products from {selectedStore === "all" ? "all 5 stores" : "the selected store"}</li>
                            <li>Include all pages and categories</li>
                            <li>Exclude 'Soon' items automatically</li>
                            <li>Apply all data fixes (prices, URLs, images, etc.)</li>
                            <li>Update existing products instead of creating duplicates</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>{editingSchedule ? "Edit Schedule" : "Create Schedule"}</CardTitle>
                      <CardDescription>
                        Configure automatic data fetching and email reporting schedules for all 5 stores (BH Store, Alkhzayran, Zaggzoog, Tamkeen, Bin Momen).
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Form {...scheduleForm}>
                        <form onSubmit={scheduleForm.handleSubmit(onSubmitSchedule)} className="space-y-4">
                          <FormField
                            control={scheduleForm.control}
                            name="type"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Schedule Type</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                  disabled={!!editingSchedule}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select a schedule type" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="fetch">Data Fetch (All 5 Stores)</SelectItem>
                                    <SelectItem value="email">Email Report (All 5 Stores)</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormDescription>
                                  {field.value === "fetch"
                                    ? "Automatically fetch product data from all 5 stores (BH Store, Alkhzayran, Zaggzoog, Tamkeen, Bin Momen)."
                                    : "Automatically send comprehensive product reports with data from all 5 stores via email."}
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={scheduleForm.control}
                            name="scheduleType"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Schedule Type</FormLabel>
                                <Select
                                  onValueChange={(value) => {
                                    field.onChange(value);
                                    // If switching to custom, show the current cron expression
                                    if (value === "custom") {
                                      const currentCron = scheduleForm.getValues("cronExpression");
                                      scheduleForm.setValue("cronExpression", currentCron);
                                    }
                                  }}
                                  defaultValue={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select schedule type" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="hourly">Hourly</SelectItem>
                                    <SelectItem value="daily">Daily</SelectItem>
                                    <SelectItem value="weekly">Weekly</SelectItem>
                                    <SelectItem value="monthly">Monthly</SelectItem>
                                    <SelectItem value="custom">Custom (Advanced)</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormDescription>
                                  Choose how often to run this task
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {scheduleForm.watch("scheduleType") === "custom" ? (
                            <FormField
                              control={scheduleForm.control}
                              name="cronExpression"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Custom Cron Expression</FormLabel>
                                  <FormControl>
                                    <Input {...field} placeholder="0 0 * * *" />
                                  </FormControl>
                                  <FormDescription>
                                    Use cron syntax to define schedule (e.g., "0 0 * * *" for daily at midnight).
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          ) : (
                            <div className="space-y-4">
                              {scheduleForm.watch("scheduleType") === "hourly" && (
                                <FormField
                                  control={scheduleForm.control}
                                  name="hourInterval"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Hour Interval</FormLabel>
                                      <Select
                                        onValueChange={field.onChange}
                                        defaultValue={field.value || "1"}
                                      >
                                        <FormControl>
                                          <SelectTrigger>
                                            <SelectValue placeholder="Select interval" />
                                          </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                          <SelectItem value="1">Every 1 hour</SelectItem>
                                          <SelectItem value="2">Every 2 hours</SelectItem>
                                          <SelectItem value="3">Every 3 hours</SelectItem>
                                          <SelectItem value="4">Every 4 hours</SelectItem>
                                          <SelectItem value="6">Every 6 hours</SelectItem>
                                          <SelectItem value="8">Every 8 hours</SelectItem>
                                          <SelectItem value="12">Every 12 hours</SelectItem>
                                        </SelectContent>
                                      </Select>
                                      <FormDescription>
                                        How often to run the task (in hours)
                                      </FormDescription>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              )}

                              {scheduleForm.watch("scheduleType") !== "hourly" && (
                                <div className="grid grid-cols-2 gap-4">
                                <FormField
                                  control={scheduleForm.control}
                                  name="hour"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Hour (0-23)</FormLabel>
                                      <Select
                                        onValueChange={field.onChange}
                                        defaultValue={field.value}
                                      >
                                        <FormControl>
                                          <SelectTrigger>
                                            <SelectValue placeholder="Select hour" />
                                          </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                          {Array.from({ length: 24 }, (_, i) => (
                                            <SelectItem key={i} value={i.toString()}>
                                              {i.toString().padStart(2, '0')}:00
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />

                                <FormField
                                  control={scheduleForm.control}
                                  name="minute"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Minute (0-59)</FormLabel>
                                      <Select
                                        onValueChange={field.onChange}
                                        defaultValue={field.value}
                                      >
                                        <FormControl>
                                          <SelectTrigger>
                                            <SelectValue placeholder="Select minute" />
                                          </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                          {Array.from({ length: 60 }, (_, i) => (
                                            <SelectItem key={i} value={i.toString()}>
                                              :{i.toString().padStart(2, '0')}
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              </div>
                              )}

                              {scheduleForm.watch("scheduleType") === "weekly" && (
                                <FormField
                                  control={scheduleForm.control}
                                  name="dayOfWeek"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Day of Week</FormLabel>
                                      <Select
                                        onValueChange={field.onChange}
                                        defaultValue={field.value}
                                      >
                                        <FormControl>
                                          <SelectTrigger>
                                            <SelectValue placeholder="Select day" />
                                          </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                          <SelectItem value="0">Sunday</SelectItem>
                                          <SelectItem value="1">Monday</SelectItem>
                                          <SelectItem value="2">Tuesday</SelectItem>
                                          <SelectItem value="3">Wednesday</SelectItem>
                                          <SelectItem value="4">Thursday</SelectItem>
                                          <SelectItem value="5">Friday</SelectItem>
                                          <SelectItem value="6">Saturday</SelectItem>
                                        </SelectContent>
                                      </Select>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              )}

                              {scheduleForm.watch("scheduleType") === "monthly" && (
                                <FormField
                                  control={scheduleForm.control}
                                  name="dayOfMonth"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Day of Month</FormLabel>
                                      <Select
                                        onValueChange={field.onChange}
                                        defaultValue={field.value}
                                      >
                                        <FormControl>
                                          <SelectTrigger>
                                            <SelectValue placeholder="Select day" />
                                          </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                          {Array.from({ length: 31 }, (_, i) => (
                                            <SelectItem key={i + 1} value={(i + 1).toString()}>
                                              {i + 1}
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              )}

                              {scheduleForm.watch("scheduleType") === "hourly" && (
                                <FormField
                                  control={scheduleForm.control}
                                  name="minute"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Starting Minute (0-59)</FormLabel>
                                      <Select
                                        onValueChange={field.onChange}
                                        defaultValue={field.value || "0"}
                                      >
                                        <FormControl>
                                          <SelectTrigger>
                                            <SelectValue placeholder="Select minute" />
                                          </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                          {Array.from({ length: 60 }, (_, i) => (
                                            <SelectItem key={i} value={i.toString()}>
                                              :{i.toString().padStart(2, '0')}
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                      <FormDescription>
                                        The minute of the hour when the task should start
                                      </FormDescription>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              )}
                            </div>
                          )}

                          <FormField
                            control={scheduleForm.control}
                            name="enabled"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Enable Schedule</FormLabel>
                                  <FormDescription>
                                    Enable or disable this scheduled task.
                                  </FormDescription>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <div className="flex justify-end space-x-2">
                            {editingSchedule && (
                              <Button
                                type="button"
                                variant="outline"
                                onClick={handleCancelEdit}
                              >
                                Cancel
                              </Button>
                            )}
                            <Button
                              type="submit"
                              disabled={editingSchedule ? updateSchedule.isPending : createSchedule.isPending}
                            >
                              {editingSchedule ?
                                (updateSchedule.isPending ? "Updating..." : "Update Schedule") :
                                (createSchedule.isPending ? "Creating..." : "Create Schedule")
                              }
                            </Button>
                          </div>
                        </form>
                      </Form>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <div className="flex justify-between items-center">
                        <div>
                          <CardTitle>Main System Schedules</CardTitle>
                          <CardDescription>
                            Manage schedules for data fetching and comprehensive reports from all 5 stores. Product Comparison reports are scheduled separately within the Product Comparisons feature.
                          </CardDescription>
                        </div>
                        {editingSchedule && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setEditingSchedule(null);
                              scheduleForm.reset({
                                type: "fetch",
                                cronExpression: "0 0 * * *",
                                enabled: true,
                                settings: {},
                                scheduleType: "daily",
                                hour: "0",
                                minute: "0",
                                hourInterval: "1",
                              });
                            }}
                          >
                            <Plus className="mr-2 h-4 w-4" />
                            New Schedule
                          </Button>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent>
                      {isLoadingSchedules ? (
                        <Skeleton className="h-48 w-full" />
                      ) : (
                        <DataTable
                          columns={scheduleColumns}
                          data={schedules || []}
                        />
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Email Configuration Tab */}
                <TabsContent value="email" className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Card>
                      <CardHeader>
                        <CardTitle>{editingTemplate ? "Edit Template" : "Email Template"}</CardTitle>
                        <CardDescription>
                          Create or edit email templates for comprehensive product reports from all 5 stores.
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <Form {...templateForm}>
                          <form onSubmit={templateForm.handleSubmit(onSubmitTemplate)} className="space-y-4">
                            <FormField
                              control={templateForm.control}
                              name="name"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Template Name</FormLabel>
                                  <FormControl>
                                    <Input {...field} placeholder="Daily Report" />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={templateForm.control}
                              name="subject"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Email Subject</FormLabel>
                                  <FormControl>
                                    <Input {...field} placeholder="Product Monitoring Report" />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={templateForm.control}
                              name="body"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Email Body</FormLabel>
                                  <FormControl>
                                    <Textarea
                                      {...field}
                                      placeholder="Email body with HTML support"
                                      rows={10}
                                    />
                                  </FormControl>
                                  <FormDescription>
                                    You can use HTML and placeholders like {'{date}'}, {'{totalProducts}'}.
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <div className="flex justify-end space-x-2">
                              {editingTemplate && (
                                <Button
                                  type="button"
                                  variant="outline"
                                  onClick={handleCancelEdit}
                                >
                                  Cancel
                                </Button>
                              )}
                              <Button type="submit">
                                {editingTemplate ? "Update Template" : "Save Template"}
                              </Button>
                            </div>
                          </form>
                        </Form>
                      </CardContent>
                    </Card>

                    <div className="space-y-6">
                      <Card>
                        <CardHeader>
                          <div className="flex justify-between items-center">
                            <CardTitle>Email Templates</CardTitle>
                            <Button variant="outline" size="sm" onClick={() => setEditingTemplate(null)}>
                              <Plus className="mr-2 h-4 w-4" />
                              New Template
                            </Button>
                          </div>
                        </CardHeader>
                        <CardContent>
                          {isLoadingTemplates ? (
                            <Skeleton className="h-32 w-full" />
                          ) : (
                            <div className="space-y-2">
                              {emailTemplates?.length > 0 ? (
                                emailTemplates.map((template: any) => (
                                  <div
                                    key={template.id}
                                    className="flex items-center justify-between p-3 bg-gray-50 rounded-md"
                                  >
                                    <div>
                                      <p className="font-medium">{template.name}</p>
                                      <p className="text-sm text-gray-500">{template.subject}</p>
                                    </div>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleEditTemplate(template)}
                                    >
                                      Edit
                                    </Button>
                                  </div>
                                ))
                              ) : (
                                <div className="text-center py-4 text-gray-500">
                                  No templates available
                                </div>
                              )}
                            </div>
                          )}
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle>Test Email Configuration</CardTitle>
                          <CardDescription>
                            Send a test email to verify your email configuration is working correctly.
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="flex items-end gap-4">
                            <div className="flex-1">
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                Recipient Email
                              </label>
                              <Input
                                type="email"
                                value={testEmailAddress}
                                onChange={(e) => setTestEmailAddress(e.target.value)}
                                placeholder="Enter email address"
                              />
                            </div>
                            <Button
                              onClick={() => {
                                if (!testEmailAddress) {
                                  toast({
                                    title: "Error",
                                    description: "Please enter an email address",
                                    variant: "destructive",
                                  });
                                  return;
                                }
                                sendTestEmail(testEmailAddress);
                              }}
                              disabled={isSendingTestEmail || !testEmailAddress}
                            >
                              {isSendingTestEmail ? "Sending..." : "Send Test Email"}
                            </Button>
                          </div>
                          <div className="mt-4 text-sm text-gray-500">
                            <p>Email will be sent from: <strong><EMAIL></strong></p>
                            <p className="mt-1">This will help verify that your email configuration is working correctly.</p>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle>Send Email Now</CardTitle>
                          <CardDescription>
                            Send an email immediately with the latest Excel sheet containing all products from all stores.
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                              <div className="flex items-start space-x-3">
                                <div className="flex-shrink-0">
                                  <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                                  </svg>
                                </div>
                                <div className="flex-1 text-sm">
                                  <p className="font-medium text-blue-800">What will be sent:</p>
                                  <ul className="mt-1 text-blue-700 list-disc list-inside space-y-1">
                                    <li>Excel file with all products from all 5 stores</li>
                                    <li>Sent to all configured email recipients</li>
                                    <li>Uses the default email template</li>
                                    <li>Includes current prices, stock status, and product details</li>
                                  </ul>
                                </div>
                              </div>
                            </div>

                            <Button
                              onClick={() => sendEmailNow()}
                              disabled={isSendingEmailNow}
                              className="w-full"
                              size="lg"
                            >
                              {isSendingEmailNow ? (
                                <>
                                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                  </svg>
                                  Sending Email...
                                </>
                              ) : (
                                <>
                                  <svg className="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                  </svg>
                                  Send Email Now with Excel Sheet
                                </>
                              )}
                            </Button>

                            <div className="text-xs text-gray-500">
                              <p><strong>Note:</strong> Make sure you have configured email recipients in the section below before sending.</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <div className="flex justify-between items-center">
                            <CardTitle>Email Recipients</CardTitle>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setIsAddingRecipient(!isAddingRecipient);
                                recipientForm.reset();
                              }}
                            >
                              {isAddingRecipient ? "Cancel" : "Add Recipient"}
                            </Button>
                          </div>
                        </CardHeader>
                        <CardContent>
                          {isAddingRecipient && (
                            <Card className="mb-4 border-primary">
                              <CardContent className="pt-4">
                                <Form {...recipientForm}>
                                  <form onSubmit={recipientForm.handleSubmit(onSubmitRecipient)} className="space-y-4">
                                    <FormField
                                      control={recipientForm.control}
                                      name="email"
                                      render={({ field }) => (
                                        <FormItem>
                                          <FormLabel>Email Address</FormLabel>
                                          <FormControl>
                                            <Input {...field} placeholder="<EMAIL>" />
                                          </FormControl>
                                          <FormMessage />
                                        </FormItem>
                                      )}
                                    />

                                    <FormField
                                      control={recipientForm.control}
                                      name="name"
                                      render={({ field }) => (
                                        <FormItem>
                                          <FormLabel>Name (Optional)</FormLabel>
                                          <FormControl>
                                            <Input {...field} placeholder="John Doe" />
                                          </FormControl>
                                          <FormMessage />
                                        </FormItem>
                                      )}
                                    />

                                    <FormField
                                      control={recipientForm.control}
                                      name="type"
                                      render={({ field }) => (
                                        <FormItem>
                                          <FormLabel>Recipient Type</FormLabel>
                                          <Select
                                            onValueChange={field.onChange}
                                            defaultValue={field.value}
                                          >
                                            <FormControl>
                                              <SelectTrigger>
                                                <SelectValue placeholder="Select type" />
                                              </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                              <SelectItem value="to">To</SelectItem>
                                              <SelectItem value="cc">CC</SelectItem>
                                              <SelectItem value="bcc">BCC</SelectItem>
                                            </SelectContent>
                                          </Select>
                                          <FormMessage />
                                        </FormItem>
                                      )}
                                    />

                                    <div className="space-y-4">
                                      <div className="text-sm font-medium text-gray-700">Notification Preferences</div>

                                      <FormField
                                        control={recipientForm.control}
                                        name="receiveScheduledReports"
                                        render={({ field }) => (
                                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                                            <div className="space-y-0.5">
                                              <FormLabel className="text-sm">Scheduled Reports</FormLabel>
                                              <FormDescription className="text-xs">
                                                Receive daily/scheduled Excel reports with all products
                                              </FormDescription>
                                            </div>
                                            <FormControl>
                                              <Switch
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                              />
                                            </FormControl>
                                          </FormItem>
                                        )}
                                      />

                                      <FormField
                                        control={recipientForm.control}
                                        name="receiveChangeNotifications"
                                        render={({ field }) => (
                                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                                            <div className="space-y-0.5">
                                              <FormLabel className="text-sm">Price & Stock Changes</FormLabel>
                                              <FormDescription className="text-xs">
                                                Receive notifications when product prices or stock change
                                              </FormDescription>
                                            </div>
                                            <FormControl>
                                              <Switch
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                              />
                                            </FormControl>
                                          </FormItem>
                                        )}
                                      />

                                      <FormField
                                        control={recipientForm.control}
                                        name="receiveNewProductNotifications"
                                        render={({ field }) => (
                                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                                            <div className="space-y-0.5">
                                              <FormLabel className="text-sm">New Products</FormLabel>
                                              <FormDescription className="text-xs">
                                                Receive notifications when new products are added
                                              </FormDescription>
                                            </div>
                                            <FormControl>
                                              <Switch
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                              />
                                            </FormControl>
                                          </FormItem>
                                        )}
                                      />
                                    </div>

                                    <div className="flex justify-end">
                                      <Button type="submit">Add Recipient</Button>
                                    </div>
                                  </form>
                                </Form>
                              </CardContent>
                            </Card>
                          )}

                          {isLoadingRecipients ? (
                            <Skeleton className="h-32 w-full" />
                          ) : (
                            <DataTable
                              columns={recipientColumns}
                              data={emailRecipients || []}
                            />
                          )}
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </TabsContent>

                {/* Change Notifications Tab */}
                <TabsContent value="notifications" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Change Notification Settings</CardTitle>
                      <CardDescription>
                        Configure email notifications for product price changes, stock changes, and new products.
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Form {...changeNotificationForm}>
                        <form onSubmit={changeNotificationForm.handleSubmit(onSubmitChangeNotifications)} className="space-y-6">
                          <div className="space-y-4">
                            <FormField
                              control={changeNotificationForm.control}
                              name="enablePriceChangeNotifications"
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                  <div className="space-y-0.5">
                                    <FormLabel className="text-base">Price Change Notifications</FormLabel>
                                    <FormDescription>
                                      Send email notifications when product prices change.
                                    </FormDescription>
                                  </div>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={changeNotificationForm.control}
                              name="enableStockChangeNotifications"
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                  <div className="space-y-0.5">
                                    <FormLabel className="text-base">Stock Change Notifications</FormLabel>
                                    <FormDescription>
                                      Send email notifications when product stock status changes.
                                    </FormDescription>
                                  </div>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={changeNotificationForm.control}
                              name="enableNewProductNotifications"
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                  <div className="space-y-0.5">
                                    <FormLabel className="text-base">New Product Notifications</FormLabel>
                                    <FormDescription>
                                      Send email notifications when new products are added.
                                    </FormDescription>
                                  </div>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={changeNotificationForm.control}
                              name="minimumPriceChangePercentage"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Minimum Price Change Percentage</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="number"
                                      step="0.01"
                                      min="0"
                                      max="100"
                                      placeholder="0.00"
                                      {...field}
                                    />
                                  </FormControl>
                                  <FormDescription>
                                    Only send price change notifications if the change is at least this percentage. Set to 0 to notify on any price change.
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={changeNotificationForm.control}
                              name="emailSubject"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Email Subject</FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="Product Changes Summary"
                                      {...field}
                                    />
                                  </FormControl>
                                  <FormDescription>
                                    Subject line for change notification emails.
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          <div className="flex gap-4 flex-wrap">
                            <Button
                              type="submit"
                              disabled={updateChangeNotificationSettings.isPending}
                            >
                              {updateChangeNotificationSettings.isPending ? "Saving..." : "Save Settings"}
                            </Button>

                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => createTestChanges.mutate()}
                              disabled={createTestChanges.isPending}
                            >
                              {createTestChanges.isPending ? "Creating..." : "Create Test Changes"}
                            </Button>

                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => sendChangeNotifications.mutate()}
                              disabled={sendChangeNotifications.isPending}
                            >
                              {sendChangeNotifications.isPending ? "Sending..." : "Send Notifications Now"}
                            </Button>

                            <Button
                              type="button"
                              variant="outline"
                              onClick={async () => {
                                try {
                                  const response = await fetch(`${API_BASE_URL}/api/change-notifications/debug`, {
                                    credentials: 'include'
                                  });
                                  const data = await response.json();
                                  console.log('Debug info:', data);
                                  alert(`Debug Info:\n- Total changes: ${data.debug.totalChanges}\n- Unsent changes: ${data.debug.unsentChanges}\n- Email recipients: ${data.debug.emailRecipients}\n- Settings: ${data.debug.settings ? 'Configured' : 'Not configured'}`);
                                } catch (error) {
                                  console.error('Debug error:', error);
                                  alert('Failed to get debug info');
                                }
                              }}
                            >
                              Debug Changes
                            </Button>

                            <Button
                              type="button"
                              variant="outline"
                              onClick={async () => {
                                if (confirm('Reset all changes to unsent status? This will allow you to test notifications again.')) {
                                  try {
                                    const response = await fetch(`${API_BASE_URL}/api/change-notifications/reset-all`, {
                                      method: 'POST',
                                      credentials: 'include'
                                    });
                                    const data = await response.json();
                                    if (data.success) {
                                      alert('All changes reset to unsent status');
                                    } else {
                                      alert('Failed to reset changes');
                                    }
                                  } catch (error) {
                                    console.error('Reset error:', error);
                                    alert('Failed to reset changes');
                                  }
                                }
                              }}
                            >
                              Reset All Changes
                            </Button>
                          </div>
                        </form>
                      </Form>
                    </CardContent>
                  </Card>

                  {isLoadingChangeSettings ? (
                    <Card>
                      <CardHeader>
                        <Skeleton className="h-6 w-48" />
                      </CardHeader>
                      <CardContent>
                        <Skeleton className="h-32 w-full" />
                      </CardContent>
                    </Card>
                  ) : (
                    <Card>
                      <CardHeader>
                        <CardTitle>Email Recipients</CardTitle>
                        <CardDescription>
                          Change notifications will be sent to the email recipients configured in the Email Configuration tab.
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="text-sm text-gray-600">
                          <p>Current recipients: {emailRecipients?.length || 0}</p>
                          <p className="mt-2">
                            To add or modify email recipients, go to the <strong>Email Configuration</strong> tab.
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>

                {/* Interface Configuration Tab */}
                <TabsContent value="interface" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Interface Settings</CardTitle>
                      <CardDescription>
                        Configure which pages and features are visible in the navigation menu.
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <div className="text-base font-medium">All Products Table</div>
                          <div className="text-sm text-gray-500">
                            Show or hide the "All Products Table" page in the navigation menu.
                            This page displays all products from all stores in an Excel-like format.
                          </div>
                        </div>
                        <Switch
                          checked={showAllProductsTable}
                          onCheckedChange={(checked) => {
                            setShowAllProductsTable(checked);
                            updateInterfaceSettings.mutate({ showAllProductsTable: checked });
                          }}
                          disabled={updateInterfaceSettings.isPending}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <div className="text-base font-medium">Wishlists</div>
                          <div className="text-sm text-gray-500">
                            Show or hide the "Wishlists" page in the navigation menu and wishlist buttons in product cards.
                            This controls the entire wishlist functionality visibility.
                          </div>
                        </div>
                        <Switch
                          checked={showWishlists}
                          onCheckedChange={(checked) => {
                            setShowWishlists(checked);
                            updateInterfaceSettings.mutate({ showWishlists: checked });
                          }}
                          disabled={updateInterfaceSettings.isPending}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <div className="text-base font-medium">Saved Searches</div>
                          <div className="text-sm text-gray-500">
                            Show or hide the "Saved Searches" page in the navigation menu.
                            This page allows users to save and manage their frequently used search queries.
                          </div>
                        </div>
                        <Switch
                          checked={showSavedSearches}
                          onCheckedChange={(checked) => {
                            setShowSavedSearches(checked);
                            updateInterfaceSettings.mutate({ showSavedSearches: checked });
                          }}
                          disabled={updateInterfaceSettings.isPending}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <div className="text-base font-medium">Recommendations</div>
                          <div className="text-sm text-gray-500">
                            Show or hide the "Recommendations" page in the navigation menu.
                            This page provides AI-powered product recommendations based on user behavior.
                          </div>
                        </div>
                        <Switch
                          checked={showRecommendations}
                          onCheckedChange={(checked) => {
                            setShowRecommendations(checked);
                            updateInterfaceSettings.mutate({ showRecommendations: checked });
                          }}
                          disabled={updateInterfaceSettings.isPending}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <div className="text-base font-medium">Report Builder</div>
                          <div className="text-sm text-gray-500">
                            Show or hide the "Report Builder" page in the navigation menu.
                            This page allows users to create custom reports with drag-and-drop functionality.
                          </div>
                        </div>
                        <Switch
                          checked={showReportBuilder}
                          onCheckedChange={(checked) => {
                            setShowReportBuilder(checked);
                            updateInterfaceSettings.mutate({ showReportBuilder: checked });
                          }}
                          disabled={updateInterfaceSettings.isPending}
                        />
                      </div>
                    </CardContent>
                    <CardFooter>
                      <div className="text-sm text-gray-500">
                        Changes are saved automatically. Refresh the page to see navigation menu updates.
                      </div>
                    </CardFooter>
                  </Card>
                </TabsContent>

                {/* Display Settings Tab */}
                <TabsContent value="display" className="space-y-6">
                  <DisplaySettingsTab />
                </TabsContent>

                {/* User Activity Logs Tab */}
                <TabsContent value="activity-logs" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <div className="flex justify-between items-center">
                        <div>
                          <CardTitle>User Activity Logs</CardTitle>
                          <CardDescription>
                            View user activity history including logins, searches, downloads, and other actions.
                          </CardDescription>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            onClick={() => cleanupActivityLogs.mutate(30)}
                            variant="outline"
                            size="sm"
                            disabled={cleanupActivityLogs.isPending}
                          >
                            {cleanupActivityLogs.isPending ? "Cleaning..." : "Cleanup Old Logs"}
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Filters */}
                      <div className="flex gap-4 items-center">
                        <div className="flex-1">
                          <Select
                            value={selectedActivityType}
                            onValueChange={setSelectedActivityType}
                          >
                            <SelectTrigger className="w-[200px]">
                              <SelectValue placeholder="All Activity Types" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All Activity Types</SelectItem>
                              <SelectItem value="login">Login</SelectItem>
                              <SelectItem value="logout">Logout</SelectItem>
                              <SelectItem value="search">Search</SelectItem>
                              <SelectItem value="download">Download</SelectItem>
                              <SelectItem value="view_page">Page View</SelectItem>
                              <SelectItem value="filter">Filter</SelectItem>
                              <SelectItem value="export">Export</SelectItem>
                              <SelectItem value="fetch_data">Data Fetch</SelectItem>
                              <SelectItem value="settings_change">Settings Change</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="flex-1">
                          <Select
                            value={selectedUserId}
                            onValueChange={setSelectedUserId}
                          >
                            <SelectTrigger className="w-[200px]">
                              <SelectValue placeholder="All Users" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All Users</SelectItem>
                              {allUsers && Array.isArray(allUsers) && allUsers.map((user: any) => (
                                <SelectItem key={user.id} value={user.id}>
                                  {user.username}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="flex gap-2">
                          <Select
                            value={activityLogsLimit.toString()}
                            onValueChange={(value) => setActivityLogsLimit(parseInt(value))}
                          >
                            <SelectTrigger className="w-[120px]">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="25">25 logs</SelectItem>
                              <SelectItem value="50">50 logs</SelectItem>
                              <SelectItem value="100">100 logs</SelectItem>
                              <SelectItem value="200">200 logs</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      {/* Activity Logs Table */}
                      {isLoadingActivityLogs ? (
                        <Skeleton className="h-96 w-full" />
                      ) : (
                        <div className="border rounded-lg">
                          <DataTable
                            columns={activityLogsColumns}
                            data={userActivityLogs || []}
                          />
                        </div>
                      )}

                      {/* Pagination */}
                      <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-500">
                          Showing {userActivityLogs?.length || 0} activity logs
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setActivityLogsPage(Math.max(1, activityLogsPage - 1))}
                            disabled={activityLogsPage <= 1}
                          >
                            Previous
                          </Button>
                          <span className="text-sm text-gray-500 px-3 py-1">
                            Page {activityLogsPage}
                          </span>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setActivityLogsPage(activityLogsPage + 1)}
                            disabled={!userActivityLogs || userActivityLogs.length < activityLogsLimit}
                          >
                            Next
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* JWT Tokens Tab */}
                <TabsContent value="jwt-tokens" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Key className="h-5 w-5" />
                        Alkhunaizan JWT Token Management
                      </CardTitle>
                      <CardDescription>
                        Manage the JWT token for Alkhunaizan store API access. The token is required for fetching products from Alkhunaizan.
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {/* Current Token Status */}
                      {alkhunaizanTokenData && (
                        <div className="p-4 border rounded-lg bg-gray-50">
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="font-medium text-gray-900">Current Token Status</h4>
                            <Badge
                              className={alkhunaizanTokenData.isValid ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}
                            >
                              {alkhunaizanTokenData.isValid ? "✅ Valid" : "❌ Invalid/Expired"}
                            </Badge>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="font-medium text-gray-700">Status:</span>
                              <span className="ml-2 text-gray-600">{alkhunaizanTokenData.message}</span>
                            </div>

                            {alkhunaizanTokenData.expiresAt && (
                              <div>
                                <span className="font-medium text-gray-700">Expires:</span>
                                <span className="ml-2 text-gray-600">
                                  {format(parseISO(alkhunaizanTokenData.expiresAt), "MMM dd, yyyy HH:mm")}
                                </span>
                              </div>
                            )}
                          </div>

                          {alkhunaizanTokenData.token && (
                            <div className="mt-3">
                              <span className="font-medium text-gray-700">Token Preview:</span>
                              <div className="mt-1 p-2 bg-white border rounded text-xs font-mono text-gray-600 break-all">
                                {alkhunaizanTokenData.token.substring(0, 50)}...
                              </div>
                            </div>
                          )}
                        </div>
                      )}

                      {/* Token Update Form */}
                      <div className="space-y-4">
                        <div>
                          <label htmlFor="jwt-token" className="block text-sm font-medium text-gray-700 mb-2">
                            JWT Token
                          </label>
                          <Textarea
                            id="jwt-token"
                            placeholder="Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                            value={alkhunaizanToken}
                            onChange={(e) => setAlkhunaizanToken(e.target.value)}
                            className="min-h-[120px] font-mono text-sm"
                            disabled={isUpdatingToken}
                          />
                          <p className="mt-2 text-sm text-gray-500">
                            Enter the complete JWT token including the "Bearer " prefix. You can get this token by inspecting network requests on the Alkhunaizan website.
                          </p>
                        </div>

                        <div className="flex gap-3">
                          <Button
                            onClick={handleUpdateToken}
                            disabled={isUpdatingToken || !alkhunaizanToken.trim()}
                            className="flex items-center gap-2"
                          >
                            {isUpdatingToken ? (
                              <>
                                <RefreshCw className="h-4 w-4 animate-spin" />
                                Updating...
                              </>
                            ) : (
                              <>
                                <Key className="h-4 w-4" />
                                Update Token
                              </>
                            )}
                          </Button>

                          <Button
                            variant="outline"
                            onClick={() => refetchAlkhunaizanToken()}
                            disabled={isUpdatingToken}
                            className="flex items-center gap-2"
                          >
                            <RefreshCw className="h-4 w-4" />
                            Refresh Status
                          </Button>
                        </div>
                      </div>

                      {/* Instructions */}
                      <div className="p-4 border rounded-lg bg-blue-50">
                        <h4 className="font-medium text-blue-900 mb-2">How to Get a New Token</h4>
                        <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
                          
                          <li>Paste it in the field above and click "Update Token"</li>
                        </ol>
                      </div>

                      {/* Token Validation Info */}
                      <div className="p-4 border rounded-lg bg-amber-50">
                        <h4 className="font-medium text-amber-900 mb-2">Token Information</h4>
                        <ul className="text-sm text-amber-800 space-y-1 list-disc list-inside">
                          <li>JWT tokens typically expire after a few days or weeks</li>
                          <li>The system will automatically validate the token format and expiration</li>
                          <li>If the token is expired, Alkhunaizan product fetching will fail</li>
                          <li>You can continue using the other 4 stores even if this token is invalid</li>
                          <li>The token is stored securely and only accessible by administrators</li>
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Bearer Token Management Tab */}
                <TabsContent value="bearer-tokens" className="space-y-6">
                  <BearerTokenManagement />
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
