#!/usr/bin/env node

import 'dotenv/config';
import pg from 'pg';

const pool = new pg.Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false
});

async function debugPriceHistory() {
  try {
    console.log('🔍 DEBUGGING PRICE HISTORY ISSUE');
    console.log('='.repeat(60));

    // Check the specific product from the error (438944)
    const productId = 438944;

    // Step 1: Check table counts
    console.log('\n1️⃣ Checking table counts...');

    const productPricesCount = await pool.query(`SELECT COUNT(*) as count FROM product_prices`);
    console.log(`Product Prices table: ${productPricesCount.rows[0]?.count || 0} records`);

    const priceHistoryCount = await pool.query(`SELECT COUNT(*) as count FROM price_history`);
    console.log(`Price History table: ${priceHistoryCount.rows[0]?.count || 0} records`);

    const productsCount = await pool.query(`SELECT COUNT(*) as count FROM products`);
    console.log(`Products table: ${productsCount.rows[0]?.count || 0} records`);

    // Step 2: Check if specific product exists
    console.log('\n2️⃣ Checking if product 438944 exists...');
    const productResult = await pool.query(`
      SELECT id, name, brand, price, store_id
      FROM products
      WHERE id = $1
    `, [productId]);

    let testProductId = productId;

    if (productResult.rows.length === 0) {
      console.log('❌ Product 438944 not found');

      // Check for any products with price data
      console.log('\n🔍 Looking for products with price data...');
      const sampleProducts = await pool.query(`
        SELECT p.id, p.name, p.brand, p.price, p.store_id,
               pp_count.price_count, ph_count.history_count
        FROM products p
        LEFT JOIN (
          SELECT product_id, COUNT(*) as price_count
          FROM product_prices
          GROUP BY product_id
        ) pp_count ON p.id = pp_count.product_id
        LEFT JOIN (
          SELECT product_id, COUNT(*) as history_count
          FROM price_history
          GROUP BY product_id
        ) ph_count ON p.id = ph_count.product_id
        WHERE pp_count.price_count > 0 OR ph_count.history_count > 0
        ORDER BY p.id
        LIMIT 5
      `);

      console.log('Sample products with data:');
      sampleProducts.rows.forEach(p => {
        console.log(`  ID: ${p.id}, Name: ${p.name?.substring(0, 40)}..., Prices: ${p.price_count || 0}, History: ${p.history_count || 0}`);
      });

      if (sampleProducts.rows.length > 0) {
        // Use the first product with data for testing
        const testProduct = sampleProducts.rows[0];
        console.log(`\n🧪 Testing with product ID: ${testProduct.id}`);
        testProductId = testProduct.id;
      } else {
        console.log('❌ No products with price data found');
        testProductId = null;
      }
    } else {
      const product = productResult.rows[0];
      console.log('✅ Product found:', product.name);
      console.log('   Current Price:', product.price, 'SAR');
    }

    if (!testProductId) {
      console.log('❌ No product to test with');
      return;
    }

    if (!testProductId) {
      console.log('❌ No product to test with');
      return;
    }

    // Step 3: Check price_history table for test product
    console.log(`\n3️⃣ Checking price_history table for product ${testProductId}...`);
    const historyResult = await pool.query(`
      SELECT * FROM price_history
      WHERE product_id = $1
      ORDER BY recorded_at DESC
    `, [testProductId]);

    console.log('📊 Price history records:', historyResult.rows.length);
    if (historyResult.rows.length > 0) {
      console.log('Sample records:', JSON.stringify(historyResult.rows.slice(0, 2), null, 2));
    }

    // Step 4: Check product_prices table for test product
    console.log(`\n4️⃣ Checking product_prices table for product ${testProductId}...`);
    const pricesResult = await pool.query(`
      SELECT price, currency, stock, status, timestamp
      FROM product_prices
      WHERE product_id = $1
      ORDER BY timestamp DESC
      LIMIT 10
    `, [testProductId]);

    console.log('📊 Product prices records:', pricesResult.rows.length);
    if (pricesResult.rows.length > 0) {
      console.log('Sample records:', JSON.stringify(pricesResult.rows.slice(0, 3), null, 2));
    }

    // Step 5: Identify the core issue
    console.log('\n5️⃣ Analyzing the issue...');

    if (priceHistoryCount.rows[0]?.count == 0) {
      console.log('❌ ISSUE FOUND: price_history table is empty!');
      console.log('   This explains why no price history data is available.');

      if (productPricesCount.rows[0]?.count > 0) {
        console.log('✅ However, product_prices table has data.');
        console.log('   The storage methods are incorrectly querying product_prices instead of price_history.');
        console.log('   We need to either:');
        console.log('   1. Fix the storage methods to query the correct table');
        console.log('   2. Populate the price_history table from existing data');
        console.log('   3. Set up proper data flow to populate price_history during scraping');
      } else {
        console.log('❌ Both tables are empty - no price data at all!');
      }
    } else {
      console.log('✅ price_history table has data');
      if (historyResult.rows.length === 0) {
        console.log(`❌ But no data for product ${testProductId}`);
      }
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await pool.end();
  }
}

// Main execution
async function main() {
  await debugPriceHistory();
  console.log('\n✅ Debug completed');
}

main().catch(error => {
  console.error('❌ Debug failed:', error);
  process.exit(1);
});
