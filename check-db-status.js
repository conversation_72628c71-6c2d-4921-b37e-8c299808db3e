/**
 * Simple script to check database status using the existing storage module
 */

import { storage } from './server/storage.js';

async function checkDatabaseStatus() {
  try {
    console.log('🔍 CHECKING DATABASE STATUS');
    console.log('='.repeat(50));

    // Check table counts
    console.log('\n📊 Table counts:');
    
    try {
      const productPricesCount = await storage.executeQuery(`SELECT COUNT(*) as count FROM product_prices`);
      console.log(`Product Prices: ${productPricesCount[0]?.count || 0} records`);
    } catch (error) {
      console.log(`Product Prices: Error - ${error.message}`);
    }

    try {
      const priceHistoryCount = await storage.executeQuery(`SELECT COUNT(*) as count FROM price_history`);
      console.log(`Price History: ${priceHistoryCount[0]?.count || 0} records`);
    } catch (error) {
      console.log(`Price History: Error - ${error.message}`);
    }

    try {
      const productsCount = await storage.executeQuery(`SELECT COUNT(*) as count FROM products`);
      console.log(`Products: ${productsCount[0]?.count || 0} records`);
    } catch (error) {
      console.log(`Products: Error - ${error.message}`);
    }

    // Check if specific product exists
    console.log('\n🎯 Checking product 438944:');
    try {
      const product = await storage.executeQuery(`
        SELECT id, name, brand, price, store_id 
        FROM products 
        WHERE id = 438944
      `);
      
      if (product.length > 0) {
        console.log('✅ Product found:', product[0].name);
      } else {
        console.log('❌ Product 438944 not found');
        
        // Find any product with data
        const anyProduct = await storage.executeQuery(`
          SELECT id, name, brand, price, store_id 
          FROM products 
          ORDER BY id 
          LIMIT 1
        `);
        
        if (anyProduct.length > 0) {
          console.log(`🔍 Using sample product: ${anyProduct[0].id} - ${anyProduct[0].name}`);
          
          // Test storage methods with this product
          console.log('\n🧪 Testing storage methods:');
          const priceHistory = await storage.getPriceHistory(anyProduct[0].id, 30);
          console.log(`getPriceHistory result: ${priceHistory.length} entries`);
          
          if (priceHistory.length > 0) {
            console.log('Sample entry:', JSON.stringify(priceHistory[0], null, 2));
          }
        }
      }
    } catch (error) {
      console.log(`Error checking product: ${error.message}`);
    }

    console.log('\n✅ Database check completed');

  } catch (error) {
    console.error('❌ Database check failed:', error);
  }
}

checkDatabaseStatus().then(() => {
  process.exit(0);
}).catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
