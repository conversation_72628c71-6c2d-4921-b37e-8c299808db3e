# 🔑 Bearer Token Management System

## Overview

The Bearer Token Management system provides a comprehensive solution for manually configuring and managing bearer tokens for stores that require API authentication. This feature serves as a backup solution when automatic token extraction fails and ensures scrapers can continue working with manually provided tokens.

## 🚀 Features

### 1. **Settings Page Interface**
- Dedicated "Bearer Token Management" section in settings
- Input fields for each store requiring bearer token authentication
- Clear labels identifying which token belongs to which store
- "Save" and "Test Token" buttons for each store
- Token expiry dates and status display (Valid/Expired/Invalid)

### 2. **Manual Token Input**
- Secure token input with validation
- JWT format validation (must start with "eyJ")
- Token preview showing first 30 characters + "..."
- Timestamp tracking of when tokens were last updated

### 3. **Real-time Scraper Updates**
- Immediate scraper configuration updates when tokens are saved
- No restart required - tokens applied to active scraper instances
- Database persistence for token storage

### 4. **Multi-Store Support**
- **Blackbox** (api.ops.blackbox.com.sa)
- **Alkhunaizan** (api.alkhn.com)
- Extensible for additional stores requiring bearer tokens

### 5. **Token Testing & Validation**
- "Test Token" functionality with real API calls
- Success/failure status with detailed error messages
- Product count verification
- Authentication issue resolution confirmation

### 6. **Fallback & Error Handling**
- Automatic fallback to token extraction if manual tokens fail
- Comprehensive error logging and user feedback
- Email notifications for token updates
- Clear validation messages for invalid token formats

## 🛠️ Setup Instructions

### 1. **Database Setup**
```bash
# Create the bearer_tokens table
node setup-bearer-tokens-table.js
```

### 2. **Test the System**
```bash
# Test bearer token management functionality
node test-bearer-token-management.js
```

### 3. **Start the Server**
```bash
# Start the server with bearer token management enabled
npm start
```

## 📖 Usage Guide

### **Accessing Bearer Token Management**

1. Navigate to **Settings** page
2. Click on **"Bearer Token Management"** tab
3. You'll see sections for each supported store

### **Adding a Bearer Token**

1. **Get Token from Browser:**
   - Open Chrome DevTools (F12)
   - Go to store website (e.g., blackbox.com.sa)
   - Navigate to Network tab
   - Refresh page and look for API requests
   - Find Authorization header: `Bearer eyJhbGciOiJIUzI1NiIs...`
   - Copy the complete token (everything after "Bearer ")

2. **Add Token in Settings:**
   - Paste token in the appropriate store field
   - Click **"Test Token"** to verify it works
   - Click **"Save"** to store the token

3. **Verify Success:**
   - Token status should show "Valid"
   - Test results should show products accessible
   - Scrapers will automatically use the new token

### **Token Status Indicators**

- 🟢 **Valid**: Token is working and not expired
- 🔴 **Expired**: Token has passed its expiry date
- 🔴 **Invalid**: Token format is incorrect or authentication failed
- ⚪ **No Token**: No token configured for this store

## 🔧 API Endpoints

### **GET /api/bearer-tokens**
Retrieve all stored bearer tokens with status information.

**Response:**
```json
{
  "success": true,
  "tokens": {
    "blackbox": {
      "storeName": "BlackBox",
      "hasToken": true,
      "tokenPreview": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "lastUpdated": "2025-01-10T10:30:00Z",
      "isValid": true,
      "isExpired": false,
      "expiryDate": "2025-12-24T23:59:59Z"
    }
  }
}
```

### **POST /api/bearer-tokens/:store**
Save bearer token for specific store.

**Request:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### **POST /api/bearer-tokens/:store/test**
Test bearer token with store API.

**Request:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response:**
```json
{
  "success": true,
  "tokenTest": {
    "success": true,
    "status": 200,
    "productCount": 150,
    "responseSize": 45678
  }
}
```

### **DELETE /api/bearer-tokens/:store**
Remove bearer token for specific store.

## 🔍 Troubleshooting

### **Common Issues**

1. **"Invalid JWT format" Error**
   - Ensure token starts with "eyJ"
   - Check that token has 3 parts separated by dots
   - Verify no extra characters or spaces

2. **"Test Failed" with 401 Error**
   - Token may be expired
   - Token may be from wrong store/environment
   - Try getting a fresh token from browser

3. **"No products found" with 200 Status**
   - Token is valid but category may be empty
   - Try testing with different store categories
   - Check if store has products available

### **Getting Fresh Tokens**

**For Blackbox:**
1. Go to https://www.blackbox.com.sa/en/air-conditioner-c-175
2. Open DevTools → Network tab
3. Refresh page
4. Look for requests to `api.ops.blackbox.com.sa`
5. Copy Authorization header value

**For Alkhunaizan:**
1. Go to https://www.alkhn.com
2. Follow similar process for their API endpoints

## 🔒 Security Considerations

- Tokens are stored securely in the database
- Token previews only show first 30 characters
- Sensitive token data is not logged
- Tokens are transmitted over HTTPS only
- Database access is restricted to authorized operations

## 📧 Email Notifications

When tokens are updated manually, email notifications are sent with:
- Store name and action performed
- Token preview (first 30 characters)
- Expiry date information
- Update timestamp

## 🔄 Integration with Scrapers

The system automatically integrates with existing scrapers:

1. **Blackbox Scraper**: Checks database for manual tokens before using hardcoded ones
2. **Alkhunaizan Scraper**: Uses manual tokens when available
3. **Future Scrapers**: Can easily integrate using the same pattern

## 🎯 Benefits

- **Reliability**: Backup solution when automatic extraction fails
- **Flexibility**: Manual control over authentication tokens
- **Monitoring**: Real-time token status and validation
- **Maintenance**: Easy token updates without code changes
- **Scalability**: Support for multiple stores and token types

## 🚀 Future Enhancements

- Automatic token expiry alerts
- Token refresh scheduling
- Bulk token import/export
- Token usage analytics
- Integration with more stores

---

**Ready to use!** The Bearer Token Management system provides a robust solution for maintaining scraper authentication when automatic methods fail.
