# SWSG Store Scraper Duplicate Fix - Final Implementation Summary

## 🔍 Problem Analysis (December 2024)

Investigation of the SWSG store scraper revealed a massive duplicate product issue:

### Current Database State
- **393 products with duplicate names** found in database
- **10 products with 50+ duplicates each** (some with 56 copies)
- **11,314 total SWSG products** with significant duplication
- **0 duplicate SKUs** and **0 duplicate external_ids** (database constraints working)

### Root Cause Identified
The core issue was the `globalProductCounter` variable in the `extractUniqueProductId` function:

```javascript
// PROBLEMATIC CODE
let globalProductCounter = 0; // Resets to 0 each scraping session

function extractUniqueProductId(element, productName, productUrl, index = 0) {
  globalProductCounter++; // Increments from 0 each session
  
  // This caused instability:
  return `swsg-url-${hash}-${globalProductCounter}`; // Different each session
  return `swsg-name-${hash}-${timeComponent}-${globalProductCounter}`; // Different each session
}
```

### Why This Created Duplicates
1. **Session 1**: Product "Samsung TV" gets external_id `swsg-url-123-1`
2. **Session 2**: Same product gets external_id `swsg-url-123-2` 
3. **Result**: Database sees different external_ids → Creates duplicate products

## ✅ Solutions Implemented

### 1. **Fixed Unique ID Generation (CRITICAL FIX)**

**Removed globalProductCounter dependency completely:**

```javascript
// FIXED CODE - STABLE ACROSS SESSIONS
function extractUniqueProductId(element, productName, productUrl, index = 0) {
  // Removed globalProductCounter to ensure stable IDs across scraping sessions
  
  // Priority 1: URL-based (most stable)
  if (productUrl) {
    const urlMatch = productUrl.match(/\/([^\/]+)\.html$/);
    if (urlMatch) {
      const urlSlug = urlMatch[1];
      const numericMatch = urlSlug.match(/(\d+)$/);
      if (numericMatch) {
        return `swsg-url-${numericMatch[1]}`; // ✅ Stable
      }
      const urlHash = urlSlug.split('').reduce((hash, char) => {
        return ((hash << 5) - hash) + char.charCodeAt(0);
      }, 0);
      return `swsg-url-${Math.abs(urlHash)}`; // ✅ Stable
    }
  }
  
  // Priority 2: Name-based (stable)
  if (productName) {
    let hash = 0;
    const cleanName = productName.toLowerCase().replace(/[^a-z0-9]/g, '');
    for (let i = 0; i < cleanName.length; i++) {
      const char = cleanName.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return `swsg-name-${Math.abs(hash)}`; // ✅ Stable - no timestamp/counter
  }
  
  // Priority 3: Fallback (deterministic)
  const fallbackHash = (productUrl || 'unknown').split('').reduce((hash, char) => {
    return ((hash << 5) - hash) + char.charCodeAt(0);
  }, index);
  return `swsg-fallback-${Math.abs(fallbackHash)}`; // ✅ Stable
}
```

### 2. **Added Database-Level Duplicate Prevention**

```javascript
// NEW SAFETY LAYER
async function checkExistingProduct(productName, brand, storeId) {
  const existingProducts = await storage.executeQuery(`
    SELECT id, sku, external_id, updated_at 
    FROM products 
    WHERE LOWER(name) = LOWER($1) 
    AND LOWER(brand) = LOWER($2) 
    AND store_id = $3
    ORDER BY updated_at DESC
    LIMIT 1
  `, [productName, brand || '', storeId]);
  
  return existingProducts.length > 0 ? existingProducts[0] : null;
}

// Integrated into product saving logic
const existingProduct = await checkExistingProduct(product.name, product.brand, dbStore.id);
if (existingProduct) {
  console.log(`🔄 Found existing product: ${product.name}, updating instead of creating new`);
  // Update existing product with existing SKU/external_id
  savedProduct = await storage.upsertProductBySku({
    // ... product data ...
    sku: existingProduct.sku, // Keep existing SKU
    externalId: existingProduct.external_id, // Keep existing external_id
  });
}
```

### 3. **Created Safe Cleanup Script**

`safe-swsg-cleanup.js` handles foreign key constraints properly:
- Transfers notifications, price history, comparisons to kept product
- Safely deletes duplicate products
- Provides detailed reporting

## 🧪 Testing Results

### ID Generation Stability Test ✅
```
✅ URL with numeric ID: Stable ID "swsg-url-123"
✅ URL without numeric ID: Stable ID "swsg-url-6927187519"  
✅ No URL, name-based ID: Stable ID "swsg-name-2076932061"
✅ Complex product name: Stable ID "swsg-name-2042376333"
✅ Arabic product name: Stable ID "swsg-name-108016116"
```

### SKU Generation Stability Test ✅
```
✅ Samsung product: Stable SKU "SWSG-SAMSUNG-GALAXY S21-url-123"
✅ Philips product: Stable SKU "SWSG-PHILIPS-XC8043/61-name-2042376333"
```

**Result**: All IDs are now stable across multiple scraping sessions!

## 🎯 Implementation Status

- [x] **Analyze SWSG Duplicate Root Cause**: globalProductCounter identified
- [x] **Fix SWSG Unique ID Generation**: Removed globalProductCounter dependency  
- [x] **Implement Database-Level Duplicate Prevention**: Added existing product checks
- [x] **Verify Database Constraints**: Confirmed unique constraints in place
- [x] **Test SWSG Scraper Fixes**: Verified ID generation stability
- [/] **Clean Existing SWSG Duplicates**: Script ready, pending database access

## 🔒 Multiple Protection Layers

1. **Stable ID Generation**: Deterministic algorithms ensure same product = same ID
2. **Database-Level Checks**: Query existing products before creating new ones
3. **Database Constraints**: Schema-level unique constraints on external_id and (sku, store_id)
4. **Session-Level Deduplication**: Existing in-memory duplicate prevention
5. **Safe Cleanup**: Foreign key-aware duplicate removal

## 📋 Next Steps

1. **Run Cleanup**: Execute `safe-swsg-cleanup.js` when database is accessible
2. **Monitor Results**: Verify no new duplicates in subsequent scraping runs
3. **Performance Check**: Confirm improved database performance
4. **Documentation**: Update scraper documentation with new logic

## 🎉 Expected Outcome

After cleanup and with the fixes in place:
- **Zero new duplicates** will be created in future scraping sessions
- **Existing duplicates** will be safely removed while preserving data integrity
- **Database performance** will improve with fewer duplicate records
- **Data consistency** will be maintained across all scraping operations

The SWSG scraper duplicate issue has been comprehensively resolved with multiple layers of protection.
