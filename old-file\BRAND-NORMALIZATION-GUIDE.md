# ProductPricePro Brand Normalization System

## Overview

The Brand Normalization System is a comprehensive solution for cleaning and standardizing brand data across the ProductPricePro database. It addresses inconsistencies caused by scraping from multiple stores and improves data quality for better search functionality and analytics.

## System Components

### 1. Brand Analysis (`brand-analysis.js`)
- Analyzes current brand data and identifies consolidation opportunities
- Uses fuzzy string matching and similarity algorithms
- Generates detailed reports and consolidation mappings

### 2. Brand Migration (`brand-migration.js`)
- Performs safe database migrations with full audit trail
- Supports batch processing and transaction rollback
- Creates automatic backup and rollback scripts

### 3. Brand Validation (`brand-validation.js`)
- Validates data quality post-migration
- Detects anomalies and generates quality reports
- Provides recommendations for further improvements

### 4. Master Orchestrator (`brand-normalization-master.js`)
- Coordinates all phases of the normalization process
- Provides interactive and automated execution modes
- Generates comprehensive executive reports

## Quick Start

### Prerequisites
```bash
# Ensure Node.js dependencies are installed
npm install

# Verify database connectivity
node -e "import('./storage.js').then(s => s.storage.executeQuery('SELECT 1'))"
```

### Basic Usage

#### 1. Run Complete Normalization (Recommended)
```bash
# Dry run first (recommended)
node scripts/brand-normalization-master.js --dry-run

# Live migration after reviewing dry run results
node scripts/brand-normalization-master.js
```

#### 2. Run Individual Components
```bash
# Analysis only
node scripts/brand-analysis.js

# Migration only (requires analysis results)
node scripts/brand-migration.js

# Validation only
node scripts/brand-validation.js
```

## Command Line Options

### Master Script Options
```bash
--dry-run                 # Preview changes without applying them
--non-interactive         # Skip user confirmations
--skip-analysis          # Skip analysis phase
--skip-migration         # Skip migration phase  
--skip-validation        # Skip validation phase
--batch-size=1000        # Set batch size for processing
--log-level=info         # Set logging level (debug, info, warn, error)
```

### Migration Script Options
```bash
--dry-run                # Preview mode
--batch-size=1000        # Batch size for updates
--no-backup             # Skip backup creation
--mapping=file.json     # Use specific mapping file
--log-level=debug       # Detailed logging
```

## Execution Workflow

### Phase 1: Analysis
1. **Data Collection**: Gathers all brand names and product counts
2. **Similarity Analysis**: Uses Levenshtein distance and fuzzy matching
3. **Consolidation Mapping**: Creates canonical brand mappings
4. **Report Generation**: Produces analysis reports and CSV files

### Phase 2: Migration
1. **Pre-Migration Validation**: Checks database state and connectivity
2. **Backup Creation**: Creates full backup of current brand data
3. **Batch Processing**: Updates brands in configurable batches
4. **Transaction Safety**: Uses database transactions with rollback capability
5. **Audit Trail**: Logs all changes with timestamps

### Phase 3: Validation
1. **Data Integrity Check**: Verifies no data loss occurred
2. **Quality Analysis**: Analyzes brand consistency and patterns
3. **Anomaly Detection**: Identifies remaining data quality issues
4. **Recommendations**: Provides actionable improvement suggestions

## Safety Features

### Backup and Recovery
- **Automatic Backup**: Creates JSON backup before migration
- **Rollback Scripts**: Generates SQL scripts for emergency reversal
- **Transaction Safety**: All updates wrapped in database transactions

### Validation and Testing
- **Dry Run Mode**: Preview all changes without applying them
- **Batch Processing**: Prevents database locks with configurable batch sizes
- **Pre/Post Validation**: Comprehensive integrity checks

### Error Handling
- **Graceful Failures**: Continues processing despite individual errors
- **Comprehensive Logging**: Detailed logs with timestamps and context
- **Recovery Guidance**: Clear error messages and recovery instructions

## Output Reports

### Analysis Reports
- `brand-analysis-summary-{timestamp}.json` - Overall analysis results
- `brand-consolidation-mapping-{timestamp}.json` - Detailed mappings
- `brand-consolidation-review-{timestamp}.csv` - Manual review format

### Migration Reports
- `brand-migration-report-{timestamp}.json` - Complete migration log
- `brand-migration-rollback-{timestamp}.sql` - Emergency rollback script

### Validation Reports
- `brand-quality-report-{timestamp}.json` - Comprehensive quality analysis
- `brand-quality-summary-{timestamp}.md` - Executive summary

### Master Reports
- `brand-normalization-master-report-{timestamp}.json` - Complete process log
- `brand-normalization-executive-summary-{timestamp}.md` - Executive overview

## Expected Results

### Typical Improvements
- **Brand Reduction**: 30-50% reduction in unique brand count
- **Data Quality**: 95%+ brand coverage with consistent formatting
- **Search Accuracy**: Improved filtering and search functionality
- **Analytics**: Better brand-based reporting and insights

### Success Metrics
- Zero data loss (100% product retention)
- Consistent brand naming across all stores
- Reduced duplicate brands from case/formatting variations
- Improved user experience in brand filtering

## Troubleshooting

### Common Issues

#### Database Connection Errors
```bash
# Check database connectivity
node -e "import('./storage.js').then(s => s.storage.executeQuery('SELECT 1'))"

# Verify database credentials in storage.js
```

#### Memory Issues with Large Datasets
```bash
# Reduce batch size
node scripts/brand-normalization-master.js --batch-size=500

# Increase Node.js memory limit
node --max-old-space-size=4096 scripts/brand-normalization-master.js
```

#### Migration Failures
```bash
# Check rollback script in reports/brand-migration/
# Run rollback if needed:
mysql -u username -p database < brand-migration-rollback-{timestamp}.sql
```

### Recovery Procedures

#### Emergency Rollback
1. Locate rollback script in `reports/brand-migration/`
2. Review rollback commands before execution
3. Execute rollback script: `mysql -u user -p db < rollback.sql`
4. Verify data restoration with validation script

#### Partial Migration Recovery
1. Check migration log for last successful batch
2. Identify failed operations in audit trail
3. Manual correction or re-run from checkpoint
4. Validate data integrity after recovery

## Best Practices

### Before Migration
1. **Always run dry-run first** to preview changes
2. **Review analysis reports** manually for accuracy
3. **Backup database** independently of script backup
4. **Test on database copy** for large datasets
5. **Schedule during low-traffic periods**

### During Migration
1. **Monitor progress** through detailed logs
2. **Don't interrupt** batch processing
3. **Check system resources** (memory, disk space)
4. **Have rollback plan ready** for emergencies

### After Migration
1. **Run validation immediately** after migration
2. **Test application functionality** thoroughly
3. **Monitor user feedback** for any issues
4. **Keep reports** for audit trail
5. **Document any manual corrections** made

## Integration with ProductPricePro

### Frontend Impact
- **StoreLogoFilter.tsx**: Improved brand filtering accuracy
- **Search functionality**: Better brand-based search results
- **Product listings**: Consistent brand display

### Backend Impact
- **Database queries**: More efficient brand-based queries
- **Analytics**: Accurate brand-based reporting
- **API responses**: Consistent brand data across endpoints

### Maintenance
- **Regular validation**: Run validation monthly
- **New store integration**: Re-run normalization when adding stores
- **Data quality monitoring**: Set up alerts for brand inconsistencies

## Support and Maintenance

### Monitoring
```bash
# Regular quality checks
node scripts/brand-validation.js

# Monthly brand analysis
node scripts/brand-analysis.js
```

### Updates
- Update similarity algorithms as needed
- Add new brand patterns for emerging brands
- Enhance validation rules based on findings

### Documentation
- Keep execution logs for audit purposes
- Document any manual corrections
- Update procedures based on lessons learned

## Contact and Support

For issues or questions regarding the Brand Normalization System:
1. Check troubleshooting section above
2. Review generated reports for detailed error information
3. Consult migration logs for specific failure points
4. Use rollback procedures if immediate recovery needed

The system is designed to be safe, comprehensive, and maintainable for long-term brand data quality management in ProductPricePro.
