# Price Comparison System for ProductPricePro

A comprehensive price comparison and monitoring system that automatically checks current prices of products in your database against actual store prices, identifies discrepancies, and generates detailed reports.

## 🎯 Overview

The Price Comparison System follows the same safety patterns, reporting structure, and testing approach as the existing brand normalization system. It provides:

- **Real-time price checking** from store websites
- **Intelligent product matching** using model numbers, SKUs, and fuzzy name matching
- **Comprehensive discrepancy reporting** with accuracy metrics
- **Safety patterns** including dry-run mode, backup creation, and validation
- **Multi-format reporting** (JSON, HTML, CSV)
- **CLI interface** for easy operation

## 🏗️ Architecture

### Core Components

1. **PriceComparisonEngine** - Main orchestrator
2. **PriceChecker** - Real-time price fetching from store websites
3. **ProductMatcher** - Intelligent product matching between database and websites
4. **PriceComparisonReporter** - Comprehensive reporting system
5. **CLI Interface** - Command-line interface for operations

### Supported Stores

The system **dynamically discovers and supports all active stores** in your ProductPricePro database, including:

- Extra (extra.com)
- Almanea (almanea.com)
- SWSG (swsg.com)
- BlackBox (blackbox.com.sa)
- BH Store (bhstore.com.sa)
- Alkhunaizan (alkhunaizan.sa)
- Tamkeen (tamkeenstores.com.sa)
- Zagzoog (zagzoog.com)
- Bin Momen (binmomen.com.sa)
- **Any other stores configured in your database**

The system automatically detects all active stores from the database and configures appropriate price extraction patterns for each store.

## 📦 Installation

### Prerequisites

- Node.js 16+ 
- PostgreSQL database
- Existing ProductPricePro application

### Setup

1. **Install Dependencies** (if not already installed):
```bash
npm install axios jsdom pg dotenv
```

2. **Setup Database Schema**:
```bash
# Dry-run first to see what will be created
node price-comparison-cli.js setup --dry-run

# Execute the schema
node price-comparison-cli.js setup
```

3. **Environment Variables**:
Ensure your `.env` file contains:
```env
DATABASE_URL=postgresql://username:password@localhost:5432/productpricepro
DB_SSL=false
```

## 🚀 Quick Start

### 1. List Available Stores

```bash
# See all stores configured in your system
node price-comparison-cli.js stores
```

### 2. Run Your First Price Comparison (Dry Run)

```bash
# Test with all stores and a small subset of products
node price-comparison-cli.js compare --dry-run --max-products 10

# Test with specific stores
node price-comparison-cli.js compare --dry-run --stores extra,almanea,bhstore --max-products 10
```

### 3. Run Live Comparison

```bash
# Run comparison for all stores with default settings
node price-comparison-cli.js compare

# Run with custom settings
node price-comparison-cli.js compare --threshold 10 --batch-size 50 --with-report

# Run for specific stores only
node price-comparison-cli.js compare --stores extra,almanea,bhstore,alkhunaizan
```

### 4. Generate Reports

```bash
# Generate report for latest run
node price-comparison-cli.js report --latest

# Generate HTML-only report
node price-comparison-cli.js report --latest --format html
```

### 5. Run Tests

```bash
# Run all tests
node price-comparison-cli.js test

# Test specific component
node price-comparison-cli.js test --component price-checker
```

## 📋 Usage Guide

### Command Line Interface

The system provides a comprehensive CLI following the same patterns as your brand normalization system:

```bash
node price-comparison-cli.js <command> [options]
```

#### Available Commands

**compare** - Run price comparison
- `--dry-run` - Safe mode, no database changes
- `--stores <list>` - Specific stores (comma-separated)
- `--threshold <percent>` - Price difference threshold (default: 5%)
- `--batch-size <number>` - Products per batch (default: 100)
- `--max-products <number>` - Limit products to check
- `--with-report` - Generate report after completion

**report** - Generate comprehensive reports
- `--latest` - Use latest completed run
- `--run-id <id>` - Specific run ID
- `--format <type>` - json, html, csv, or all
- `--output <directory>` - Output directory

**test** - Run test suite
- `--component <name>` - Test specific component only

**setup** - Setup database schema
- `--dry-run` - Preview changes without executing

### Safety Features

Following your brand normalization patterns:

1. **Dry-Run Mode**: Test operations without making changes
2. **Backup Creation**: Automatic backup before live operations
3. **Validation Steps**: Multi-phase validation and error checking
4. **Comprehensive Logging**: Detailed logs with timestamps
5. **Error Recovery**: Graceful handling of failures

### Configuration Options

```javascript
const options = {
  dryRun: false,                    // Safe mode
  stores: ['extra', 'almanea'],     // Target stores
  priceThreshold: 5.0,              // 5% discrepancy threshold
  batchSize: 100,                   // Products per batch
  maxProducts: 0,                   // 0 = no limit
  timeout: 15000,                   // Request timeout (ms)
  retryCount: 3,                    // Retry failed requests
  cacheTimeout: 300000              // Cache duration (5 minutes)
};
```

## 📊 Reports and Analytics

### Report Types

1. **JSON Reports** - Machine-readable data
2. **HTML Reports** - Human-readable dashboards
3. **CSV Reports** - Spreadsheet-compatible data

### Key Metrics

- **Accuracy Rate** - Percentage of products with correct prices
- **Price Discrepancies** - Products with significant price differences
- **Store Performance** - Per-store accuracy and error rates
- **Trend Analysis** - Price accuracy trends over time
- **Top Issues** - Most problematic brands/categories

### Sample Report Structure

```json
{
  "metadata": {
    "runId": "run_1234567890_abc123",
    "runDate": "2024-01-15T10:30:00Z",
    "stores": ["extra", "almanea"],
    "configuration": {...}
  },
  "summary": {
    "productsChecked": 1250,
    "priceDiscrepancies": 63,
    "accuracyPercentage": 94.96,
    "errors": 12
  },
  "storeAnalysis": [...],
  "discrepancies": [...],
  "recommendations": [...]
}
```

## 🧪 Testing

### Test Suite Components

1. **Price Checker Tests** - URL fetching, price extraction, error handling
2. **Product Matcher Tests** - Model extraction, SKU comparison, similarity scoring
3. **Comparison Engine Tests** - Configuration, dry-run mode, statistics
4. **Reporter Tests** - Report generation, CSV export, recommendations
5. **Integration Tests** - End-to-end workflow, performance validation

### Running Tests

```bash
# Run all tests
node price-comparison-cli.js test

# Test individual components
node price-comparison-cli.js test --component price-checker
node price-comparison-cli.js test --component product-matcher
node price-comparison-cli.js test --component comparison-engine
node price-comparison-cli.js test --component reporter
node price-comparison-cli.js test --component integration
```

## 🔧 Advanced Configuration

### Product Matching

The system uses intelligent matching with configurable weights:

```javascript
const matcherOptions = {
  similarityThreshold: 0.8,    // Minimum match confidence
  modelNumberWeight: 0.4,      // Model number importance
  skuWeight: 0.3,              // SKU importance  
  nameWeight: 0.2,             // Name similarity importance
  brandWeight: 0.1             // Brand matching importance
};
```

### Store-Specific Price Selectors

Each store has configurable CSS selectors for price extraction:

```javascript
const storeConfig = {
  extra: {
    priceSelectors: ['.price-current', '.jood-price'],
    regularPriceSelectors: ['.price-was', '.standard-price']
  }
  // ... other stores
};
```

## 📈 Performance Optimization

### Batch Processing
- Configurable batch sizes for memory management
- Rate limiting between requests to avoid overwhelming stores
- Concurrent processing with safety limits

### Caching
- 5-minute default cache for price checks
- Configurable cache duration
- Cache invalidation and cleanup

### Database Optimization
- Indexed tables for fast queries
- Bulk operations for large datasets
- Connection pooling for efficiency

## 🛡️ Error Handling

### Retry Logic
- Configurable retry count for failed requests
- Exponential backoff for rate limiting
- Graceful degradation on persistent failures

### Validation
- URL accessibility checks
- Price logic validation (regular_price >= price)
- Data integrity verification

### Monitoring
- Comprehensive error logging
- Performance metrics tracking
- Alert generation for high error rates

## 🔄 Integration with Existing Systems

### Brand Normalization Integration
- Uses existing brand standardization mappings
- Follows same safety and reporting patterns
- Compatible with existing database schema

### Store Scraper Integration
- Leverages existing scraper configurations
- Reuses authentication and request patterns
- Maintains consistency with current data flow

## 📅 Maintenance and Monitoring

### Regular Operations

1. **Daily Price Checks**:
```bash
# Automated daily comparison
node price-comparison-cli.js compare --threshold 5 --with-report
```

2. **Weekly Comprehensive Reports**:
```bash
# Generate detailed analytics
node price-comparison-cli.js report --latest --format all
```

3. **Monthly System Tests**:
```bash
# Validate system health
node price-comparison-cli.js test
```

### Monitoring Recommendations

- Set up alerts for accuracy rates below 90%
- Monitor error rates and investigate spikes
- Review discrepancy patterns for systematic issues
- Update store selectors when websites change

## 🆘 Troubleshooting

### Common Issues

1. **High Error Rates**
   - Check store website accessibility
   - Verify CSS selectors are current
   - Review rate limiting settings

2. **Low Match Rates**
   - Adjust similarity thresholds
   - Review product naming conventions
   - Check model number extraction patterns

3. **Performance Issues**
   - Reduce batch sizes
   - Increase delays between requests
   - Check database connection limits

### Debug Mode

Enable verbose logging for troubleshooting:

```bash
# Run with detailed logging
DEBUG=price-comparison:* node price-comparison-cli.js compare --dry-run
```

## 📞 Support

For issues or questions:

1. Check the test suite output for component-specific problems
2. Review log files in the reports directory
3. Run dry-run mode to safely test configurations
4. Consult the existing brand normalization documentation for similar patterns

## 🔮 Future Enhancements

- Real-time price alerts via webhooks
- Machine learning for improved product matching
- API endpoints for web interface integration
- Historical price trend analysis
- Automated price update suggestions
