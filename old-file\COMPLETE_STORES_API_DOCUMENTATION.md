# Complete Stores API & Website Documentation - ProductPricePro

## 📋 Overview

This document provides comprehensive information about all 9 stores integrated into the ProductPricePro application, including their APIs, websites, authentication methods, and scraping configurations.

---

## 🏪 Store Configurations

### 1. BlackBox Store
- **Store Name**: BlackBox
- **Store Slug**: `blackbox`
- **Website**: `https://www.blackbox.com.sa`
- **Base URL**: `https://www.blackbox.com.sa`
- **API Base URL**: `https://api.ops.blackbox.com.sa/api/v1`
- **Products API**: `https://api.ops.blackbox.com.sa/api/v1/search/products/facets/category/{categoryId}`
- **Authentication**: Bearer <PERSON>ken required
- **Method**: API-based scraping
- **Categories**: 
  - Air Conditioners (175)
  - TVs & Audio (106) 
  - Home Appliances (218)
  - Small Appliances (174)
- **Scraper File**: `blackbox-scraper.js`
- **Expected Products**: ~2000

### 2. BH Store
- **Store Name**: BH Store
- **Store Slug**: `bhstore`
- **Website**: `https://bhstore.com.sa`
- **Base URL**: `https://bhstore.com.sa`
- **API Base URL**: `https://api.bhstore.com.sa`
- **Products API**: `https://api.bhstore.com.sa/commerce/products/`
- **Categories API**: `https://api.bhstore.com.sa/commerce/categories/`
- **API Key**: `2853152294a192f18c3da51ae965f`
- **Authentication**: X-API-Key header
- **Method**: API-based scraping
- **Pagination**: 30 items per page
- **Headers Required**:
  ```json
  {
    "accept": "application/json, text/plain, */*",
    "interface": "user",
    "language": "en",
    "store": "1",
    "x-api-key": "2853152294a192f18c3da51ae965f",
    "Referer": "https://bhstore.com.sa/",
    "origin": "https://bhstore.com.sa"
  }
  ```
- **Scraper File**: `fetch-bhstore-to-db.js` (alias: `bhstore-scraper.js`)
- **Expected Products**: ~1300

### 3. Alkhunaizan Store
- **Store Name**: Alkhunaizan
- **Store Slug**: `alkhunaizan`
- **Website**: `https://www.alkhunaizan.sa`
- **Base URL**: `https://www.alkhunaizan.sa`
- **API Base URL**: `https://api.alkhn.com`
- **Products API**: `https://api.alkhn.com/api/v1/search/products/facets/category/{categoryId}`
- **Authentication**: Custom API headers required
- **Method**: API-based scraping
- **Categories**:
  - Television (324)
  - Air Condition (6)
  - Washing machines (32)
  - Dryer (55)
  - Refrigerator (30)
  - Freezer (31)
  - Dishwasher (179)
- **API Parameters**:
  ```json
  {
    "pageNo": 1,
    "pageSize": 100,
    "sortBy": "position",
    "sortDir": "ASC",
    "region": "all"
  }
  ```
- **Scraper File**: `alkhunaizan-scraper.js`
- **Expected Products**: ~3000

### 4. Tamkeen Stores
- **Store Name**: Tamkeen
- **Store Slug**: `tamkeen`
- **Website**: `https://www.tamkeen.sa`
- **Base URL**: `https://www.tamkeen.sa`
- **Products API**: `https://www.tamkeen.sa/api/products/{categorySlug}/{region}`
- **Region**: `riyadh`
- **Authentication**: Session-based
- **Method**: API-based scraping
- **Scraper File**: `tamkeen-scraper.js`
- **Expected Products**: ~900

### 5. Zagzoog Store
- **Store Name**: Zagzoog
- **Store Slug**: `zagzoog`
- **Website**: `https://zagzoog.com`
- **Base URL**: `https://zagzoog.com`
- **Products API**: `https://zagzoog.com/english/ajax_products.php?s=all`
- **Authentication**: None required
- **Method**: AJAX-based scraping with custom headers
- **Headers Required**: AJAX headers
- **Scraper File**: `zagzoog-scraper.js`
- **Expected Products**: ~250

### 6. Bin Momen Store
- **Store Name**: Bin Momen
- **Store Slug**: `binmomen`
- **Website**: `https://www.binmomen.com.sa`
- **Base URL**: `https://www.binmomen.com.sa`
- **Products API**: `https://www.binmomen.com.sa/{categorySlug}`
- **Authentication**: None required
- **Method**: Web scraping with custom parameters
- **Scraper File**: `binmomen-scraper.js`
- **Expected Products**: ~1000

### 7. SWSG Store
- **Store Name**: SWSG
- **Store Slug**: `swsg`
- **Website**: `https://swsg.co/en/`
- **Base URL**: `https://swsg.co`
- **English URL**: `https://swsg.co/en`
- **Authentication**: Session-based
- **Method**: Web scraping
- **Store ID**: 6
- **Scraper File**: `swsg-scraper.js`
- **Expected Products**: ~500

### 8. Extra Store
- **Store Name**: Extra
- **Store Slug**: `extra`
- **Website**: `https://extra.com`
- **Base URL**: `https://extra.com`
- **Authentication**: API key required
- **Method**: Web scraping with API integration
- **Price Selectors**:
  - `.price-current`
  - `.price-now`
  - `[data-testid="price-current"]`
  - `.jood-price`
  - `.member-price`
  - `.price-value`
  - `.current-price`
- **Scraper File**: `extra-scraper.js`
- **Expected Products**: ~11000

### 9. Almanea Store (Additional)
- **Store Name**: Almanea
- **Store Slug**: `almanea`
- **Website**: `https://www.almanea.sa/en/`
- **Base URL**: `https://www.almanea.sa`
- **API Base URL**: `https://api-preprod.dev-almanea.com`
- **Authentication**: Token-based
- **Method**: API-based scraping
- **API Endpoints**:
  - Category Tree: `/api/category/tree?lang=en`
  - Product Search: `/api/v1/facets/categoryV2`
  - Auth Token: `/api/v1/auth/token`
  - Regions: `/api/v1/getRegions/lang=en`
- **Languages**: English (en), Arabic (ar)
- **Scraper File**: `almanea-scraper.js` (if implemented)

---

## 🔧 Technical Configuration

### Database Store IDs
```sql
1. BH Store (bhstore)
2. Alkhunaizan (alkhunaizan)  
3. Tamkeen (tamkeen)
4. Zagzoog (zagzoog)
5. Bin Momen (binmomen)
6. SWSG (swsg)
7. BlackBox (blackbox)
8. Extra (extra)
9. Almanea (almanea)
```

### Authentication Methods Summary
- **API Key**: BH Store, Extra
- **Bearer Token**: BlackBox, Almanea
- **Custom Headers**: Alkhunaizan
- **Session-based**: Tamkeen, SWSG
- **No Authentication**: Zagzoog, Bin Momen

### Scraping Methods Summary
- **API-based**: BlackBox, BH Store, Alkhunaizan, Tamkeen, Almanea
- **Web Scraping**: SWSG, Bin Momen, Extra
- **AJAX-based**: Zagzoog

---

## 📊 Production Configuration

### Enabled Stores in Production
```json
{
  "stores": {
    "enabled": [
      "bhstore",
      "alkhunaizan", 
      "binmomen",
      "zagzoog",
      "tamkeen",
      "extra",
      "swsg",
      "blackbox"
    ],
    "refreshInterval": "24h",
    "concurrent": false
  }
}
```

### Expected Total Products
- **Total Expected**: ~20,000+ products
- **Largest Store**: Extra (~11,000)
- **Smallest Store**: Zagzoog (~250)

---

## 🔍 Health Check URLs

### Store Accessibility Test URLs
```javascript
const HEALTH_CHECK_URLS = {
  bhstore: 'https://bhstore.com.sa/en/',
  alkhunaizan: 'https://www.alkhunaizan.sa',
  tamkeen: 'https://www.tamkeen.sa', 
  zagzoog: 'https://zagzoog.com',
  binmomen: 'https://www.binmomen.com.sa',
  swsg: 'https://swsg.co/en/',
  blackbox: 'https://www.blackbox.com.sa',
  extra: 'https://extra.com',
  almanea: 'https://www.almanea.sa/en/'
};
```

---

## 🚀 Usage Examples

### Running Individual Store Scrapers
```bash
# BH Store
node fetch-bhstore-to-db.js

# Alkhunaizan
node alkhunaizan-scraper.js

# Tamkeen
node tamkeen-scraper.js

# Zagzoog
node zagzoog-scraper.js

# Bin Momen
node binmomen-scraper.js

# SWSG
node swsg-scraper.js

# BlackBox
node blackbox-scraper.js

# Extra
node extra-scraper.js
```

### Running All Stores
```bash
# All stores concurrently
node storeScraper.js

# With monitoring
node start-fetch-simple.js
```

---

## 📝 Notes

### Store Status
- ✅ **Active**: All 8 main stores are active and functional
- 🔄 **In Development**: Almanea store (configuration ready)
- 📊 **Performance**: Extra store has the highest product count
- 🔧 **Maintenance**: Regular updates needed for API changes

### Common Issues
- **Authentication Expiry**: BlackBox, Almanea tokens may expire
- **Rate Limiting**: BH Store, Extra may have rate limits
- **Website Changes**: Web scrapers may need updates for DOM changes
- **API Changes**: API-based scrapers may need endpoint updates

This documentation provides complete information for all stores integrated into the ProductPricePro application.
