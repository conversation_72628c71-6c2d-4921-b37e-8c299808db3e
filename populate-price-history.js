#!/usr/bin/env node

import 'dotenv/config';
import pg from 'pg';

const pool = new pg.Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false
});

async function populatePriceHistory() {
  try {
    console.log('🔧 POPULATING PRICE HISTORY DATA');
    console.log('='.repeat(50));

    // Step 1: Check current state
    console.log('\n1️⃣ Checking current state...');
    
    const priceHistoryCount = await pool.query(`SELECT COUNT(*) as count FROM price_history`);
    console.log(`Price History table: ${priceHistoryCount.rows[0]?.count || 0} records`);

    const productPricesCount = await pool.query(`SELECT COUNT(*) as count FROM product_prices`);
    console.log(`Product Prices table: ${productPricesCount.rows[0]?.count || 0} records`);

    // Step 2: Get sample products with price data
    console.log('\n2️⃣ Finding products with price data...');
    
    const productsWithPrices = await pool.query(`
      SELECT DISTINCT p.id, p.name, p.brand, p.price, p.store_id,
             COUNT(pp.id) as price_count
      FROM products p
      INNER JOIN product_prices pp ON p.id = pp.product_id
      GROUP BY p.id, p.name, p.brand, p.price, p.store_id
      HAVING COUNT(pp.id) > 0
      ORDER BY COUNT(pp.id) DESC
      LIMIT 10
    `);

    console.log(`Found ${productsWithPrices.rows.length} products with price data`);

    if (productsWithPrices.rows.length === 0) {
      console.log('❌ No products with price data found. Cannot populate price history.');
      return;
    }

    // Step 3: For each product, create price history from product_prices
    console.log('\n3️⃣ Creating price history from product_prices...');
    
    let totalCreated = 0;

    for (const product of productsWithPrices.rows) {
      console.log(`\n📊 Processing product ${product.id}: ${product.name?.substring(0, 40)}...`);
      
      // Get all price entries for this product
      const priceEntries = await pool.query(`
        SELECT price, currency, stock, status, timestamp
        FROM product_prices
        WHERE product_id = $1
        ORDER BY timestamp ASC
      `, [product.id]);

      console.log(`   Found ${priceEntries.rows.length} price entries`);

      let previousPrice = null;
      let createdForProduct = 0;

      for (const [index, entry] of priceEntries.rows.entries()) {
        let changeType = 'daily_snapshot';
        let changeAmount = null;
        let changePercentage = null;

        if (previousPrice !== null) {
          const currentPrice = parseFloat(entry.price);
          const prevPrice = parseFloat(previousPrice);
          changeAmount = currentPrice - prevPrice;
          
          if (Math.abs(changeAmount) > 0.01) {
            changeType = changeAmount > 0 ? 'price_increase' : 'price_decrease';
            changePercentage = (changeAmount / prevPrice) * 100;
          }
        }

        // Insert into price_history
        await pool.query(`
          INSERT INTO price_history (
            product_id, price, regular_price, stock, currency, 
            change_type, change_amount, change_percentage, previous_price, 
            recorded_at, created_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $10)
          ON CONFLICT DO NOTHING
        `, [
          product.id,
          entry.price,
          entry.price, // Use same as regular price
          entry.stock || 0,
          entry.currency || 'SAR',
          changeType,
          changeAmount,
          changePercentage,
          previousPrice,
          entry.timestamp
        ]);

        previousPrice = entry.price;
        createdForProduct++;
      }

      console.log(`   ✅ Created ${createdForProduct} price history entries`);
      totalCreated += createdForProduct;
    }

    // Step 4: Verify the results
    console.log('\n4️⃣ Verifying results...');
    
    const newPriceHistoryCount = await pool.query(`SELECT COUNT(*) as count FROM price_history`);
    console.log(`Price History table now has: ${newPriceHistoryCount.rows[0]?.count || 0} records`);
    console.log(`Created ${totalCreated} new price history entries`);

    // Test with a specific product
    const testProduct = productsWithPrices.rows[0];
    const testHistory = await pool.query(`
      SELECT COUNT(*) as count, MIN(recorded_at) as earliest, MAX(recorded_at) as latest
      FROM price_history
      WHERE product_id = $1
    `, [testProduct.id]);

    console.log(`\n🧪 Test product ${testProduct.id}:`);
    console.log(`   Price history entries: ${testHistory.rows[0]?.count || 0}`);
    console.log(`   Date range: ${testHistory.rows[0]?.earliest} to ${testHistory.rows[0]?.latest}`);

    console.log('\n✅ Price history population completed successfully!');

  } catch (error) {
    console.error('❌ Error populating price history:', error);
  } finally {
    await pool.end();
  }
}

populatePriceHistory();
