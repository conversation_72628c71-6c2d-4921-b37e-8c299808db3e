# Multi-Store Product Monitor

A comprehensive product monitoring and price tracking system that scrapes and monitors products from multiple e-commerce stores in Saudi Arabia.

## 🚀 Features

### 🏪 Multi-Store Support
Monitor products from 5 major Saudi Arabian stores:
- **BH Store** (bhstore.com.sa) - Electronics and appliances
- **Alkhunaizan** (alkhunaizan.com) - Home appliances and electronics
- **Tamkeen** (tamkeenstores.com.sa) - Electronics, gaming, and smart home
- **Zagzoog** (zagzoog.com) - Electronics and gadgets
- **Bin Momen** (binmomen.com.sa) - Home appliances and electronics

### 📊 Product Management
- **Comprehensive Product Data**: Name, model, brand, size, UOM, SKU, price, stock
- **Category Management**: Automatic categorization with manual override
- **Product Enhancement**: Automatic size and UOM extraction from product names
- **Duplicate Prevention**: Smart detection and prevention of duplicate products
- **Data Validation**: Automatic data cleaning and validation
- **Bulk Operations**: High-performance bulk database operations

### 💰 Price Tracking & Monitoring
- **Real-time Price Tracking**: Monitor current and regular prices
- **Price History**: Complete price change history with timestamps
- **Promotion Detection**: Automatic detection of sales and promotions
- **Price Change Notifications**: Instant alerts when prices change
- **Price Analytics**: Visual charts and trends analysis

### 🔔 Notification System
- **Real-time Notifications**: In-app notifications for price changes
- **Email Alerts**: Automated email notifications for significant changes
- **Smart Filtering**: Suppress notifications for 0% price changes
- **User Preferences**: Customizable notification settings per user
- **Notification History**: Complete audit trail of all notifications

### ⏰ Scheduling & Automation
- **Flexible Scheduling**: Cron-based scheduling for data fetching
- **Multiple Schedule Types**:
  - Product fetching schedules
  - Email report schedules
- **Schedule Management**: Enable/disable, modify, and monitor schedules
- **Automatic Recovery**: Failed tasks are automatically retried
- **Schedule Persistence**: Maintains exact next run times across restarts

### 📧 Email System
- **Automated Reports**: Scheduled product and price reports
- **Gmail Integration**: Built-in Gmail SMTP support
- **Email Templates**: Customizable email templates
- **Recipient Management**: Manage email recipients and preferences
- **Test Email Function**: Verify email configuration

### 👥 User Management & Authentication
- **Role-based Access Control**: Admin and user roles
- **Secure Authentication**: Session-based authentication with Passport.js
- **User Registration**: Admin can create new users
- **Profile Management**: User profile and preference management
- **Remember Me**: Persistent login sessions
- **Session Security**: Secure session management

### 🛠 Admin Features
- **Store Management**: Add, edit, and manage store configurations
- **Manual Data Fetching**: On-demand product fetching with progress tracking
- **Data Export**: Export products to Excel/CSV formats
- **System Monitoring**: Real-time system status and performance metrics
- **Error Handling**: Comprehensive error logging and reporting
- **Database Management**: Direct database operations and maintenance

### 📱 User Interface
- **Modern React UI**: Built with React 18 and TypeScript
- **Responsive Design**: Mobile-friendly interface with Tailwind CSS
- **Dark/Light Theme**: Theme switching support
- **Interactive Tables**: Advanced data tables with sorting and filtering
- **Real-time Updates**: Live data updates without page refresh
- **Progress Tracking**: Visual progress indicators for long operations

### 🔧 Data Processing & Enhancement
- **Automatic Data Fixes**: Built-in data cleaning and enhancement
- **Size & UOM Extraction**: Intelligent extraction from product names (ALL 5 STORES)
- **Multi-language Support**: Handles Arabic and English product descriptions
- **Category-specific Logic**: TVs (inches), ACs (BTU), Appliances (KG/Liters)
- **Model Number Cleaning**: Automatic removal of numeric suffixes
- **Category Normalization**: Consistent category naming across stores
- **Price Validation**: Automatic price validation and correction
- **Stock Monitoring**: Track product availability
- **Super Fast Fetching**: Concurrent processing of all stores
- **Bulk Database Operations**: Optimized for high-volume data processing

## 🏗 Tech Stack

### Frontend
- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **Radix UI** components
- **TanStack Query** for data fetching
- **React Hook Form** for form management
- **Recharts** for data visualization
- **Framer Motion** for animations

### Backend
- **Node.js** with Express
- **TypeScript** for type safety
- **Drizzle ORM** for database operations
- **PostgreSQL** database
- **Passport.js** for authentication
- **Node Schedule** for task scheduling
- **Nodemailer** for email functionality

### Data Scraping
- **Axios** for HTTP requests
- **Cheerio** for HTML parsing
- **JSDOM** for DOM manipulation
- **Custom scrapers** for each store
- **Retry logic** and error handling
- **Rate limiting** and respectful scraping

### Development Tools
- **Vite** for build tooling
- **ESBuild** for fast compilation
- **Drizzle Kit** for database migrations
- **Cross-env** for environment management

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- PostgreSQL database
- Gmail account for email features

### Linux Server Dependencies (Required for Puppeteer/Chrome)
If deploying on a Linux server, install these system dependencies:
```bash
sudo apt update && sudo apt install -y \
  libasound2t64 libatk1.0-0t64 libc6 libcairo2 libcups2t64 \
  libdbus-1-3 libexpat1 libfontconfig1 libgcc-s1 libglib2.0-0t64 \
  libgtk-3-0t64 libnspr4 libpango-1.0-0 libpangocairo-1.0-0 libstdc++6 \
  libx11-6 libx11-xcb1 libxcb1 libxcomposite1 libxcursor1 libxdamage1 \
  libxext6 libxfixes3 libxi6 libxrandr2 libxrender1 libxss1 libxtst6 \
  ca-certificates fonts-liberation libnss3 lsb-release xdg-utils wget
```

**Note**: These dependencies are required for Chrome/Puppeteer to run on headless Linux servers. Without them, you'll get errors like `libasound.so.2: cannot open shared object file`.

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd multi-store-monitor
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   Create a `.env` file with:
   ```env
   DATABASE_URL=postgresql://username:password@localhost:5432/database_name
   SESSION_SECRET=your-session-secret
   GMAIL_USER=<EMAIL>
   GMAIL_PASS=your-app-password
   NODE_ENV=development
   ```

4. **Database Setup**
   ```bash
   npm run db:push
   ```

5. **Start Development Server**
   ```bash
   npm run dev
   ```

6. **Access the Application**
   Open http://localhost:3021 in your browser

### Default Login Credentials
- **Username**: admin
- **Password**: admin

## 📖 Usage Guide

### Dashboard
- View system overview and statistics
- Monitor recent price and stock changes
- Quick access to all features

### Product Management
- Browse all products from all stores
- Filter by store, category, or search terms
- View detailed product information
- Export product data

### Product Comparison
- Compare products side-by-side useing MODEL
- Analyze price and stock differences
- Identify best deals


### Price Tracking
- Monitor price changes over time
- View price history charts
- Set up price alerts
- Analyze pricing trends

### Scheduling
- Set up automated data fetching
- Configure email reports
- Manage schedule timing
- Monitor schedule execution

### User Management (Admin Only)
- Create and manage user accounts
- Assign user roles
- Configure user permissions

### Settings
- Configure email settings
- Manage notification preferences
- System configuration options
- Clear database data
- Test email configuration
-fetch products from stores
- Update product data
-add new node code to fetch new stores Js code  or pythone code

## 🔧 API Endpoints

### Authentication
- `POST /api/login` - User login
- `POST /api/logout` - User logout
- `GET /api/user` - Get current user

### Products
- `GET /api/products` - List all products
- `GET /api/products/:id` - Get product details
- `GET /api/products/export` - Export products

### Stores
- `GET /api/stores` - List all stores
- `POST /api/admin/fetch-products` - Fetch products from stores

### Scheduling
- `GET /api/schedules` - List schedules
- `POST /api/schedules` - Create schedule
- `PUT /api/schedules/:id` - Update schedule
- `DELETE /api/schedules/:id` - Delete schedule

### Users (Admin Only)
- `GET /api/users` - List users
- `POST /api/users` - Create user
- `PUT /api/users/:id/role` - Update user role
- `DELETE /api/users/:id` - Delete user

## 🛠 Store Scrapers

### Supported Stores

#### BH Store
- **API Integration**: Uses official BH Store API
- **Categories**: Electronics, appliances, gaming
- **Features**: Real-time stock, pricing, product details

#### Alkhunaizan
- **Web Scraping**: Custom scraper for product pages
- **Categories**: Home appliances, electronics
- **Features**: Product specifications, pricing

#### Tamkeen
- **API Integration**: Uses Tamkeen partner API with Puppeteer
- **Categories**: 6 main categories (TVs, Air Conditioners, Large Appliances, Small Appliances, Personal Care, Built-in Cooking)
- **Features**: Real-time data fetching, multi-page support, fetches ALL pages
- **Recent Fix**: Fixed API data structure handling for productData.products.data
- **Performance**: Fetches 500+ products across all categories

#### Zagzoog
- **Web Scraping**: Advanced scraper with category support
- **Categories**: Electronics, gadgets, accessories
- **Features**: Product details, pricing, availability

#### Bin Momen
- **Web Scraping**: Custom scraper for product catalog
- **Categories**: Home appliances, electronics
- **Features**: Product information, pricing

### Scraper Features
- **Retry Logic**: Automatic retry on failures
- **Rate Limiting**: Respectful scraping practices
- **Error Handling**: Comprehensive error logging
- **Data Validation**: Automatic data cleaning
- **Bulk Operations**: High-performance database operations

## 📊 Database Schema

### Core Tables
- **users** - User accounts and authentication
- **stores** - Store configurations
- **categories** - Product categories
- **products** - Product information
- **product_prices** - Price history tracking
- **product_quantities** - Stock level tracking
- **notifications** - User notifications
- **schedules** - Automated task scheduling
- **settings** - System configuration
- **email_templates** - Email template management
- **email_recipients** - Email recipient lists

## 🔒 Security Features

- **Session-based Authentication**: Secure session management
- **Role-based Access Control**: Admin and user permissions
- **CSRF Protection**: Cross-site request forgery protection
- **Input Validation**: Comprehensive input sanitization
- **SQL Injection Prevention**: Parameterized queries
- **Secure Headers**: Security headers for web protection

## 📈 Performance Features

- **Bulk Database Operations**: High-performance data processing
- **Connection Pooling**: Efficient database connections
- **Caching**: Smart caching for frequently accessed data
- **Lazy Loading**: Efficient data loading strategies
- **Optimized Queries**: Performance-tuned database queries

## 🐛 Error Handling & Logging

- **Comprehensive Logging**: Detailed application logs
- **Error Recovery**: Automatic error recovery mechanisms
- **User-friendly Errors**: Clear error messages for users
- **Debug Information**: Detailed debugging for development
- **Performance Monitoring**: Application performance tracking

## 🔄 Data Processing Pipeline

1. **Data Fetching**: Automated scraping from all stores
2. **Data Validation**: Automatic validation and cleaning
3. **Data Enhancement**: Size, UOM, and category extraction
4. **Duplicate Detection**: Prevent duplicate products
5. **Price Tracking**: Monitor and record price changes
6. **Notification Generation**: Create alerts for changes
7. **Report Generation**: Automated email reports

## 🆕 Recent Major Improvements

### ✅ **Tamkeen Integration Fix (Latest)**
- **Fixed API Structure**: Resolved productData.products.data extraction
- **Multi-page Support**: Now fetches ALL pages (no artificial limits)
- **Real Product Data**: 500+ real products from Tamkeen API
- **Enhanced Debugging**: Comprehensive API response analysis
- **Performance**: Optimized for faster data processing

### ✅ **Size & UOM Enhancement (All Stores)**
- **Universal Implementation**: Size/UOM extraction for ALL 5 stores
- **During Fetch Process**: Extraction happens during product fetching
- **Arabic Support**: Enhanced handling for Arabic product names
- **Category-specific Logic**: Tailored extraction for different product types
- **Standardized Units**: Consistent UOM formats across all stores

### ✅ **Super Fast Performance**
- **Concurrent Processing**: All 5 stores processed simultaneously
- **Bulk Operations**: Optimized database operations for speed
- **Memory Management**: Efficient memory usage during large operations
- **Connection Pooling**: Optimized database connections
- **Progress Tracking**: Real-time progress monitoring

### ✅ **Fetch Button Enhancement**
- **All Stores Support**: Single button fetches all 5 stores
- **Real-time Progress**: Live updates during fetch process
- **Error Handling**: Robust error recovery and reporting
- **Automatic Fixes**: Data corrections applied during fetch
- **Complete Pipeline**: Fetch → Process → Extract Size/UOM → Save

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the documentation
- Review error logs
- Contact the development team
----
---

**Built with ❤️ for efficient e-commerce monitoring**
g