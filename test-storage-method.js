#!/usr/bin/env node

import 'dotenv/config';
import { storage } from './server/storage.js';

async function testStorageMethod() {
  try {
    console.log('🧪 TESTING STORAGE METHOD DIRECTLY');
    console.log('='.repeat(50));

    const productId = 9981;
    const periodDays = 30;
    
    // Calculate date range (same as API)
    const endDate = new Date();
    const startDate = new Date(Date.now() - periodDays * 24 * 60 * 60 * 1000);
    
    console.log(`📅 Date range:`);
    console.log(`   Start: ${startDate.toISOString()}`);
    console.log(`   End: ${endDate.toISOString()}`);
    console.log(`   Product ID: ${productId}`);
    console.log(`   Period: ${periodDays} days`);

    // Call the storage method directly
    console.log('\n🔍 Calling storage.getPriceHistoryByDateRange...');
    const priceHistory = await storage.getPriceHistoryByDateRange(
      productId,
      startDate,
      endDate
    );

    console.log(`\n📊 Results:`);
    console.log(`   Entries found: ${priceHistory.length}`);
    
    if (priceHistory.length > 0) {
      console.log(`   Sample entries:`);
      priceHistory.slice(0, 3).forEach((entry, index) => {
        console.log(`     ${index + 1}. ${entry.price} SAR - ${entry.changeType} - ${entry.recordedAt}`);
      });
    } else {
      console.log(`   ❌ No entries found`);
    }

    // Also test with a longer period
    console.log(`\n🔍 Testing with 365 days period...`);
    const longStartDate = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
    const longPriceHistory = await storage.getPriceHistoryByDateRange(
      productId,
      longStartDate,
      endDate
    );

    console.log(`📊 365-day Results:`);
    console.log(`   Entries found: ${longPriceHistory.length}`);

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testStorageMethod();
