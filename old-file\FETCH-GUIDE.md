# Product Fetch Monitoring Guide

## 🚀 Quick Start

### Option 1: Simple Command (Recommended)
```bash
# Windows
run-fetch.cmd

# Linux/Mac
chmod +x run-fetch.sh
./run-fetch.sh
```

### Option 2: Direct Node Commands

#### Simple Monitoring (Recommended)
```bash
node start-fetch-simple.js
```

#### Comprehensive Monitoring (Detailed)
```bash
node start-fetch-with-monitoring.js
```

#### Direct Scraper (Minimal)
```bash
node -e "import('./storeScraper.js').then(m => m.scrapeAllStores()).then(r => console.log('✅ Done:', r)).catch(e => console.error('❌ Error:', e))"
```

## 📊 Monitoring Levels

### 1. Simple Monitoring
- ✅ Basic error and warning counting
- ✅ Duration tracking
- ✅ Success/failure summary
- ✅ Lightweight and fast
- **Best for**: Regular fetch operations

### 2. Comprehensive Monitoring
- ✅ All simple monitoring features
- ✅ Real-time system resource monitoring
- ✅ Database health checks
- ✅ Categorized error tracking (constraint, network, parsing)
- ✅ Detailed error reports
- ✅ Store-specific error tracking
- **Best for**: Debugging and detailed analysis

### 3. Direct Scraper
- ✅ Minimal overhead
- ✅ Standard console output
- **Best for**: Production environments

## 🔍 What to Watch For

### ✅ Success Indicators
```
✅ Database connected
✅ [Store] scraping completed
✅ Products stored successfully
✅ FETCH COMPLETED SUCCESSFULLY!
```

### ⚠️ Warning Signs
```
⚠️ [Extra] Error storing product
⚠️ Database stats error
⚠️ Could not find existing product
⚠️ Duplicate key error (should be handled gracefully)
```

### ❌ Critical Errors
```
❌ Database health check failed
❌ Error upserting product by SKU
❌ Critical error in monitored fetch
💥 Uncaught Exception
```

## 📈 Expected Output

### Stores Being Scraped
1. **BlackBox** - Electronics and appliances
2. **Alkhunaizan** - Home appliances
3. **Tamkeen** - Various products
4. **Zagzoog** - Electronics
5. **SWSG** - Air conditioning and appliances
6. **Extra** - Electronics and appliances

### Typical Process Flow
```
🚀 Starting product fetch
🔍 Pre-fetch system checks
✅ Database connected
🏪 Starting [STORE] store scraping
📦 Found X products on page Y
💾 Storing products in database
✅ [STORE] scraping completed
📧 Starting comprehensive post-fetch processing
✅ FETCH COMPLETED SUCCESSFULLY!
```

## 🛠️ Troubleshooting

### If Fetch Fails to Start
1. Check Node.js version: `node --version` (should be 18+)
2. Check if dependencies are installed: `npm install`
3. Check database connection
4. Check if server is running

### If Errors Occur During Fetch
1. **Constraint Errors**: Normal - handled by enhanced error recovery
2. **Network Errors**: Check internet connection and store websites
3. **Parsing Errors**: May indicate website structure changes
4. **Database Errors**: Check database connectivity and schema

### If Fetch Hangs
1. Check network connectivity
2. Check if specific store websites are accessible
3. Monitor system resources (memory/CPU)
4. Use Ctrl+C to stop and check logs

## 📊 Performance Expectations

### Typical Duration
- **Small fetch** (testing): 5-15 minutes
- **Full fetch** (all stores): 30-90 minutes
- **Large fetch** (comprehensive): 1-3 hours

### Resource Usage
- **Memory**: 200-500MB typical
- **CPU**: Moderate during scraping
- **Network**: Continuous HTTP requests
- **Database**: Frequent write operations

## 💡 Best Practices

### Before Starting
1. ✅ Ensure stable internet connection
2. ✅ Check database is accessible
3. ✅ Verify sufficient disk space
4. ✅ Close unnecessary applications

### During Fetch
1. ✅ Monitor console output for errors
2. ✅ Don't interrupt unless critical error
3. ✅ Check system resources if slow
4. ✅ Note any recurring error patterns

### After Completion
1. ✅ Review error summary
2. ✅ Check database for new products
3. ✅ Verify price updates worked
4. ✅ Address any critical errors found

## 🎯 Success Criteria

### Fetch is Successful If:
- ✅ All stores complete without critical errors
- ✅ Products are stored in database
- ✅ Price records are created
- ✅ No uncaught exceptions
- ✅ Final success message appears

### Fetch Needs Attention If:
- ⚠️ High number of constraint errors (>100)
- ⚠️ Network timeouts for specific stores
- ⚠️ Parsing errors indicating website changes
- ⚠️ Database connection issues

---

**Ready to start? Run one of the commands above and monitor the console output!**
