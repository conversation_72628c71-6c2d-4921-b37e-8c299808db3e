-- Migration: Add missing columns to price_history table
-- This migration adds missing columns to support comprehensive price tracking

-- Add member_price column to price_history table
ALTER TABLE price_history
ADD COLUMN IF NOT EXISTS member_price DECIMAL(10, 2);

-- Add stock_status column to price_history table
ALTER TABLE price_history
ADD COLUMN IF NOT EXISTS stock_status VARCHAR(50);

-- Add quantity_available column to price_history table
ALTER TABLE price_history
ADD COLUMN IF NOT EXISTS quantity_available INTEGER;

-- Add store_id column to price_history table
ALTER TABLE price_history
ADD COLUMN IF NOT EXISTS store_id INTEGER;

-- Add member_price column to product_prices table if not exists
ALTER TABLE product_prices
ADD COLUMN IF NOT EXISTS member_price DECIMAL(10, 2);

-- Add member_price column to products table if not exists
ALTER TABLE products
ADD COLUMN IF NOT EXISTS member_price DECIMAL(10, 2);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_price_history_member_price
ON price_history(member_price) WHERE member_price IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_price_history_stock_status
ON price_history(stock_status) WHERE stock_status IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_products_member_price
ON products(member_price) WHERE member_price IS NOT NULL;

-- Add documentation comments
COMMENT ON COLUMN price_history.member_price IS 'Historical membership/loyalty price for trend analysis';
COMMENT ON COLUMN price_history.stock_status IS 'Stock availability status (in_stock, out_of_stock, etc.)';
COMMENT ON COLUMN price_history.quantity_available IS 'Available quantity at time of recording';
COMMENT ON COLUMN price_history.store_id IS 'Reference to the store for multi-store tracking';
COMMENT ON COLUMN product_prices.member_price IS 'Historical membership/loyalty price tracking';
COMMENT ON COLUMN products.member_price IS 'Membership/loyalty price (e.g., Extra Jood Gold price)';
