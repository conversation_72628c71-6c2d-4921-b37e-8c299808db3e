-- Migration: Add member_price column to price_history table
-- This migration adds the missing member_price column to support membership/loyalty pricing

-- Add member_price column to price_history table
ALTER TABLE price_history 
ADD COLUMN IF NOT EXISTS member_price DECIMAL(10, 2);

-- Add member_price column to product_prices table if not exists
ALTER TABLE product_prices 
ADD COLUMN IF NOT EXISTS member_price DECIMAL(10, 2);

-- Add member_price column to products table if not exists
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS member_price DECIMAL(10, 2);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_price_history_member_price 
ON price_history(member_price) WHERE member_price IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_products_member_price 
ON products(member_price) WHERE member_price IS NOT NULL;

-- Add documentation comments
COMMENT ON COLUMN price_history.member_price IS 'Historical membership/loyalty price for trend analysis';
COMMENT ON COLUMN product_prices.member_price IS 'Historical membership/loyalty price tracking';
COMMENT ON COLUMN products.member_price IS 'Membership/loyalty price (e.g., Extra Jood Gold price)';
