# Duplicate Prevention System

This document explains the comprehensive duplicate prevention system implemented to ensure that when fetching products multiple times, no duplicate products are created and only product data like quantity, price, etc. gets updated.

## Overview

The duplicate prevention system consists of several components working together:

1. **Centralized Duplicate Prevention Utility** (`duplicate-prevention.js`)
2. **Enhanced Storage Methods** (in `storage.js`)
3. **Updated Store Scrapers** (using the new utility)
4. **Comprehensive Testing** (`test-duplicate-prevention.js`)

## Key Features

### 1. Multi-Criteria Product Matching

The system uses multiple criteria to identify existing products:

- **Primary**: `store_id` + `external_id` (most reliable)
- **Secondary**: `store_id` + `name` + `model` (for products with missing external_id)
- **Tertiary**: `store_id` + `sku` (for products with unique SKUs)
- **Fallback**: `store_id` + `name` (least reliable but catches some duplicates)

### 2. Smart Change Detection

The system only updates products when significant changes are detected:

- **Price changes** (with tolerance for floating-point differences)
- **Stock quantity changes**
- **Product information changes** (name, model, brand, size, UOM, URL, image)

### 3. Automatic Price and Quantity Tracking

When products are created or updated, the system automatically:

- Creates price history records in `product_prices` table
- Creates quantity history records in `product_quantities` table
- Maintains proper stock status tracking

## Implementation Details

### Core Functions

#### `processProductWithDuplicatePrevention(productData, store, category, existingProductsMap)`

Processes a single product with comprehensive duplicate prevention:

```javascript
const result = await processProductWithDuplicatePrevention(
  {
    name: 'Samsung 55" Smart TV',
    model: 'SAM-TV55-001',
    price: '2500.00',
    regular_price: '2800.00',
    stock: 10,
    externalId: 'samsung-tv-001',
    // ... other fields
  },
  { id: storeId, slug: 'store-slug' },
  { id: categoryId }
);

// Result contains:
// - action: 'created', 'updated', 'skipped', or 'error'
// - productId: Database ID of the product
// - changes: Array of changed fields
// - message: Human-readable description
// - errors: Array of error messages
```

#### `processBulkProductsWithDuplicatePrevention(productsData, store, category)`

Processes multiple products efficiently with bulk duplicate prevention:

```javascript
const summary = await processBulkProductsWithDuplicatePrevention(
  productsArray,
  store,
  category
);

// Summary contains:
// - total: Total number of products processed
// - created: Number of new products created
// - updated: Number of existing products updated
// - skipped: Number of products skipped (no changes)
// - errors: Number of products that failed processing
```

### Enhanced Storage Methods

#### `findProductByMultipleCriteria(storeId, externalId, name, model, sku)`

Finds products using multiple criteria in order of reliability.

#### `hasProductDataChanged(existingProduct, newProductData)`

Compares existing and new product data to determine if updates are needed.

#### `findProductsByStoreId(storeId)`

Retrieves all products for a store (used for bulk operations).

## Usage in Store Scrapers

### Before (Old Approach)
```javascript
// Check if product exists
const existingProduct = await storage.findProductByExternalId(storeId, externalId);

if (existingProduct) {
  // Manual update logic
  await storage.updateProduct(existingProduct.id, productData);
  // Manual price/quantity record creation
} else {
  // Manual create logic
  const newProduct = await storage.createProduct(productData);
  // Manual price/quantity record creation
}
```

### After (New Approach)
```javascript
// Use centralized duplicate prevention
const result = await processProductWithDuplicatePrevention(
  productData,
  store,
  category
);

// The utility handles everything:
// - Duplicate checking
// - Create/update decisions
// - Price/quantity record creation
// - Change tracking
```

## Benefits

### 1. **No Duplicates**
- Comprehensive matching prevents duplicate products
- Multiple fallback criteria catch edge cases
- Consistent behavior across all store scrapers

### 2. **Efficient Updates**
- Only updates when data actually changes
- Avoids unnecessary database operations
- Maintains accurate change history

### 3. **Consistent Behavior**
- All store scrapers use the same logic
- Standardized error handling
- Uniform logging and reporting

### 4. **Performance Optimized**
- Bulk operations support for large datasets
- Efficient database queries
- Minimal redundant operations

## Testing

Run the comprehensive test suite:

```bash
node test-duplicate-prevention.js
```

The test covers:
- Creating new products
- Detecting and skipping duplicates
- Updating products with changes
- Bulk processing
- Database integrity verification

## Integration with Existing Scrapers

The system has been integrated with:

1. **save-products-to-db.js** - Main product saving utility
2. **storeScraper.js** - Core scraper functionality
3. **server/bhstore-fetcher.js** - BH Store specific fetcher

Other store scrapers can be easily updated to use the new system by:

1. Importing the duplicate prevention utility
2. Replacing manual duplicate checking with `processProductWithDuplicatePrevention`
3. Removing manual price/quantity record creation (handled automatically)

## Configuration

The system uses these configurable parameters:

- **Price change threshold**: 0.01 SAR (configurable in `hasProductDataChanged`)
- **Batch size**: Configurable in bulk operations
- **Retry logic**: Built into database operations

## Monitoring and Logging

The system provides detailed logging:

- Product creation/update actions
- Change detection details
- Error reporting with context
- Performance metrics for bulk operations

## Future Enhancements

Potential improvements:

1. **Configurable matching criteria** - Allow stores to specify custom matching rules
2. **Advanced change detection** - More sophisticated comparison algorithms
3. **Performance metrics** - Detailed timing and efficiency reporting
4. **Automated duplicate cleanup** - Background processes to fix existing duplicates
