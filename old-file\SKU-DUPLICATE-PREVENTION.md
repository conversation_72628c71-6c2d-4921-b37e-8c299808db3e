# SKU-Based Duplicate Prevention Implementation

## Overview

This document outlines the implementation of SKU+Store based duplicate prevention to replace the current external_id approach. This ensures that products with the same SKU cannot be duplicated within the same store.

## ✅ Changes Made

### 1. Database Schema Updates

**File: `shared/schema.ts`**
- Added composite unique constraint: `(sku, store_id)`
- This prevents the same SKU from existing twice in the same store

```typescript
export const products = pgTable("products", {
  // ... existing fields
}, (table) => ({
  // Composite unique constraint: same SKU cannot exist twice in the same store
  skuStoreUnique: unique("products_sku_store_unique").on(table.sku, table.storeId),
}));
```

### 2. Storage Layer Updates

**File: `server/storage.ts`**

**New Functions Added:**
- `upsertProductBySku()` - Upserts products based on SKU+Store combination
- `findProductBySku()` - Finds products by SKU within a specific store

```typescript
async upsertProductBySku(product: InsertProduct): Promise<Product> {
  // Ensures SKU is provided, generates one if not
  const productWithSku = {
    ...product,
    sku: product.sku || `PRODUCT-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
  };

  const [upsertedProduct] = await db
    .insert(products)
    .values(productWithSku)
    .onConflictDoUpdate({
      target: [products.sku, products.storeId],
      set: {
        ...productWithSku,
        updatedAt: new Date(),
      },
    })
    .returning();
  return upsertedProduct;
}
```

## 🔄 Migration Required

### Database Migration Steps

1. **Generate SKU for products without one:**
   ```sql
   UPDATE products 
   SET sku = CONCAT('PRODUCT-', id)
   WHERE sku IS NULL OR sku = '';
   ```

2. **Remove duplicates (keep most recent):**
   ```sql
   WITH duplicates AS (
     SELECT id, ROW_NUMBER() OVER (
       PARTITION BY sku, store_id 
       ORDER BY updated_at DESC, id DESC
     ) as rn
     FROM products
     WHERE sku IS NOT NULL AND store_id IS NOT NULL
   )
   DELETE FROM products WHERE id IN (
     SELECT id FROM duplicates WHERE rn > 1
   );
   ```

3. **Add unique constraint:**
   ```sql
   ALTER TABLE products 
   ADD CONSTRAINT products_sku_store_unique 
   UNIQUE (sku, store_id);
   ```

## 📝 Implementation Plan

### Phase 1: Database Migration
- [ ] Run the migration script to add SKU+Store constraint
- [ ] Verify no duplicate SKU+Store combinations exist
- [ ] Test constraint works by attempting to insert duplicate

### Phase 2: Update Store Scrapers
- [ ] Update all store scrapers to use `upsertProductBySku()` instead of `upsertProduct()`
- [ ] Ensure all scrapers provide meaningful SKUs
- [ ] Update duplicate prevention logic to use SKU-based approach

### Phase 3: Update Application Logic
- [ ] Update product creation/update APIs to handle SKU constraints
- [ ] Update error handling for SKU constraint violations
- [ ] Update product search/filtering to leverage SKU indexing

## 🏪 Store-Specific SKU Strategies

### BH Store
- Use product model/code as SKU
- Format: `BH-{model}` or use existing product codes

### Alkhunaizan
- Use product ID or model number as SKU
- Format: `ALK-{productId}` or existing model codes

### Bin Momen
- Use product model/SKU from their system
- Format: `BM-{model}` or existing SKU field

### Tamkeen
- Use product code/model from API
- Format: `TAM-{productCode}` or existing model field

### Zagzoog
- Use product model number
- Format: `ZAG-{model}` or existing model field

## 🔧 Usage Examples

### Creating Products with SKU-based Deduplication

```typescript
// Instead of using externalId
const product = await storage.upsertProductBySku({
  name: "Samsung 55\" Smart TV",
  sku: "SAM-TV55-001", // Meaningful SKU
  storeId: store.id,
  categoryId: category.id,
  price: "2500.00",
  // ... other fields
});
```

### Finding Products by SKU

```typescript
// Find product by SKU within a store
const existingProduct = await storage.findProductBySku(store.id, "SAM-TV55-001");

if (existingProduct) {
  // Update existing product
  await storage.updateProduct(existingProduct.id, updatedData);
} else {
  // Create new product
  await storage.upsertProductBySku(newProductData);
}
```

## ✅ Benefits

1. **Logical Deduplication**: Uses business-meaningful SKUs instead of arbitrary external IDs
2. **Store Isolation**: Same SKU can exist in different stores (different products)
3. **Better Data Integrity**: Prevents accidental duplicates during bulk imports
4. **Improved Performance**: SKU+Store indexing for faster lookups
5. **Business Logic Alignment**: Matches real-world inventory management practices

## 🚨 Important Notes

- **Backward Compatibility**: Existing `externalId` approach still works
- **Gradual Migration**: Can migrate scrapers one by one
- **SKU Generation**: Auto-generates SKUs for products without them
- **Constraint Handling**: Proper error handling for constraint violations

## 📋 Testing Checklist

- [ ] Verify constraint prevents duplicate SKU+Store combinations
- [ ] Test SKU auto-generation for products without SKUs
- [ ] Verify upsert behavior (insert new, update existing)
- [ ] Test cross-store SKU allowance (same SKU, different stores)
- [ ] Validate scraper integration with new functions
- [ ] Test error handling for constraint violations

## 🎯 Next Steps

1. **Run Migration**: Execute the database migration script
2. **Update Scrapers**: Modify store scrapers to use SKU-based approach
3. **Test Integration**: Verify all functionality works with new constraint
4. **Monitor Performance**: Ensure no performance degradation
5. **Documentation**: Update API documentation and developer guides
