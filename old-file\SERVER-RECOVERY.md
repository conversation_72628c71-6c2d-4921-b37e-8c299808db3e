# Server Recovery Guide

This guide helps you recover from server crashes and monitor server health.

## Quick Recovery Commands

### Check Server Health
```bash
npm run health
```
This will check if the server is running and healthy.

### Start Server with Monitoring
```bash
npm run dev:monitor
```
This starts the server with automatic restart on crashes.

### Regular Server Start
```bash
npm run dev
```
Standard server start without monitoring.

## Common Issues and Solutions

### 1. Server Crashes During Zagzoog Fetching

**Symptoms:**
- Server stops responding during product fetching
- Memory usage spikes
- "Processing database batch" messages before crash

**Solutions:**
1. **Restart the server:**
   ```bash
   npm run dev
   ```

2. **Use monitored mode:**
   ```bash
   npm run dev:monitor
   ```

3. **Check memory usage:**
   ```bash
   npm run health
   ```

### 2. Database Connection Issues

**Symptoms:**
- "Database connection failed" messages
- API endpoints returning 500 errors

**Solutions:**
1. Check database connection in health check
2. Restart the server
3. Verify database credentials in .env file

### 3. Memory Issues

**Symptoms:**
- High memory usage warnings
- Slow response times
- Server becomes unresponsive

**Solutions:**
1. Restart the server to clear memory
2. Use the monitored mode for automatic restarts
3. Monitor memory usage with health checks

## Server Monitoring Tools

### 1. Health Check Script (`check-server-health.js`)
- Checks if server is responding
- Reports memory usage
- Verifies database connection
- Provides recovery suggestions

### 2. Server Monitor Script (`server-monitor.js`)
- Automatically restarts server on crashes
- Monitors memory usage
- Limits restart attempts to prevent infinite loops
- Provides detailed logging

## Improved Error Handling

The server now includes:

1. **Process Error Handling:**
   - Catches uncaught exceptions
   - Handles unhandled promise rejections
   - Prevents immediate crashes

2. **Memory Monitoring:**
   - Tracks memory usage every 5 minutes
   - Warns when memory usage is high
   - Automatically runs garbage collection

3. **Batch Processing Improvements:**
   - Reduced concurrent requests (3 instead of 10)
   - Better error handling in batches
   - Automatic recovery from failed batches
   - Memory cleanup between batches

## Usage Examples

### Start server with monitoring:
```bash
npm run dev:monitor
```

### Check server health:
```bash
npm run health
```

### If server crashes during fetching:
1. Check what happened: `npm run health`
2. Restart with monitoring: `npm run dev:monitor`
3. The monitor will automatically restart if it crashes again

## Prevention Tips

1. **Use monitored mode** for production or when doing heavy operations
2. **Check health regularly** during long-running operations
3. **Restart server periodically** if doing intensive data fetching
4. **Monitor memory usage** and restart if it gets too high

## Log Messages to Watch For

- `⚠️ High memory usage detected` - Consider restarting
- `❌ Uncaught Exception` - Server will try to continue
- `🔄 Attempting to continue` - Server is recovering
- `💥 Server process exited` - Monitor will restart automatically
