/**
 * Bearer Token Management API Routes
 * Handles manual bearer token management for all stores
 */

import express from 'express';
import jwt from 'jsonwebtoken';
import axios from 'axios';
import https from 'https';
import { storage } from '../storage.js';
import { sendFetchSummaryEmail } from '../simple-fetch-emailer.js';

const router = express.Router();

// Store configurations for bearer token management
const STORE_CONFIGS = {
  blackbox: {
    name: 'BlackBox',
    apiUrl: 'https://api.ops.blackbox.com.sa/api/v1/search/products/facets/category/175',
    testParams: { pageNo: 0, pageSize: 5 },
    headers: {
      'Accept': 'application/json, text/plain, */*',
      'Accept-Language': 'en-US,en;q=0.9',
      'Origin': 'https://www.blackbox.com.sa',
      'Referer': 'https://www.blackbox.com.sa/',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
  },
  alkhunaizan: {
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    apiUrl: 'https://api.alkhn.com/api/v1/products',
    testParams: { limit: 5 },
    headers: {
      'Accept': 'application/json',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
  }
};

/**
 * Validate JWT token format
 */
function validateTokenFormat(token) {
  if (!token || typeof token !== 'string') {
    return { valid: false, error: 'Token is required and must be a string' };
  }

  if (!token.startsWith('eyJ')) {
    return { valid: false, error: 'Invalid JWT format - must start with "eyJ"' };
  }

  const parts = token.split('.');
  if (parts.length !== 3) {
    return { valid: false, error: 'Invalid JWT format - must have 3 parts separated by dots' };
  }

  try {
    // Try to decode the payload to check format
    const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString());
    
    let expiryDate = null;
    let isExpired = false;
    
    if (payload.exp) {
      expiryDate = new Date(payload.exp * 1000);
      isExpired = expiryDate.getTime() < Date.now();
    }

    return {
      valid: true,
      payload: payload,
      expiryDate: expiryDate,
      isExpired: isExpired
    };
  } catch (error) {
    return { valid: false, error: 'Invalid JWT format - cannot decode payload' };
  }
}

/**
 * Test bearer token with store API
 */
async function testBearerToken(store, token) {
  const config = STORE_CONFIGS[store];
  if (!config) {
    return { success: false, error: 'Unknown store configuration' };
  }

  try {
    const response = await axios.get(config.apiUrl, {
      headers: {
        ...config.headers,
        'Authorization': `Bearer ${token}`
      },
      params: config.testParams,
      httpsAgent: new https.Agent({ rejectUnauthorized: false }),
      timeout: 15000
    });

    const productCount = response.data?.products?.length || 
                        response.data?.data?.length || 
                        (Array.isArray(response.data) ? response.data.length : 0);

    return {
      success: true,
      status: response.status,
      productCount: productCount,
      responseSize: JSON.stringify(response.data).length
    };
  } catch (error) {
    return {
      success: false,
      status: error.response?.status,
      error: error.message,
      details: error.response?.data
    };
  }
}

/**
 * Update scraper configuration with new token
 */
async function updateScraperConfig(store, token) {
  try {
    // Update in-memory configuration based on store
    if (store === 'blackbox') {
      // Update blackbox-scraper.js configuration
      const { scrapeBlackBox } = await import('../../blackbox-scraper.js');
      // The scraper will pick up the new token from the database
    }
    
    // Store in database for persistence
    await storage.saveBearerToken(store, token);
    
    return { success: true };
  } catch (error) {
    console.error('Error updating scraper config:', error);
    return { success: false, error: error.message };
  }
}

/**
 * GET /api/bearer-tokens
 * Get all stored bearer tokens with status
 */
router.get('/', async (req, res) => {
  try {
    const tokens = {};
    
    for (const [storeKey, config] of Object.entries(STORE_CONFIGS)) {
      const tokenData = await storage.getBearerToken(storeKey);
      
      if (tokenData && tokenData.token) {
        const validation = validateTokenFormat(tokenData.token);
        
        tokens[storeKey] = {
          storeName: config.name,
          hasToken: true,
          tokenPreview: `${tokenData.token.substring(0, 30)}...`,
          lastUpdated: tokenData.updated_at,
          isValid: validation.valid,
          isExpired: validation.isExpired,
          expiryDate: validation.expiryDate,
          error: validation.error
        };
      } else {
        tokens[storeKey] = {
          storeName: config.name,
          hasToken: false,
          tokenPreview: null,
          lastUpdated: null,
          isValid: false,
          isExpired: false,
          expiryDate: null,
          error: null
        };
      }
    }

    res.json({
      success: true,
      tokens: tokens,
      storeConfigs: Object.keys(STORE_CONFIGS)
    });
  } catch (error) {
    console.error('Error fetching bearer tokens:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch bearer tokens'
    });
  }
});

/**
 * POST /api/bearer-tokens/:store
 * Save bearer token for specific store
 */
router.post('/:store', async (req, res) => {
  try {
    const { store } = req.params;
    const { token } = req.body;

    if (!STORE_CONFIGS[store]) {
      return res.status(400).json({
        success: false,
        error: 'Unknown store'
      });
    }

    // Validate token format
    const validation = validateTokenFormat(token);
    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        error: validation.error
      });
    }

    // Save token to database
    await storage.saveBearerToken(store, token);

    // Update scraper configuration
    const updateResult = await updateScraperConfig(store, token);
    if (!updateResult.success) {
      console.warn('Failed to update scraper config:', updateResult.error);
    }

    // Send email notification
    try {
      await sendFetchSummaryEmail({
        scraperName: `${STORE_CONFIGS[store].name} Bearer Token Update`,
        newProducts: 0,
        updatedProducts: 0,
        totalProcessed: 0,
        errors: [],
        duration: '0s',
        timestamp: new Date().toISOString(),
        processedSuccessfully: true,
        
        tokenUpdate: {
          store: STORE_CONFIGS[store].name,
          action: 'Manual Token Update',
          tokenPreview: `${token.substring(0, 30)}...`,
          expiryDate: validation.expiryDate,
          isExpired: validation.isExpired,
          updatedBy: 'Manual Settings Update'
        }
      });
    } catch (emailError) {
      console.warn('Failed to send token update email:', emailError);
    }

    res.json({
      success: true,
      message: `Bearer token saved for ${STORE_CONFIGS[store].name}`,
      tokenPreview: `${token.substring(0, 30)}...`,
      expiryDate: validation.expiryDate,
      isExpired: validation.isExpired
    });
  } catch (error) {
    console.error('Error saving bearer token:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to save bearer token'
    });
  }
});

/**
 * POST /api/bearer-tokens/:store/test
 * Test bearer token for specific store
 */
router.post('/:store/test', async (req, res) => {
  try {
    const { store } = req.params;
    const { token } = req.body;

    if (!STORE_CONFIGS[store]) {
      return res.status(400).json({
        success: false,
        error: 'Unknown store'
      });
    }

    // Validate token format first
    const validation = validateTokenFormat(token);
    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        error: validation.error
      });
    }

    // Test token with API
    const testResult = await testBearerToken(store, token);

    res.json({
      success: true,
      store: STORE_CONFIGS[store].name,
      tokenTest: testResult,
      tokenInfo: {
        isExpired: validation.isExpired,
        expiryDate: validation.expiryDate
      }
    });
  } catch (error) {
    console.error('Error testing bearer token:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to test bearer token'
    });
  }
});

/**
 * DELETE /api/bearer-tokens/:store
 * Remove bearer token for specific store
 */
router.delete('/:store', async (req, res) => {
  try {
    const { store } = req.params;

    if (!STORE_CONFIGS[store]) {
      return res.status(400).json({
        success: false,
        error: 'Unknown store'
      });
    }

    await storage.deleteBearerToken(store);

    res.json({
      success: true,
      message: `Bearer token removed for ${STORE_CONFIGS[store].name}`
    });
  } catch (error) {
    console.error('Error deleting bearer token:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete bearer token'
    });
  }
});

export default router;
