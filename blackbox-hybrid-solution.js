/**
 * Blackbox Hybrid Solution
 * Tries token-based API first, falls back to session scraping if 401 errors persist
 */

import { scrapeBlackBox } from './blackbox-scraper.js';
import { scrapeBlackboxWithSession } from './blackbox-session-scraper.js';
import { sendFetchSummaryEmail } from './server/simple-fetch-emailer.js';

/**
 * Hybrid approach: Try API first, fallback to session scraping
 */
async function runBlackboxHybridSolution() {
  console.log('🚀 BLACKBOX HYBRID SOLUTION');
  console.log('='.repeat(35));
  console.log('Strategy: Try API token first, fallback to session scraping');
  console.log('This ensures we get products regardless of token issues');
  console.log('');

  const startTime = Date.now();
  let apiResults = null;
  let sessionResults = null;
  let finalResults = null;
  let method = 'unknown';

  try {
    // Attempt 1: Try API-based scraping with current token
    console.log('📋 ATTEMPT 1: API-Based Scraping');
    console.log('-'.repeat(35));
    console.log('🔑 Trying with current bearer token...');
    
    try {
      apiResults = await scrapeBlackBox(false, true, false);
      
      if (apiResults.success && apiResults.count > 0) {
        console.log(`✅ API scraping successful: ${apiResults.count} products`);
        finalResults = apiResults;
        method = 'api_token';
      } else {
        console.log(`⚠️ API scraping completed but found ${apiResults.count || 0} products`);
        if (apiResults.errors && apiResults.errors.some(e => e.includes('401'))) {
          console.log('🔑 401 errors detected - will try session approach');
        }
      }
    } catch (error) {
      console.log(`❌ API scraping failed: ${error.message}`);
      apiResults = { success: false, count: 0, errors: [error.message] };
    }

    // Attempt 2: Session-based scraping if API failed or got no results
    if (!finalResults || finalResults.count === 0) {
      console.log('\n📋 ATTEMPT 2: Session-Based Scraping');
      console.log('-'.repeat(40));
      console.log('🌐 Trying browser session approach (bypasses token issues)...');
      
      try {
        sessionResults = await scrapeBlackboxWithSession();
        
        if (sessionResults.success && sessionResults.count > 0) {
          console.log(`✅ Session scraping successful: ${sessionResults.count} products`);
          finalResults = sessionResults;
          method = 'session_bypass';
        } else {
          console.log(`⚠️ Session scraping found ${sessionResults.count || 0} products`);
        }
      } catch (error) {
        console.log(`❌ Session scraping failed: ${error.message}`);
        sessionResults = { success: false, count: 0, errors: [error.message] };
      }
    }

    // If both methods failed, use the better of the two
    if (!finalResults) {
      if (apiResults && sessionResults) {
        finalResults = apiResults.count >= sessionResults.count ? apiResults : sessionResults;
        method = apiResults.count >= sessionResults.count ? 'api_token_partial' : 'session_bypass_partial';
      } else {
        finalResults = apiResults || sessionResults || { success: false, count: 0, errors: ['Both methods failed'] };
        method = 'failed';
      }
    }

  } catch (error) {
    console.error('❌ Hybrid solution crashed:', error.message);
    finalResults = { success: false, count: 0, errors: [`Hybrid solution crashed: ${error.message}`] };
    method = 'crashed';
  }

  // Send comprehensive email report
  console.log('\n📋 SENDING EMAIL REPORT');
  console.log('-'.repeat(25));

  const duration = ((Date.now() - startTime) / 1000).toFixed(2);

  try {
    const emailData = {
      scraperName: 'Blackbox Hybrid Solution',
      newProducts: finalResults?.count || 0,
      updatedProducts: 0,
      totalProcessed: finalResults?.count || 0,
      errors: finalResults?.errors || [],
      duration: `${duration}s`,
      timestamp: new Date().toISOString(),
      processedSuccessfully: finalResults?.success && (finalResults?.count > 0),
      
      // Hybrid solution details
      hybridSolution: {
        methodUsed: method,
        apiAttempt: {
          attempted: true,
          success: apiResults?.success || false,
          productsFound: apiResults?.count || 0,
          errors: apiResults?.errors || []
        },
        sessionAttempt: {
          attempted: !!sessionResults,
          success: sessionResults?.success || false,
          productsFound: sessionResults?.count || 0,
          errors: sessionResults?.errors || []
        },
        finalMethod: method,
        totalProductsFound: finalResults?.count || 0
      },
      
      // Issue resolution status
      resolutionStatus: {
        originalIssue: 'Fetch Results all 0 due to 401 authentication errors',
        solutionApplied: 'Hybrid approach: API token + session fallback',
        currentStatus: finalResults?.success && (finalResults?.count > 0) ? 'RESOLVED' : 'ONGOING',
        recommendedAction: finalResults?.success && (finalResults?.count > 0) ? 
          `Continue using ${method} method for regular scraping` :
          'Investigate website changes or consider manual token extraction'
      }
    };

    const emailResult = await sendFetchSummaryEmail(emailData);
    
    if (emailResult.success) {
      console.log('✅ Email report sent <NAME_EMAIL>');
    } else {
      console.log('⚠️ Email report failed:', emailResult.error);
    }

  } catch (error) {
    console.error('❌ Email sending failed:', error.message);
  }

  // Final summary
  console.log('\n📊 HYBRID SOLUTION RESULTS:');
  console.log('='.repeat(35));
  console.log(`🔧 Method used: ${method}`);
  console.log(`✅ Success: ${finalResults?.success || false}`);
  console.log(`📦 Products found: ${finalResults?.count || 0}`);
  console.log(`❌ Total errors: ${finalResults?.errors?.length || 0}`);
  console.log(`⏱️ Duration: ${duration}s`);
  
  if (apiResults) {
    console.log(`🔑 API attempt: ${apiResults.success ? 'SUCCESS' : 'FAILED'} (${apiResults.count || 0} products)`);
  }
  
  if (sessionResults) {
    console.log(`🌐 Session attempt: ${sessionResults.success ? 'SUCCESS' : 'FAILED'} (${sessionResults.count || 0} products)`);
  }

  const overallSuccess = finalResults?.success && (finalResults?.count > 0);

  if (overallSuccess) {
    console.log('\n🎉 HYBRID SOLUTION SUCCESSFUL!');
    console.log('✅ Products successfully extracted');
    console.log('✅ "Fetch Results all 0" issue RESOLVED');
    console.log(`✅ Working method: ${method}`);
    console.log('✅ Email report sent with details');
    console.log('');
    console.log('📧 Check your <NAME_EMAIL> for full report');
    console.log(`🚀 Use the ${method} method for future scraping!`);
  } else {
    console.log('\n⚠️ HYBRID SOLUTION NEEDS ATTENTION');
    console.log('Both API and session methods had issues:');
    
    if (apiResults && !apiResults.success) {
      console.log('🔑 API method failed - token issues persist');
    }
    
    if (sessionResults && !sessionResults.success) {
      console.log('🌐 Session method failed - website structure issues');
    }
    
    console.log('\n💡 NEXT STEPS:');
    console.log('1. 📧 Check email for detailed error analysis');
    console.log('2. 🔑 Try getting a completely fresh token');
    console.log('3. 🌐 Check if Blackbox website is accessible');
    console.log('4. 🔧 Consider manual debugging of both methods');
  }

  return {
    success: overallSuccess,
    method: method,
    finalResults: finalResults,
    apiResults: apiResults,
    sessionResults: sessionResults,
    duration: duration
  };
}

// Export the main function
export { runBlackboxHybridSolution };

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runBlackboxHybridSolution()
    .then(results => {
      if (results.success) {
        console.log('\n🎉 BLACKBOX HYBRID SOLUTION COMPLETED SUCCESSFULLY!');
        console.log(`📦 ${results.finalResults?.count || 0} products extracted using ${results.method}`);
        console.log('✅ Ready for production use!');
        process.exit(0);
      } else {
        console.log('\n❌ BLACKBOX HYBRID SOLUTION NEEDS ATTENTION!');
        console.log('🔧 Both methods encountered issues - check email for details');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('\n💥 BLACKBOX HYBRID SOLUTION CRASHED:', error);
      process.exit(1);
    });
}
