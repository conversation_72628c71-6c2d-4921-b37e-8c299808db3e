const { Pool } = require('pg');

async function fixMemberPriceColumn() {
  console.log('🔧 Fixing member_price column in price_history table...');
  
  const pool = new Pool({
    connectionString: 'postgres://postgres:<EMAIL>:5432/ir2-ksa?sslmode=no-verify',
    ssl: { rejectUnauthorized: false }
  });

  try {
    console.log('✅ Connected to database');

    // Check if member_price column exists in price_history table
    console.log('\n🔍 Checking if member_price column exists...');
    const columnCheck = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'price_history' 
      AND column_name = 'member_price'
    `);

    if (columnCheck.rows.length === 0) {
      console.log('❌ member_price column does not exist in price_history table');
      console.log('🔧 Adding member_price column...');
      
      await pool.query(`
        ALTER TABLE price_history 
        ADD COLUMN member_price DECIMAL(10, 2)
      `);
      
      console.log('✅ Added member_price column to price_history table');
    } else {
      console.log('✅ member_price column already exists in price_history table');
    }

    // Also check and add to other tables if needed
    console.log('\n🔍 Checking other tables...');
    
    // Check products table
    const productsCheck = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'products' 
      AND column_name = 'member_price'
    `);

    if (productsCheck.rows.length === 0) {
      console.log('🔧 Adding member_price column to products table...');
      await pool.query(`
        ALTER TABLE products 
        ADD COLUMN member_price DECIMAL(10, 2)
      `);
      console.log('✅ Added member_price column to products table');
    }

    // Check product_prices table
    const productPricesCheck = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'product_prices' 
      AND column_name = 'member_price'
    `);

    if (productPricesCheck.rows.length === 0) {
      console.log('🔧 Adding member_price column to product_prices table...');
      await pool.query(`
        ALTER TABLE product_prices 
        ADD COLUMN member_price DECIMAL(10, 2)
      `);
      console.log('✅ Added member_price column to product_prices table');
    }

    console.log('\n🎉 Migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

fixMemberPriceColumn().catch(console.error);
