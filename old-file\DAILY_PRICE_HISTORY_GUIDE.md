# Daily Price History Tracking System - User Guide

## 🎯 Overview

The Daily Price History Tracking System provides comprehensive price monitoring and trend analysis for all products across all stores in ProductPricePro. It leverages the existing `product_prices` table infrastructure to deliver powerful insights without data duplication.

## 🚀 Quick Start

### 1. Setup (One-time)
```bash
# Run the setup script to initialize the system
node setup-daily-price-history.js
```

### 2. Testing
```bash
# Test the system functionality
node test-daily-price-tracker.js

# Test API endpoints (requires server running)
node test-price-history-api.js
```

### 3. Access the Demo
Navigate to `/price-history-demo` in your browser to see all features in action.

## 📊 Key Features

### ✅ Automated Daily Snapshots
- **Schedule**: Runs automatically at 3:00 AM daily
- **Coverage**: All products across all active stores
- **Performance**: Bulk processing with 1000 products per batch
- **Reliability**: Store-by-store processing with error isolation

### ✅ Advanced Analytics
- **Price Trends**: Up/down/stable trend detection
- **Volatility**: Price stability analysis
- **Change Tracking**: Detailed price change history
- **Store Comparison**: Cross-store price analysis

### ✅ Interactive Visualizations
- **Enhanced Price Charts**: Multiple price types (regular, member, promotional)
- **Time Range Filtering**: 7d, 30d, 90d, 1y, all time
- **Real-time Dashboard**: Daily statistics and store breakdowns
- **Summary Statistics**: Min, max, average prices

## 🔧 API Endpoints

### Public Endpoints

#### Get Daily Snapshot Statistics
```http
GET /api/price-history/daily-stats?date=2024-01-15
```
**Response:**
```json
{
  "success": true,
  "data": {
    "date": "2024-01-15",
    "totalSnapshots": 15420,
    "avgPrice": 245.67,
    "minPrice": 12.50,
    "maxPrice": 2999.99,
    "storeBreakdown": [
      {"storeId": 1, "storeName": "Extra", "count": 5200},
      {"storeId": 2, "storeName": "Almanea", "count": 3100}
    ]
  }
}
```

#### Get Product Price Analytics
```http
GET /api/products/12345/price-analytics?days=30
```
**Response:**
```json
{
  "success": true,
  "data": {
    "productId": 12345,
    "totalEntries": 45,
    "priceChanges": 8,
    "avgPrice": 199.99,
    "minPrice": 179.99,
    "maxPrice": 219.99,
    "volatility": 12.5,
    "trend": "down",
    "lastUpdated": "2024-01-15T10:30:00Z"
  }
}
```

#### Get Enhanced Price History
```http
GET /api/products/12345/price-history/enhanced?days=7&limit=50&storeId=1
```
**Response:**
```json
{
  "success": true,
  "data": {
    "priceHistory": [
      {
        "id": 789,
        "productId": 12345,
        "price": "199.99",
        "regularPrice": "219.99",
        "memberPrice": "189.99",
        "stock": 15,
        "stockStatus": "in_stock",
        "currency": "SAR",
        "storeId": 1,
        "recordedAt": "2024-01-15T03:00:00Z"
      }
    ],
    "totalEntries": 25,
    "filters": {
      "productId": 12345,
      "days": 7,
      "limit": 50,
      "storeId": 1
    }
  }
}
```

### Admin Endpoints

#### Trigger Manual Snapshot
```http
POST /api/price-history/capture-snapshot
```
**Response:**
```json
{
  "success": true,
  "message": "Daily price snapshot completed",
  "data": {
    "totalProducts": 15420,
    "successfulSnapshots": 15380,
    "errors": [],
    "processingTime": 45000
  }
}
```

#### Cleanup Old Data
```http
DELETE /api/price-history/cleanup?keepDays=365
```

## 🎨 Frontend Components

### EnhancedPriceChart
```tsx
import EnhancedPriceChart from '../components/EnhancedPriceChart';

<EnhancedPriceChart
  data={priceHistoryData}
  title="Product Price History"
  height={400}
  showRegularPrice={true}
  showMemberPrice={true}
  timeRange="30d"
  onTimeRangeChange={handleTimeRangeChange}
/>
```

### DailyPriceHistoryDashboard
```tsx
import DailyPriceHistoryDashboard from '../components/DailyPriceHistoryDashboard';

<DailyPriceHistoryDashboard className="mb-6" />
```

## 🔄 Scheduler Configuration

The system uses the existing scheduler infrastructure:

```javascript
// Schedule Type: 'price_history'
// Cron Expression: '0 3 * * *' (3:00 AM daily)
// Settings: { "captureAllStores": true, "batchSize": 1000 }
```

### Manual Schedule Management
```sql
-- Check schedule status
SELECT * FROM schedules WHERE type = 'price_history';

-- Enable/disable schedule
UPDATE schedules SET enabled = true WHERE type = 'price_history';

-- Update schedule time
UPDATE schedules SET cron_expression = '0 2 * * *' WHERE type = 'price_history';
```

## 📈 Data Architecture

### Existing Infrastructure Used
- **`product_prices` table**: Primary storage for price history
- **`products` table**: Product information and current prices
- **`stores` table**: Store information and active status
- **`schedules` table**: Automated job scheduling

### Data Flow
1. **Daily Scheduler** triggers at 3:00 AM
2. **Daily Price Tracker** processes all active stores
3. **Bulk Operations** insert price snapshots into `product_prices`
4. **API Endpoints** serve historical data for analysis
5. **Frontend Components** visualize trends and statistics

## 🛠️ Troubleshooting

### Common Issues

#### Schedule Not Running
```sql
-- Check if schedule exists and is enabled
SELECT id, enabled, last_run, next_run FROM schedules WHERE type = 'price_history';

-- Check server logs for scheduler errors
```

#### No Price Data
```sql
-- Check if products have price data
SELECT COUNT(*) FROM products WHERE price IS NOT NULL;

-- Check recent price entries
SELECT COUNT(*) FROM product_prices WHERE timestamp >= NOW() - INTERVAL '24 hours';
```

#### API Errors
- Ensure server is running on correct port
- Check authentication for admin endpoints
- Verify product IDs exist in database

### Performance Optimization

#### Database Indexes
```sql
-- Ensure these indexes exist for optimal performance
CREATE INDEX IF NOT EXISTS idx_product_prices_timestamp ON product_prices(timestamp);
CREATE INDEX IF NOT EXISTS idx_product_prices_product_timestamp ON product_prices(product_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_products_store_id ON products(store_id);
```

#### Batch Size Tuning
```javascript
// Adjust batch size in daily-price-tracker.ts
private batchSize = 1000; // Increase for more memory, decrease for stability
```

## 📝 Monitoring

### Key Metrics to Monitor
- Daily snapshot completion rate
- Processing time per store
- Error rates and types
- Database storage growth
- API response times

### Health Checks
```bash
# Check daily stats
curl "http://localhost:3000/api/price-history/daily-stats"

# Check system health
curl "http://localhost:3000/api/health"
```

## 🔮 Future Enhancements

### Planned Features
- **Price Alerts**: Notify users of significant price changes
- **Trend Predictions**: ML-based price forecasting
- **Export Features**: Excel/CSV export of price history
- **Advanced Filtering**: More granular data filtering options
- **Real-time Updates**: WebSocket-based live price updates

### Integration Opportunities
- **Business Intelligence**: Connect to BI tools for advanced analytics
- **Mobile App**: Price tracking on mobile devices
- **Third-party APIs**: Integration with external price comparison services

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review server logs for error details
3. Test with the provided test scripts
4. Verify database connectivity and permissions

---

**Last Updated**: January 2024  
**Version**: 1.0.0  
**Compatibility**: ProductPricePro v2.0+
