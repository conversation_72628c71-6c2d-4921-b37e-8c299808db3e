# 🚨 PRODUCTION DEPLOYMENT FIX

## **ISSUE SUMMARY**
The production server at `https://ir-ksa.dream4soft.co.uk/login` is experiencing:
1. **Database Connection Timeouts** - `TimeoutError: signal timed out`
2. **Server Not Responding** - `net::ERR_CONNECTION_TIMED_OUT`
3. **Cross-Origin Issues** - API calls failing due to hostname mismatches

## **ROOT CAUSES IDENTIFIED**

### 1. **Database Configuration Issues**
- PostgreSQL connection timeouts
- Possible SSL/connection string problems
- Pool configuration may be too restrictive

### 2. **Server Configuration Issues**
- PM2 process may be down or misconfigured
- Port 3021 may not be properly exposed
- Firewall or network configuration issues

### 3. **Client-Side Configuration**
- API URL configuration for production domain
- Cross-origin request handling

## **🔧 IMMEDIATE FIXES REQUIRED**

### **Step 1: Check Production Server Status**
```bash
# SSH into your production server
ssh your-server

# Check if PM2 is running
pm2 status

# Check if the application is running on port 3021
netstat -tulpn | grep :3021

# Check PM2 logs for errors
pm2 logs irksa
```

### **Step 2: Database Connection Fix**
Check your `.env` file on the production server:
```env
# Required database configuration
DATABASE_URL=postgresql://username:password@host:port/database
DB_HOST=your-db-host
DB_USER=your-db-user
DB_PASSWORD=your-db-password
DB_NAME=your-db-name
DB_PORT=5432
DB_SSL=true

# Session configuration
SESSION_SECRET=your-session-secret

# Email configuration (if using)
GMAIL_USER=<EMAIL>
GMAIL_PASS=your-app-password

# Environment
NODE_ENV=production
PORT=3021
```

### **Step 3: PM2 Configuration Fix**
Create/update `ecosystem.config.js`:
```javascript
module.exports = {
  apps: [{
    name: 'irksa',
    script: 'dist/index.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3021
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
```

### **Step 4: Build and Deploy**
```bash
# On your production server
cd /path/to/your/app

# Pull latest changes
git pull origin main

# Install dependencies
npm install --production

# Build the application
npm run build

# Restart PM2
pm2 restart irksa

# Check status
pm2 status
pm2 logs irksa --lines 50
```

### **Step 5: Database Connection Test**
Create a test script to verify database connectivity:
```javascript
// test-db.js
import pg from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const pool = new pg.Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false
});

async function testConnection() {
  try {
    const client = await pool.connect();
    console.log('✅ Database connection successful');
    const result = await client.query('SELECT NOW()');
    console.log('✅ Query test successful:', result.rows[0]);
    client.release();
  } catch (error) {
    console.error('❌ Database connection failed:', error);
  } finally {
    await pool.end();
  }
}

testConnection();
```

Run: `node test-db.js`

## **🔍 DEBUGGING STEPS**

### **1. Check Server Logs**
```bash
# PM2 logs
pm2 logs irksa --lines 100

# System logs
journalctl -u nginx -f  # if using nginx
tail -f /var/log/nginx/error.log
```

### **2. Check Network Connectivity**
```bash
# Test if port 3021 is accessible
curl -I http://localhost:3021/
curl -I https://ir-ksa.dream4soft.co.uk:3021/

# Check if process is listening
lsof -i :3021
```

### **3. Check Database Connectivity**
```bash
# Test PostgreSQL connection
psql -h your-db-host -U your-db-user -d your-db-name

# Check database server status
systemctl status postgresql  # if self-hosted
```

## **⚡ QUICK RECOVERY COMMANDS**

If the server is completely down:
```bash
# Kill any stuck processes
pkill -f "node.*irksa"
pkill -f "tsx.*server"

# Clean restart
pm2 delete irksa
pm2 start ecosystem.config.js
pm2 save
```

## **🛡️ PREVENTIVE MEASURES**

### **1. Enhanced Error Handling**
Add better database connection error handling in production.

### **2. Health Check Endpoint**
Implement `/health` endpoint for monitoring.

### **3. Monitoring Setup**
- Set up PM2 monitoring
- Database connection monitoring
- Log aggregation

## **📞 NEXT STEPS**

1. **Immediate**: Check PM2 status and restart if needed
2. **Short-term**: Fix database connection configuration
3. **Long-term**: Implement proper monitoring and alerting

## **🔗 USEFUL COMMANDS**

```bash
# PM2 Management
pm2 restart irksa
pm2 reload irksa
pm2 stop irksa
pm2 start irksa
pm2 logs irksa
pm2 monit

# Application Management
npm run build
npm run start
npm run dev

# Database Management
npm run db:push
node test-db.js
```

---

**⚠️ CRITICAL**: The production server needs immediate attention. Start with checking PM2 status and database connectivity.
