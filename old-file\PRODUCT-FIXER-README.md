# Product Fixer Integration

This integration automatically fixes common issues with product data during the fetching process. When you click the "Fetch Products" button in the UI, all the fixes will be applied automatically without needing to run any additional scripts.

## What This Fixes

1. **Regular Price Calculation**
   - For BH Store: Adds 10% to the current price when regular price is missing or equal to current price
   - For Alkhunaizan: Uses 15% markup for better price differentiation and checks for original_price or special_price in the API response
   - For other stores: Adds 10% markup when regular price is missing or equal to current price

2. **Category Names**
   - Standardizes category names across all stores
   - Maps similar categories to consistent names (e.g., "TV", "Television", "Smart TV" all map to "TVs & Entertainment")

3. **Tamkeen Product URLs**
   - Ensures URLs follow the format: https://tamkeenstores.com.sa/en/product/[product-slug]
   - Creates proper slugs from product names

4. **Zagzoog Store Models**
   - Generates consistent model codes with proper brand and category identifiers
   - Extracts size information and formats it based on unit of measurement
   - Uses SKU when available for more accurate unique IDs

5. **Image URLs**
   - Ensures all image URLs are absolute (adds domain if missing)
   - Provides category-specific default images for missing images
   - Fixes placeholder images

6. **Product Pricing Display**
   - When a product has only a price without a regular price, uses price as regular price and doesn't show promotion
   - When regular price equals price, only shows regular price (no promotion)

## How It Works

The integration consists of several components:

1. **product-fixer.js**: Contains all the fixing logic
2. **storage-with-fixer.js**: Enhances the database storage to apply fixes before saving products
3. **integrate-product-fixer.js**: Script to integrate the enhanced storage with storeScraper.js
4. **storeBridge.ts**: Modified to apply fixes after fetching products

## Installation

1. Run the integration script:

```
node integrate-product-fixer.js
```

This will:
- Create a backup of the original storeScraper.js file
- Modify storeScraper.js to use the enhanced storage with product fixing

2. Restart your server:

```
npm start
```

## Usage

Simply use the "Fetch Products" button in the UI as you normally would. All products will be automatically fixed during the fetching process.

You don't need to run any additional scripts after fetching - everything is handled automatically!

## Verification

To verify that the fixes are being applied:

1. Check the server logs during fetching - you should see messages like "Fixing product X from store Y..."
2. After fetching, check the products in the UI:
   - Regular prices should be higher than or equal to current prices
   - Products without promotions should show only one price
   - Tamkeen product URLs should follow the correct format
   - Zagzoog products should have proper model codes
   - All images should load correctly

## Troubleshooting

If you encounter any issues:

1. Check the server logs for error messages
2. Make sure the product-fixer.js file is in the server directory
3. Restore the original storeScraper.js from the backup:

```
cp storeScraper.js.bak storeScraper.js
```

4. If you see an error about missing exports, check that the product-fixer.js file has all the necessary functions implemented
5. Run the manual fix script if needed:

```
node run-all-fixes.js
```

## Additional Information

- The fixes are applied both during fetching (for new products) and after fetching (for existing products)
- The integration is designed to be non-intrusive and can be easily removed if needed
- All fixes are consistent with the standalone fix scripts (run-all-fixes.js, fix-product-pricing-display.js, etc.)

For any questions or issues, please contact the development team.
