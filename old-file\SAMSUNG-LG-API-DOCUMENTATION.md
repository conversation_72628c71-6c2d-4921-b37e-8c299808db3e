# Samsung & LG Products API Documentation

## 📋 Overview

The Samsung & LG API endpoints provide comprehensive product information including complete details and daily price history. These endpoints are specifically designed to retrieve Samsung and LG products with full specifications, pricing data, and historical price tracking.

## 🚀 Base URLs

```
/api/products/samsung-lg-complete
/api/products/samsung-lg-history
```

## 📊 Features

✅ **All Samsung and LG products** from all stores  
✅ **Complete product details** (name, brand, category, specifications)
✅ **Daily price history** with change tracking
✅ **Price statistics** (min, max, average, volatility)
✅ **Store information** and availability
✅ **Advanced filtering** by date, brand, store, category
✅ **Discount calculations** and price comparisons
✅ **Performance optimized** queries with execution time tracking
✅ **JSON aggregated** price history data
✅ **Real-time data** from database

## 🛠️ API Endpoints

### 1. Complete Samsung & LG Products

**Endpoint:** `GET /api/products/samsung-lg-complete`

**Description:** Returns all Samsung and LG products with complete details and full price history.

**Parameters:** None required

**Features:**
- All Samsung and LG products from all stores
- Complete product specifications
- Full daily price history
- Price statistics (min, max, average, changes)
- Store information
- Discount calculations

### 2. Filtered Samsung & LG Products

**Endpoint:** `GET /api/products/samsung-lg-history`

**Description:** Returns Samsung and LG products with filtered price history based on various criteria.

**Query Parameters:**

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `startDate` | string | - | Start date for price history filter (YYYY-MM-DD) |
| `endDate` | string | - | End date for price history filter (YYYY-MM-DD) |
| `brand` | string | - | Filter by specific brand ('samsung' or 'lg') |
| `store` | string | - | Filter by store slug or name |
| `category` | string | - | Filter by product category |
| `limit` | number | 100 | Maximum number of products to return |

## 💡 Usage Examples

### 1. Get All Samsung & LG Products

```bash
curl "http://localhost:3021/api/products/samsung-lg-complete"
```

### 2. Get Samsung Products Only

```bash
curl "http://localhost:3021/api/products/samsung-lg-history?brand=samsung"
```

### 3. Get Products with Price History from Last 30 Days

```bash
curl "http://localhost:3021/api/products/samsung-lg-history?startDate=2024-01-01&endDate=2024-01-31"
```

### 4. Get LG TVs from Specific Store

```bash
curl "http://localhost:3021/api/products/samsung-lg-history?brand=lg&category=tvs&store=alkhunaizan"
```

### 5. JavaScript Example

```javascript
async function getSamsungLGProducts() {
  try {
    const response = await fetch('http://localhost:3021/api/products/samsung-lg-complete');
    const data = await response.json();
    
    console.log(`Found ${data.summary.total_products} products`);
    console.log(`Samsung: ${data.summary.samsung_products}, LG: ${data.summary.lg_products}`);
    
    // Process products with price history
    data.products.forEach(product => {
      console.log(`${product.brand} ${product.name}: ${product.current_price} SAR`);
      console.log(`Price history: ${product.price_history.length} records`);
      
      if (product.price_history.length > 0) {
        const latestChange = product.price_history[0];
        console.log(`Latest change: ${latestChange.change_type} - ${latestChange.price} SAR`);
      }
    });
    
  } catch (error) {
    console.error('Error fetching products:', error);
  }
}
```

## 🧪 Testing

Run the test script to verify the API endpoints:

```bash
node test-samsung-lg-api.js
```

## 📊 Response Format

Both endpoints return comprehensive product data with the following structure:

```json
{
  "success": true,
  "summary": {
    "total_products": 150,
    "samsung_products": 85,
    "lg_products": 65,
    "stores_covered": 8,
    "categories_covered": 5,
    "products_with_price_history": 120,
    "avg_price_samsung": 2500,
    "avg_price_lg": 2200,
    "execution_time_ms": 450,
    "generated_at": "2024-01-15T10:30:00.000Z"
  },
  "products": [
    {
      "id": 12345,
      "name": "Samsung 55\" QLED 4K Smart TV",
      "brand": "SAMSUNG",
      "category": "tvs-entertainment",
      "current_price": 2999.00,
      "regular_price": 3499.00,
      "discount_percentage": 14.29,
      "store_name": "Alkhunaizan",
      "price_history": [
        {
          "date": "2024-01-15",
          "price": 2999.00,
          "change_type": "decrease",
          "price_change": -100.00,
          "percentage_change": -3.23,
          "recorded_at": "2024-01-15T08:00:00.000Z"
        }
      ],
      "price_statistics": {
        "total_price_changes": 25,
        "price_increases": 8,
        "price_decreases": 12,
        "avg_price": 3150.50,
        "min_price": 2899.00,
        "max_price": 3499.00
      }
    }
  ]
}
```

## ⚡ Performance Notes

### Optimization Features

1. **Efficient Queries**: Uses PostgreSQL CTEs and LATERAL JOINs for optimal performance
2. **JSON Aggregation**: Price history is aggregated at database level
3. **Indexed Lookups**: Queries use indexed columns (brand, store_id, product_id)
4. **Execution Time Tracking**: Response includes query execution time

### Expected Performance

- **Complete API**: 300-800ms for all Samsung & LG products
- **Filtered API**: 100-400ms depending on filters and date range
- **Memory Usage**: Optimized for large datasets with streaming JSON responses

## ⚠️ Error Handling

### Error Response Format

```json
{
  "success": false,
  "error": "Failed to fetch Samsung & LG products with price history",
  "message": "Database connection failed",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Common HTTP Status Codes

- `200 OK`: Request successful
- `400 Bad Request`: Invalid query parameters
- `500 Internal Server Error`: Server or database error

## 🎯 Summary

The new Samsung & LG API endpoints provide:

✅ **Two powerful endpoints** for comprehensive product data  
✅ **Complete product details** with specifications and pricing  
✅ **Daily price history** with change tracking and statistics  
✅ **Advanced filtering** by date, brand, store, and category  
✅ **Performance optimized** queries with execution time tracking  
✅ **JSON aggregated** price history for easy consumption  
✅ **Comprehensive testing** with included test script  
✅ **Detailed documentation** with usage examples  

These endpoints are now ready for production use and provide all the Samsung and LG product data with complete price history as requested.
