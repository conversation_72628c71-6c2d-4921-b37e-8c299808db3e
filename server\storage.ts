import {
  users,
  products,
  productPrices,
  productQuantities,
  stores,
  categories,
  notifications,
  settings,
  emailTemplates,
  emailRecipients,
  schedules,
  userFavorites,
  productChanges,
  changeNotificationSettings,
  displaySettings,
  userActivityLogs,
  priceHistory,
  priceTrends,
  priceAlerts,
  productReviews,
  reviewVotes,
  productComparisons,
  comparisonProducts,
  userWishlists,
  wishlistItems,
  userBehavior,
  productRecommendations,
  savedSearches,
  searchHistory,
  customReports,
  reportExecutions,
  type User,
  type UpsertUser,
  type Product,
  type InsertProduct,
  type ProductPrice,
  type InsertProductPrice,
  type ProductQuantity,
  type InsertProductQuantity,
  type Store,
  type InsertStore,
  type Category,
  type InsertCategory,
  type Notification,
  type InsertNotification,
  type Settings,
  type InsertSettings,
  type EmailTemplate,
  type InsertEmailTemplate,
  type EmailRecipient,
  type InsertEmailRecipient,
  type Schedule,
  type InsertSchedule,
  type UserFavorite,
  type InsertUserFavorite,
  type ProductChange,
  type InsertProductChange,
  type ChangeNotificationSettings,
  type InsertChangeNotificationSettings,
  type DisplaySettings,
  type InsertDisplaySettings,
  type UserActivityLog,
  type InsertUserActivityLog,
  type PriceHistory,
  type InsertPriceHistory,
  type PriceTrends,
  type InsertPriceTrends,
  type PriceAlerts,
  type InsertPriceAlerts,
  type ProductReview,
  type InsertProductReview,
  type ReviewVote,
  type InsertReviewVote,
  type ProductComparison,
  type InsertProductComparison,
  type ComparisonProduct,
  type InsertComparisonProduct,
  type UserWishlist,
  type InsertUserWishlist,
  type WishlistItem,
  type InsertWishlistItem,
  type UserBehavior,
  type InsertUserBehavior,
  type ProductRecommendation,
  type InsertProductRecommendation,
} from "@shared/schema";
import { db, pool } from "./db";
import { eq, desc, and, gte, lte, like, or, sql, asc, isNull, isNotNull, ilike, inArray } from "drizzle-orm";
import { alias } from "drizzle-orm/pg-core";
import {
  calculateModelSimilarity,
  generateFuzzySearchConditions,
  rankResultsBySimilarity
} from './similarity-search';

export interface IStorage {
  // User methods
  getUser(id: string): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  upsertUser(user: UpsertUser): Promise<User>;
  createUser(user: UpsertUser): Promise<User>;
  listUsers(): Promise<User[]>;
  updateUserRole(id: string, role: string): Promise<User | undefined>;
  deleteUser(id: string): Promise<boolean>;

  // Products methods
  getProduct(id: number): Promise<Product | undefined>;
  findProductByExternalId(storeId: number, externalId: string): Promise<Product | undefined>;
  getProductWithLatestPrice(id: number): Promise<any | undefined>;
  listProducts(params?: ListProductsParams): Promise<{ products: any[], total: number }>;
  createProduct(product: InsertProduct): Promise<Product>;
  upsertProduct(product: InsertProduct): Promise<Product>;
  updateProduct(id: number, product: Partial<InsertProduct>): Promise<Product | undefined>;
  deleteProduct(id: number): Promise<boolean>;

  // Direct database query method
  executeQuery(query: string, params?: any[]): Promise<any>;

  // Product price methods
  getProductPrice(id: number): Promise<ProductPrice | undefined>;
  listProductPrices(productId: number): Promise<ProductPrice[]>;
  createProductPrice(price: InsertProductPrice): Promise<ProductPrice>;
  getProductChanges(days: number): Promise<any[]>;
  getRecentChanges(limit?: number): Promise<any[]>;

  // Product quantity methods
  getProductQuantity(id: number): Promise<ProductQuantity | undefined>;
  listProductQuantities(productId: number): Promise<ProductQuantity[]>;
  createProductQuantity(quantity: InsertProductQuantity): Promise<ProductQuantity>;
  updateProductQuantity(id: number, quantity: Partial<InsertProductQuantity>): Promise<ProductQuantity | undefined>;
  deleteProductQuantity(id: number): Promise<boolean>;

  // Store methods
  getStore(id: number): Promise<Store | undefined>;
  getStoreBySlug(slug: string): Promise<Store | undefined>;
  listStores(): Promise<Store[]>;
  createStore(store: InsertStore): Promise<Store>;
  updateStore(id: number, store: Partial<InsertStore>): Promise<Store | undefined>;

  // Category methods
  getCategory(id: number): Promise<Category | undefined>;
  getCategoryBySlug(slug: string): Promise<Category | undefined>;
  listCategories(): Promise<Category[]>;
  createCategory(category: InsertCategory): Promise<Category>;
  clearCategories(): Promise<number>;

  // Notification methods
  getNotification(id: number): Promise<Notification | undefined>;
  listNotifications(userId: string, read?: boolean): Promise<Notification[]>;
  createNotification(notification: InsertNotification): Promise<Notification>;
  markNotificationAsRead(id: number): Promise<boolean>;
  markAllNotificationsAsRead(userId: string): Promise<boolean>;
  cleanupBadNotifications(): Promise<number>;

  // Settings methods
  getSetting(key: string): Promise<Settings | undefined>;
  updateSetting(key: string, value: any): Promise<Settings>;

  // Email methods
  getEmailTemplate(id: number): Promise<EmailTemplate | undefined>;
  getEmailTemplateByName(name: string): Promise<EmailTemplate | undefined>;
  listEmailTemplates(): Promise<EmailTemplate[]>;
  createEmailTemplate(template: InsertEmailTemplate): Promise<EmailTemplate>;
  updateEmailTemplate(id: number, template: Partial<InsertEmailTemplate>): Promise<EmailTemplate | undefined>;

  // Email recipients methods
  listEmailRecipients(): Promise<EmailRecipient[]>;
  createEmailRecipient(recipient: InsertEmailRecipient): Promise<EmailRecipient>;
  deleteEmailRecipient(id: number): Promise<boolean>;

  // Schedule methods
  getSchedule(id: number): Promise<Schedule | undefined>;
  listSchedules(filter?: { type?: string }): Promise<Schedule[]>;
  createSchedule(schedule: InsertSchedule): Promise<Schedule>;
  updateSchedule(id: number, schedule: Partial<InsertSchedule>): Promise<Schedule | undefined>;
  updateScheduleNextRun(id: number, lastRun: Date | null, nextRun: Date): Promise<Schedule | undefined>;
  updateScheduleLastRun(id: number, lastRun: Date): Promise<Schedule | undefined>;
  updateScheduleData(id: number, data: Partial<Schedule>): Promise<Schedule | undefined>;
  deleteSchedule(id: number): Promise<boolean>;

  // Stats methods
  getStats(): Promise<any>;

  // User Favorites methods
  getUserFavorites(userId: string): Promise<(UserFavorite & { product: Product & { store: Store; category: Category } })[]>;
  addToFavorites(favorite: InsertUserFavorite): Promise<UserFavorite>;
  removeFromFavorites(userId: string, productId: number): Promise<boolean>;
  isFavorite(userId: string, productId: number): Promise<boolean>;
  updateFavoriteSettings(userId: string, productId: number, settings: Partial<InsertUserFavorite>): Promise<UserFavorite | undefined>;
  getUsersFollowingProduct(productId: number): Promise<(UserFavorite & { user: User })[]>;

  // Product Changes methods
  createProductChange(change: InsertProductChange): Promise<ProductChange>;
  getProductChanges(days?: number): Promise<ProductChange[]>;
  getUnsentProductChanges(): Promise<ProductChange[]>;
  markProductChangesAsSent(changeIds: number[]): Promise<void>;
  clearAllProductChanges(): Promise<void>;

  // Change Notification Settings methods
  getChangeNotificationSettings(): Promise<ChangeNotificationSettings | undefined>;
  updateChangeNotificationSettings(settings: Partial<InsertChangeNotificationSettings>): Promise<ChangeNotificationSettings>;

  // User Activity Log methods
  createUserActivityLog(log: InsertUserActivityLog): Promise<UserActivityLog>;
  getUserActivityLogs(userId?: string, limit?: number, offset?: number): Promise<UserActivityLog[]>;
  getUserActivityLogsByType(activityType: string, userId?: string, limit?: number): Promise<UserActivityLog[]>;
  deleteOldActivityLogs(daysToKeep: number): Promise<number>;

  // Product Comparison methods
  createProductComparison(comparison: InsertProductComparison): Promise<ProductComparison | null>;
  getUserComparisons(userId: string): Promise<any[]>;
  getUserAndPublicComparisons(userId: string): Promise<any[]>;
  getAllComparisons(): Promise<any[]>;
  getComparisonWithProducts(comparisonId: number): Promise<any>;
  addProductToComparison(comparisonId: number, productId: number): Promise<ComparisonProduct | null>;
  removeProductFromComparison(comparisonId: number, productId: number): Promise<boolean>;
  updateComparisonVisibility(comparisonId: number, isPublic: boolean): Promise<boolean>;
  deleteComparison(comparisonId: number): Promise<boolean>;
}

export interface ListProductsParams {
  page?: number;
  limit?: number;
  search?: string;
  storeId?: number;
  storeIds?: number[];  // New field for multiple store IDs
  categoryId?: number;
  brand?: string;  // Brand name filter (legacy)
  brands?: string[];  // Multiple brands for bilingual filtering
  brandId?: string;  // Added brandId (legacy)
  minPrice?: number;  // Minimum price filter
  maxPrice?: number;  // Maximum price filter
  stockStatus?: string;  // Stock status filter (in_stock, low_stock, out_of_stock, unknown)
  inStock?: boolean;  // In stock only filter (legacy)
  sort?: string;
  order?: 'asc' | 'desc';
}

export class DatabaseStorage implements IStorage {
  // User methods
  async getUser(id: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user;
  }

  async upsertUser(userData: UpsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(userData)
      .onConflictDoUpdate({
        target: users.id,
        set: {
          ...userData,
          updatedAt: new Date(),
        },
      })
      .returning();
    return user;
  }

  async createUser(userData: UpsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(userData)
      .returning();
    return user;
  }

  async listUsers(): Promise<User[]> {
    return db.select().from(users).orderBy(users.firstName);
  }

  async updateUserRole(id: string, role: string): Promise<User | undefined> {
    const [user] = await db
      .update(users)
      .set({ role, updatedAt: new Date() })
      .where(eq(users.id, id))
      .returning();
    return user;
  }

  async deleteUser(id: string): Promise<boolean> {
    // First delete any related notifications
    await db.delete(notifications).where(eq(notifications.userId, id));

    // Then delete the user
    const result = await db.delete(users).where(eq(users.id, id)).returning();
    return result.length > 0;
  }

  // Products methods
  async getProduct(id: number): Promise<Product | undefined> {
    const [product] = await db.select().from(products).where(eq(products.id, id));
    return product;
  }

  async findProductByExternalId(storeId: number, externalId: string): Promise<Product | undefined> {
    const [product] = await db
      .select()
      .from(products)
      .where(and(
        eq(products.storeId, storeId),
        eq(products.externalId, externalId)
      ));
    return product;
  }

  async findProductBySku(storeId: number, sku: string): Promise<Product | undefined> {
    const [product] = await db
      .select()
      .from(products)
      .where(and(
        eq(products.storeId, storeId),
        eq(products.sku, sku)
      ));
    return product;
  }

  async getProductWithLatestPrice(id: number): Promise<any | undefined> {
    // Get the product with its related data
    const result = await db.query.products.findFirst({
      where: eq(products.id, id),
      with: {
        store: true,
        category: true,
      },
    });

    if (!result) return undefined;

    // Get latest price with stock information
    const [latestPrice] = await db
      .select({
        id: productPrices.id,
        productId: productPrices.productId,
        price: productPrices.price,
        currency: productPrices.currency,
        stock: productPrices.stock,
        status: productPrices.status,
        timestamp: productPrices.timestamp
      })
      .from(productPrices)
      .where(eq(productPrices.productId, id))
      .orderBy(desc(productPrices.timestamp))
      .limit(1);

    // Get previous price (second most recent)
    const [previousPrice] = await db
      .select()
      .from(productPrices)
      .where(eq(productPrices.productId, id))
      .orderBy(desc(productPrices.timestamp))
      .offset(1)
      .limit(1);

    // Get the pricing data directly from the products table (including member_price for Extra store three-tier pricing)
    const [productWithPricing] = await db
      .select({
        regular_price: products.regular_price,
        price: products.price,
        member_price: products.member_price  // CRITICAL FIX: Include member_price for Extra store three-tier pricing
      })
      .from(products)
      .where(eq(products.id, id));

    // Get latest quantity information
    const [latestQuantity] = await db
      .select()
      .from(productQuantities)
      .where(eq(productQuantities.productId, id))
      .orderBy(desc(productQuantities.updatedAt))
      .limit(1);

    // Ensure pricing fields are properly formatted as strings for consistent comparison
    const regular_price = productWithPricing?.regular_price
      ? parseFloat(productWithPricing.regular_price).toFixed(2)
      : null;

    // CRITICAL FIX: Include member_price for Extra store three-tier pricing
    const member_price = productWithPricing?.member_price
      ? parseFloat(productWithPricing.member_price).toFixed(2)
      : null;

    // If latestPrice is missing but we have a price in the products table, create a fallback price object
    let finalLatestPrice = latestPrice;
    if (!finalLatestPrice && productWithPricing?.price) {
      finalLatestPrice = {
        id: 0,
        productId: id,
        price: parseFloat(productWithPricing.price).toFixed(2),
        currency: "SAR",
        stock: 0,
        status: "unknown",
        timestamp: new Date()
      };
    }

    return {
      ...result,
      latestPrice: finalLatestPrice,
      previousPrice,
      regular_price,
      member_price,  // CRITICAL FIX: Include member_price in response for Extra store three-tier pricing
      latestQuantity,
    };
  }

  async listProducts(params: ListProductsParams = {}): Promise<{ products: any[], total: number }> {
    const maxRetries = 3;
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await this._listProductsInternal(params);
      } catch (error) {
        lastError = error as Error;
        console.error(`❌ listProducts attempt ${attempt}/${maxRetries} failed:`, error);

        // If it's a connection timeout, wait before retrying
        if (error instanceof Error && error.message.includes('timeout')) {
          if (attempt < maxRetries) {
            const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // Exponential backoff, max 5s
            console.log(`⏳ Retrying in ${delay}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }
        }

        // For non-timeout errors, don't retry
        throw error;
      }
    }

    throw lastError || new Error('Failed to list products after retries');
  }

  private async _listProductsInternal(params: ListProductsParams = {}): Promise<{ products: any[], total: number }> {
    const {
      page = 1,
      limit = 20,
      search = '',
      storeId,
      storeIds,  // Extract storeIds parameter
      categoryId,
      brand,     // Extract brand parameter (legacy)
      brands,    // Extract brands parameter for bilingual filtering
      brandId,   // Extract brandId parameter (legacy)
      minPrice,  // Extract minPrice parameter
      maxPrice,  // Extract maxPrice parameter
      stockStatus, // Extract stock status parameter
      inStock,   // Extract inStock parameter
      sort = 'updatedAt',
      order = 'desc',
    } = params;

    const offset = (page - 1) * limit;

    // Use optimized query with LATERAL JOINs for better performance
    return this._listProductsOptimized(params);
  }

  /**
   * Optimized products listing using raw SQL with LATERAL JOINs
   * This replaces the slow individual price/quantity queries with a single efficient query
   */
  private async _listProductsOptimized(params: ListProductsParams = {}): Promise<{ products: any[], total: number }> {
    const {
      page = 1,
      limit = 20,
      search = '',
      storeId,
      storeIds,
      categoryId,
      brand,
      brands,
      brandId,
      minPrice,
      maxPrice,
      stockStatus,
      inStock,
      sort = 'updatedAt',
      order = 'desc',
    } = params;

    const offset = (page - 1) * limit;
    const whereConditions = [];
    const queryParams = [];
    let paramIndex = 1;

    // Build WHERE conditions
    if (search) {
      whereConditions.push(`(
        p.name ILIKE $${paramIndex} OR
        p.brand ILIKE $${paramIndex} OR
        p.model ILIKE $${paramIndex} OR
        p.sku ILIKE $${paramIndex}
      )`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    if (storeId) {
      whereConditions.push(`p.store_id = $${paramIndex}`);
      queryParams.push(parseInt(storeId));
      paramIndex++;
    }

    if (storeIds && Array.isArray(storeIds)) {
      const storeIdPlaceholders = storeIds.map(() => `$${paramIndex++}`).join(',');
      whereConditions.push(`p.store_id IN (${storeIdPlaceholders})`);
      queryParams.push(...storeIds.map(id => parseInt(id)));
    }

    if (categoryId) {
      whereConditions.push(`p.category_id = $${paramIndex}`);
      queryParams.push(parseInt(categoryId));
      paramIndex++;
    }

    if (brand) {
      whereConditions.push(`p.brand ILIKE $${paramIndex}`);
      queryParams.push(`%${brand}%`);
      paramIndex++;
    }

    if (brands && Array.isArray(brands)) {
      const brandConditions = brands.map(() => `p.brand ILIKE $${paramIndex++}`).join(' OR ');
      whereConditions.push(`(${brandConditions})`);
      queryParams.push(...brands.map(b => `%${b}%`));
    }

    if (minPrice) {
      whereConditions.push(`p.price >= $${paramIndex}`);
      queryParams.push(parseFloat(minPrice));
      paramIndex++;
    }

    if (maxPrice) {
      whereConditions.push(`p.price <= $${paramIndex}`);
      queryParams.push(parseFloat(maxPrice));
      paramIndex++;
    }

    if (stockStatus) {
      whereConditions.push(`p.stock_status = $${paramIndex}`);
      queryParams.push(stockStatus);
      paramIndex++;
    }

    if (inStock) {
      whereConditions.push(`(p.stock > 0 OR p.stock_status = 'in_stock')`);
    }

    // Build ORDER BY clause
    const sortMapping = {
      'updatedAt': 'p.updated_at',
      'createdAt': 'p.created_at',
      'name': 'p.name',
      'price': 'p.price',
      'brand': 'p.brand'
    };

    const sortColumn = sortMapping[sort] || 'p.updated_at';
    const sortOrder = order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Optimized query with LATERAL JOINs for price and quantity data
    const optimizedQuery = `
      SELECT
        p.id,
        p.name,
        p.model,
        p.description,
        p.brand,
        p.size,
        p.uom,
        p.sku,
        p.price,
        p.regular_price,
        p.member_price,  -- CRITICAL FIX: Add member_price for Extra store three-tier pricing
        p.stock,
        p.quantity_available,
        p.stock_status,
        p.inventory_last_updated,
        p.external_id,
        p.url,
        p.image_url,
        p.created_at,
        p.updated_at,

        -- Store information
        s.id as store_id,
        s.name as store_name,
        s.slug as store_slug,
        s.url as store_url,
        s.active as store_active,

        -- Category information
        c.id as category_id,
        c.name as category_name,
        c.slug as category_slug,

        -- Latest price information (using LATERAL JOIN for performance)
        latest_price.price as latest_price,
        latest_price.currency as price_currency,
        latest_price.stock as price_stock,
        latest_price.status as price_status,
        latest_price.timestamp as price_updated_at,

        -- Latest quantity information (using LATERAL JOIN for performance)
        latest_quantity.quantity as latest_quantity,
        latest_quantity.status as quantity_status,
        latest_quantity.location as quantity_location,
        latest_quantity.updated_at as quantity_updated_at

      FROM products p

      -- Join store information
      LEFT JOIN stores s ON p.store_id = s.id

      -- Join category information
      LEFT JOIN categories c ON p.category_id = c.id

      -- LATERAL JOIN for latest price (much faster than individual queries)
      LEFT JOIN LATERAL (
        SELECT
          price,
          currency,
          stock,
          status,
          timestamp
        FROM product_prices pp
        WHERE pp.product_id = p.id
        ORDER BY pp.timestamp DESC
        LIMIT 1
      ) latest_price ON true

      -- LATERAL JOIN for latest quantity (much faster than individual queries)
      LEFT JOIN LATERAL (
        SELECT
          quantity,
          status,
          location,
          updated_at
        FROM product_quantities pq
        WHERE pq.product_id = p.id
        ORDER BY pq.updated_at DESC
        LIMIT 1
      ) latest_quantity ON true

      ${whereClause}
      ORDER BY ${sortColumn} ${sortOrder}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    // Count query for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM products p
      ${whereClause}
    `;

    queryParams.push(limit, offset);

    try {
      // Execute both queries concurrently for better performance
      const [productsResult, countResult] = await Promise.all([
        pool.query(optimizedQuery, queryParams),
        pool.query(countQuery, queryParams.slice(0, -2)) // Remove limit and offset for count
      ]);

      const products = productsResult.rows.map((row: any) => ({
          id: row.id,
          name: row.name,
          model: row.model,
          description: row.description,
          brand: row.brand,
          size: row.size,
          uom: row.uom,
          sku: row.sku,
          price: row.price,
          regular_price: row.regular_price,
          member_price: row.member_price,  // CRITICAL FIX: Include member_price for Extra store three-tier pricing
          stock: row.stock,
          quantity_available: row.quantity_available,
        stock_status: row.stock_status,
        inventory_last_updated: row.inventory_last_updated,
        external_id: row.external_id,
        url: row.url,
        image_url: row.image_url,
        created_at: row.created_at,
        updated_at: row.updated_at,

        // Store information
        store: row.store_id ? {
          id: row.store_id,
          name: row.store_name,
          slug: row.store_slug,
          url: row.store_url,
          active: row.store_active
        } : null,

        // Category information
        category: row.category_id ? {
          id: row.category_id,
          name: row.category_name,
          slug: row.category_slug
        } : null,

        // Latest price information
        latestPrice: row.latest_price ? {
          price: row.latest_price,
          currency: row.price_currency,
          stock: row.price_stock,
          status: row.price_status,
          timestamp: row.price_updated_at
        } : null,

        // Latest quantity information
        latestQuantity: row.latest_quantity !== null ? {
          quantity: row.latest_quantity,
          status: row.quantity_status,
          location: row.quantity_location,
          updated_at: row.quantity_updated_at
        } : null
      }));

      const total = parseInt(countResult.rows[0].total);

      console.log(`✅ Optimized query completed: ${products.length} products in ${Date.now() - Date.now()}ms`);

      return { products, total };

    } catch (error) {
      console.error('❌ Optimized products query failed:', error);
      // Fallback to original implementation if needed
      throw error;
    }
  }

  /**
   * Legacy implementation - kept for fallback if needed
   */
  private async _listProductsLegacy(params: ListProductsParams = {}): Promise<{ products: any[], total: number }> {
    // Simple fallback implementation
    console.log('⚠️ Using legacy products query fallback');

    const {
      page = 1,
      limit = 20,
      search = '',
      storeId,
      storeIds,
      categoryId,
      sort = 'updatedAt',
      order = 'desc',
    } = params;

    const offset = (page - 1) * limit;
    const whereConditions = [];

    if (search) {
      whereConditions.push(or(
        ilike(products.name, `%${search}%`),
        ilike(products.brand, `%${search}%`),
        ilike(products.model, `%${search}%`),
        ilike(products.sku, `%${search}%`)
      ));
    }

    if (storeId) {
      whereConditions.push(eq(products.storeId, parseInt(storeId)));
    }

    if (storeIds && Array.isArray(storeIds)) {
      whereConditions.push(inArray(products.storeId, storeIds.map(id => parseInt(id))));
    }

    if (categoryId) {
      whereConditions.push(eq(products.categoryId, parseInt(categoryId)));
    }

    // Simple fallback query
    const productsData = await db.query.products.findMany({
      where: whereConditions.length ? and(...whereConditions) : undefined,
      with: {
        store: true,
        category: true,
      },
      offset,
      limit,
      orderBy: order === 'asc' ? asc(products.updatedAt) : desc(products.updatedAt),
    });

    const count = await db.select({ count: sql<number>`count(*)` }).from(products)
      .where(whereConditions.length ? and(...whereConditions) : undefined);

    return {
      products: productsData,
      total: Number(count[0].count),
    };
  }

  async createProduct(product: InsertProduct): Promise<Product> {
    const [newProduct] = await db.insert(products).values(product).returning();
    return newProduct;
  }

  /**
   * List Samsung and LG refrigerator and washing machine products from specific stores
   * Only includes products from: Extra, Almanea, SWSG, and Black Box stores
   * Implements the same safety patterns as brand normalization system
   */
  async listSamsungLGAppliances(params: {
    page?: number;
    limit?: number;
    storeId?: number;
    category?: 'refrigerator' | 'washing machine';
  } = {}): Promise<{
    products: Array<{
      sku: string;
      name: string;
      brand: string;
      category: string;
      storeName: string;
      price: number;
      lastUpdateDate: string;
    }>;
    pagination: {
      page: number;
      limit: number;
      total: number;
      hasMore: boolean;
    };
  }> {
    const {
      page = 1,
      limit = 50,
      storeId,
      category,
    } = params;

    const offset = (page - 1) * limit;

    console.log(`🔍 [Samsung/LG Appliances] Fetching page ${page}, limit ${limit}, category: ${category || 'all'}, store: ${storeId || 'all'}`);

    // Build where conditions with comprehensive logging
    const whereConditions = [];

    // Brand filtering: Samsung and LG only (case-insensitive)
    const brandConditions = [
      eq(products.brand, 'Samsung'),
      eq(products.brand, 'LG'),
      // Handle case variations
      sql`LOWER(${products.brand}) = 'samsung'`,
      sql`LOWER(${products.brand}) = 'lg'`,
    ];
    whereConditions.push(or(...brandConditions));

    // Category filtering: refrigerators and washing machines
    const categoryConditions = [];

    if (category === 'refrigerator') {
      // Only refrigerators
      categoryConditions.push(
        sql`LOWER(${categories.name}) LIKE '%refrigerator%'`,
        sql`LOWER(${categories.name}) LIKE '%fridge%'`,
        sql`LOWER(${categories.name}) LIKE '%freezer%'`
      );
    } else if (category === 'washing machine') {
      // Only washing machines
      categoryConditions.push(
        sql`LOWER(${categories.name}) LIKE '%washing%'`,
        sql`LOWER(${categories.name}) LIKE '%washer%'`,
        sql`LOWER(${categories.name}) LIKE '%laundry%'`
      );
    } else {
      // Both refrigerators and washing machines
      categoryConditions.push(
        sql`LOWER(${categories.name}) LIKE '%refrigerator%'`,
        sql`LOWER(${categories.name}) LIKE '%fridge%'`,
        sql`LOWER(${categories.name}) LIKE '%freezer%'`,
        sql`LOWER(${categories.name}) LIKE '%washing%'`,
        sql`LOWER(${categories.name}) LIKE '%washer%'`,
        sql`LOWER(${categories.name}) LIKE '%laundry%'`
      );
    }

    whereConditions.push(or(...categoryConditions));

    // Store filtering: Only include specific stores (Extra, Almanea, SWSG, Black Box)
    const allowedStores = ['Extra', 'Almanea', 'SWSG', 'Black Box'];
    const storeConditions = allowedStores.map(storeName =>
      sql`LOWER(${stores.name}) = ${storeName.toLowerCase()}`
    );
    whereConditions.push(or(...storeConditions));

    // Additional store ID filtering (optional, but must be within allowed stores)
    if (storeId) {
      whereConditions.push(eq(products.storeId, storeId));
    }

    // Ensure products have valid data
    whereConditions.push(isNotNull(products.name));
    whereConditions.push(isNotNull(products.brand));
    whereConditions.push(isNotNull(products.price));

    try {
      // Get total count for pagination
      const [{ count }] = await db
        .select({ count: sql<number>`count(*)` })
        .from(products)
        .leftJoin(categories, eq(products.categoryId, categories.id))
        .leftJoin(stores, eq(products.storeId, stores.id))
        .where(and(...whereConditions));

      console.log(`📊 [Samsung/LG Appliances] Found ${count} total products matching criteria`);

      // Get products with related data
      const productsData = await db
        .select({
          id: products.id,
          sku: products.sku,
          name: products.name,
          brand: products.brand,
          price: products.price,
          updatedAt: products.updatedAt,
          categoryName: categories.name,
          storeName: stores.name,
        })
        .from(products)
        .leftJoin(categories, eq(products.categoryId, categories.id))
        .leftJoin(stores, eq(products.storeId, stores.id))
        .where(and(...whereConditions))
        .orderBy(desc(products.updatedAt)) // Most recently updated first
        .offset(offset)
        .limit(limit);

      console.log(`✅ [Samsung/LG Appliances] Retrieved ${productsData.length} products for page ${page}`);

      // Transform data to match API specification
      const transformedProducts = productsData.map(product => ({
        sku: product.sku || `PROD-${product.id}`, // Fallback SKU if not available
        name: product.name,
        brand: product.brand,
        category: this.normalizeCategoryName(product.categoryName),
        storeName: product.storeName || 'Unknown Store',
        price: parseFloat(product.price || '0'),
        lastUpdateDate: product.updatedAt ? product.updatedAt.toISOString() : new Date().toISOString(),
      }));

      const totalCount = Number(count);
      const hasMore = (page * limit) < totalCount;

      return {
        products: transformedProducts,
        pagination: {
          page,
          limit,
          total: totalCount,
          hasMore,
        },
      };

    } catch (error) {
      console.error('❌ [Samsung/LG Appliances] Database error:', error);
      throw new Error(`Failed to fetch Samsung/LG appliances: ${error.message}`);
    }
  }

  /**
   * Normalize category names for consistent API response
   * Following brand normalization safety patterns
   */
  private normalizeCategoryName(categoryName: string | null): string {
    if (!categoryName) return 'other';

    const normalized = categoryName.toLowerCase();

    if (normalized.includes('refrigerator') || normalized.includes('fridge') || normalized.includes('freezer')) {
      return 'refrigerator';
    }

    if (normalized.includes('washing') || normalized.includes('washer') || normalized.includes('laundry')) {
      return 'washing machine';
    }

    return 'other';
  }

  async upsertProduct(product: InsertProduct): Promise<Product> {
    try {
      const [upsertedProduct] = await db
        .insert(products)
        .values(product)
        .onConflictDoUpdate({
          target: products.externalId,
          set: {
            ...product,
            updatedAt: new Date(),
          },
        })
        .returning();
      return upsertedProduct;
    } catch (error) {
      // Enhanced error handling for duplicate key constraints
      if (error.message.includes('duplicate key') || error.message.includes('unique constraint')) {
        console.warn(`Duplicate key error for product ${product.name}, attempting recovery...`);

        // Try to find existing product and update it
        try {
          const existingProduct = await this.findProductByExternalId(product.storeId!, product.externalId!);
          if (existingProduct) {
            console.log(`Found existing product with external_id ${product.externalId}, updating...`);
            await this.updateProduct(existingProduct.id, product);
            return { ...existingProduct, ...product } as Product;
          }
        } catch (findError) {
          console.warn(`Could not find existing product: ${findError.message}`);
        }

        // If we can't find the existing product, generate a unique external_id
        const uniqueExternalId = `${product.externalId}-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`;
        console.warn(`Generating unique external_id: ${uniqueExternalId}`);

        const productWithUniqueId = {
          ...product,
          externalId: uniqueExternalId
        };

        const [upsertedProduct] = await db
          .insert(products)
          .values(productWithUniqueId)
          .returning();
        return upsertedProduct;
      }

      // Re-throw other errors
      throw error;
    }
  }

  async upsertProductBySku(product: InsertProduct): Promise<Product> {
    try {
      // Ensure SKU is provided, generate one if not
      const productWithSku = {
        ...product,
        sku: product.sku || `PRODUCT-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      };

      const [upsertedProduct] = await db
        .insert(products)
        .values(productWithSku)
        .onConflictDoUpdate({
          target: [products.sku, products.storeId],
          set: {
            ...productWithSku,
            updatedAt: new Date(),
          },
        })
        .returning();
      return upsertedProduct;
    } catch (error: any) {
      // Handle external_id unique constraint violations
      if (error.message.includes('duplicate key') || error.message.includes('unique constraint')) {
        console.warn(`Duplicate key error for product ${product.name}, attempting recovery...`);

        // Check if it's an external_id conflict
        if (error.message.includes('external_id') || error.constraint === 'products_external_id_unique') {
          console.log(`External ID conflict detected for ${product.externalId}`);

          // Try to find existing product by external_id and update it
          try {
            const existingProduct = await this.findProductByExternalId(product.storeId!, product.externalId!);
            if (existingProduct) {
              console.log(`Found existing product with external_id ${product.externalId}, updating...`);
              await this.updateProduct(existingProduct.id, product);
              return { ...existingProduct, ...product } as Product;
            }
          } catch (findError) {
            console.warn(`Could not find existing product by external_id: ${(findError as Error).message}`);
          }

          // If we can't find the existing product, generate a unique external_id
          const uniqueExternalId = `${product.externalId}-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`;
          console.warn(`Generating unique external_id: ${uniqueExternalId}`);

          const productWithUniqueId = {
            ...product,
            externalId: uniqueExternalId,
            sku: product.sku || `PRODUCT-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          };

          const [upsertedProduct] = await db
            .insert(products)
            .values(productWithUniqueId)
            .onConflictDoUpdate({
              target: [products.sku, products.storeId],
              set: {
                ...productWithUniqueId,
                updatedAt: new Date(),
              },
            })
            .returning();
          return upsertedProduct;
        }

        // Handle other duplicate key errors (like SKU conflicts)
        console.warn(`Other duplicate key error: ${error.message}`);

        // Try to find existing product by SKU and update it
        try {
          const existingProduct = await this.findProductBySku(product.storeId!, product.sku!);
          if (existingProduct) {
            console.log(`Found existing product with SKU ${product.sku}, updating...`);
            await this.updateProduct(existingProduct.id, product);
            return { ...existingProduct, ...product } as Product;
          }
        } catch (findError) {
          console.warn(`Could not find existing product by SKU: ${(findError as Error).message}`);
        }
      }

      // Re-throw other errors
      throw error;
    }
  }

  async updateProduct(id: number, product: Partial<InsertProduct>): Promise<Product | undefined> {
    try {
      // Get the current product data for price tracking
      const currentProduct = await this.getProduct(id);

      const [updatedProduct] = await db
        .update(products)
        .set({ ...product, updatedAt: new Date() })
        .where(eq(products.id, id))
        .returning();

      // Track price changes if price or stock changed
      if (updatedProduct && currentProduct && (product.price || product.stock !== undefined)) {
        const { priceTracker } = await import('./price-tracker');
        await priceTracker.trackPriceChange({
          productId: id,
          newPrice: product.price || currentProduct.price || '0',
          newRegularPrice: product.regular_price || currentProduct.regular_price || undefined,
          newStock: product.stock !== undefined ? product.stock : currentProduct.stock,
          previousPrice: currentProduct.price || undefined,
          previousRegularPrice: currentProduct.regular_price || undefined,
          previousStock: currentProduct.stock || undefined,
        });
      }

      return updatedProduct;
    } catch (error) {
      console.error("Error updating product:", error);
      throw error;
    }
  }

  async batchUpdateProductsWithTimestamp(productsToUpdate: Array<{id: number} & Partial<InsertProduct>>, timestamp: Date): Promise<void> {
    try {
      console.log(`🔄 Batch updating ${productsToUpdate.length} products with unified timestamp: ${timestamp.toISOString()}`);

      // Update products in batches to avoid overwhelming the database
      const batchSize = 50;
      for (let i = 0; i < productsToUpdate.length; i += batchSize) {
        const batch = productsToUpdate.slice(i, i + batchSize);

        // Update each product in the batch with the same timestamp
        await Promise.all(batch.map(async (productUpdate) => {
          const { id, ...updateData } = productUpdate;
          await db
            .update(products)
            .set({ ...updateData, updatedAt: timestamp })
            .where(eq(products.id, id));
        }));

        console.log(`✅ Updated batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(productsToUpdate.length / batchSize)} (${batch.length} products)`);
      }

      console.log(`✅ Batch update completed for ${productsToUpdate.length} products`);
    } catch (error) {
      console.error("Error in batch update:", error);
      throw error;
    }
  }

  async deleteProduct(id: number): Promise<boolean> {
    // First delete all related prices and quantities
    await db.delete(productPrices).where(eq(productPrices.productId, id));
    await db.delete(productQuantities).where(eq(productQuantities.productId, id));
    // Then delete the product
    const result = await db.delete(products).where(eq(products.id, id)).returning();
    return result.length > 0;
  }

  // Product price methods
  async getProductPrice(id: number): Promise<ProductPrice | undefined> {
    const [price] = await db.select().from(productPrices).where(eq(productPrices.id, id));
    return price;
  }

  async listProductPrices(productId: number): Promise<ProductPrice[]> {
    return db
      .select()
      .from(productPrices)
      .where(eq(productPrices.productId, productId))
      .orderBy(desc(productPrices.timestamp));
  }

  async createProductPrice(price: InsertProductPrice): Promise<ProductPrice> {
    const [newPrice] = await db.insert(productPrices).values(price).returning();
    return newPrice;
  }

  async bulkCreateProductPrices(prices: InsertProductPrice[]): Promise<ProductPrice[]> {
    try {
      if (!prices || prices.length === 0) {
        console.log('💾 Storage: No product prices to insert');
        return [];
      }

      console.log(`💾 Storage: Bulk creating ${prices.length} product price entries...`);

      // Insert in chunks to avoid memory issues and database limits
      const chunkSize = 500;
      const results: ProductPrice[] = [];

      for (let i = 0; i < prices.length; i += chunkSize) {
        const chunk = prices.slice(i, i + chunkSize);
        console.log(`💾 Storage: Inserting price chunk ${Math.floor(i / chunkSize) + 1}/${Math.ceil(prices.length / chunkSize)} (${chunk.length} entries)`);

        try {
          const chunkResults = await db
            .insert(productPrices)
            .values(chunk)
            .returning();

          results.push(...chunkResults);
          console.log(`✅ Storage: Successfully inserted price chunk of ${chunkResults.length} entries`);
        } catch (chunkError) {
          console.error(`❌ Storage: Error inserting price chunk starting at index ${i}:`, chunkError);
          // Continue with next chunk instead of failing completely
        }
      }

      console.log(`✅ Storage: Bulk price insert completed. Successfully created ${results.length}/${prices.length} product price entries`);
      return results;
    } catch (error) {
      console.error("❌ Storage: Error in bulk product price creation:", error);
      throw error;
    }
  }

  async getProductChanges(days: number): Promise<any[]> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const currentPrices = alias(productPrices, 'current_prices');
    const previousPrices = alias(productPrices, 'previous_prices');

    // This query gets products with price changes in the last days days
    const result = await db
      .select({
        productId: products.id,
        productName: products.name,
        productModel: products.model,
        storeName: stores.name,
        categoryName: categories.name,
        currentPrice: currentPrices.price,
        previousPrice: previousPrices.price,
        priceChange: sql<number>`${currentPrices.price} - ${previousPrices.price}`,
        // Use NULLIF to prevent division by zero
        percentChange: sql<number>`
          CASE
            WHEN ${previousPrices.price} = 0 OR ${previousPrices.price} IS NULL THEN 0
            ELSE (${currentPrices.price} - ${previousPrices.price}) / ${previousPrices.price} * 100
          END
        `,
        timestamp: currentPrices.timestamp,
      })
      .from(products)
      .innerJoin(stores, eq(products.storeId, stores.id))
      .innerJoin(categories, eq(products.categoryId, categories.id))
      .innerJoin(currentPrices, eq(products.id, currentPrices.productId))
      .innerJoin(
        previousPrices,
        and(
          eq(previousPrices.productId, currentPrices.productId),
          sql`${previousPrices.timestamp} < ${currentPrices.timestamp}`
        )
      )
      .where(
        and(
          gte(currentPrices.timestamp, startDate),
          sql`${currentPrices.timestamp} = (
            SELECT MAX(timestamp) FROM product_prices
            WHERE product_id = ${products.id}
          )`,
          sql`${previousPrices.timestamp} = (
            SELECT MAX(timestamp) FROM product_prices
            WHERE product_id = ${products.id} AND timestamp < ${currentPrices.timestamp}
          )`,
          // Filter out 0% price changes
          sql`ABS(${currentPrices.price} - ${previousPrices.price}) > 0.01`
        )
      )
      .orderBy(desc(currentPrices.timestamp));

    return result;
  }

  // Alias for the above method to maintain compatibility
  async getPriceChangesWithDetails(days: number): Promise<any[]> {
    return this.getProductChanges(days);
  }

  async getRecentChanges(limit: number = 10): Promise<any[]> {
    try {
      // Use the existing shared pool from db.ts instead of creating a new one
      const { pool } = await import('./db');

      // Optimized query using window functions for better performance
      const recentChangesQuery = `
        WITH ranked_prices AS (
          SELECT
            pp.product_id,
            pp.price,
            pp.stock,
            pp.timestamp,
            LAG(pp.price) OVER (PARTITION BY pp.product_id ORDER BY pp.timestamp) as prev_price,
            LAG(pp.stock) OVER (PARTITION BY pp.product_id ORDER BY pp.timestamp) as prev_stock,
            ROW_NUMBER() OVER (PARTITION BY pp.product_id ORDER BY pp.timestamp DESC) as rn
          FROM product_prices pp
          WHERE pp.timestamp >= NOW() - INTERVAL '7 days'
        ),
        recent_changes AS (
          SELECT
            rp.product_id,
            rp.price as current_price,
            rp.prev_price as previous_price,
            rp.price - rp.prev_price as price_change,
            CASE
              WHEN rp.prev_price = 0 OR rp.prev_price IS NULL THEN 0
              ELSE (rp.price - rp.prev_price) / rp.prev_price * 100
            END as percent_change,
            rp.stock,
            rp.prev_stock as previous_stock,
            rp.stock - rp.prev_stock as stock_change,
            rp.timestamp
          FROM ranked_prices rp
          WHERE rp.rn = 1
            AND rp.prev_price IS NOT NULL
            AND (
              ABS(rp.price - rp.prev_price) > 0.01
              OR rp.stock != rp.prev_stock
            )
        )
        SELECT
          rc.product_id as "productId",
          p.name as "productName",
          s.name as "storeName",
          c.name as "categoryName",
          rc.current_price as "currentPrice",
          rc.previous_price as "previousPrice",
          rc.price_change as "priceChange",
          rc.percent_change as "percentChange",
          rc.stock,
          rc.previous_stock as "previousStock",
          rc.stock_change as "stockChange",
          rc.timestamp
        FROM recent_changes rc
        JOIN products p ON rc.product_id = p.id
        JOIN stores s ON p.store_id = s.id
        LEFT JOIN categories c ON p.category_id = c.id
        ORDER BY rc.timestamp DESC
        LIMIT $1
      `;

      const result = await pool.query(recentChangesQuery, [limit]);
      // Don't close the shared pool - it's managed by the application lifecycle

      return result.rows;
    } catch (error) {
      console.error('Error in optimized getRecentChanges:', error);
      // Fallback to simpler query if the optimized one fails
      return this.getRecentChangesSimple(limit);
    }
  }

  // Fallback simple recent changes method
  async getRecentChangesSimple(limit: number = 10): Promise<any[]> {
    // Simple query that just gets recent product updates
    const result = await db
      .select({
        productId: products.id,
        productName: products.name,
        storeName: stores.name,
        categoryName: categories.name,
        currentPrice: sql<number>`${products.price}`,
        previousPrice: sql<number>`${products.price}`,
        priceChange: sql<number>`0`,
        percentChange: sql<number>`0`,
        stock: products.stock,
        previousStock: products.stock,
        stockChange: sql<number>`0`,
        timestamp: products.updatedAt,
      })
      .from(products)
      .innerJoin(stores, eq(products.storeId, stores.id))
      .leftJoin(categories, eq(products.categoryId, categories.id))
      .where(gte(products.updatedAt, sql`NOW() - INTERVAL '24 hours'`))
      .orderBy(desc(products.updatedAt))
      .limit(limit);

    return result;
  }

  // Product quantity methods
  async getProductQuantity(id: number): Promise<ProductQuantity | undefined> {
    const [quantity] = await db.select().from(productQuantities).where(eq(productQuantities.id, id));
    return quantity;
  }

  async listProductQuantities(productId: number): Promise<ProductQuantity[]> {
    return db
      .select()
      .from(productQuantities)
      .where(eq(productQuantities.productId, productId))
      .orderBy(desc(productQuantities.updatedAt));
  }

  async createProductQuantity(quantity: InsertProductQuantity): Promise<ProductQuantity> {
    const [newQuantity] = await db.insert(productQuantities).values(quantity).returning();
    return newQuantity;
  }

  async updateProductQuantity(id: number, quantity: Partial<InsertProductQuantity>): Promise<ProductQuantity | undefined> {
    const [updatedQuantity] = await db
      .update(productQuantities)
      .set({ ...quantity, updatedAt: new Date() })
      .where(eq(productQuantities.id, id))
      .returning();
    return updatedQuantity;
  }

  async deleteProductQuantity(id: number): Promise<boolean> {
    const result = await db.delete(productQuantities).where(eq(productQuantities.id, id)).returning();
    return result.length > 0;
  }

  // Store methods
  async getStore(id: number): Promise<Store | undefined> {
    const [store] = await db.select().from(stores).where(eq(stores.id, id));
    return store;
  }

  async getStoreBySlug(slug: string): Promise<Store | undefined> {
    const [store] = await db.select().from(stores).where(eq(stores.slug, slug));
    return store;
  }

  async listStores(): Promise<Store[]> {
    // Return all stores, including inactive ones, to ensure all products can be filtered
    // Use distinct to avoid duplicates
    const result = await db
      .select({
        id: stores.id,
        name: stores.name,
        slug: stores.slug,
        active: stores.active,
        createdAt: stores.createdAt,
        updatedAt: stores.updatedAt
      })
      .from(stores)
      .orderBy(stores.name);

    // Create a map to track unique store names (case-insensitive)
    const uniqueStores = new Map();

    // Keep only the first occurrence of each store name (case-insensitive)
    for (const store of result) {
      const normalizedName = store.name.trim().toLowerCase();
      if (!uniqueStores.has(normalizedName)) {
        uniqueStores.set(normalizedName, store);
      }
    }

    // Ensure we have the required stores
    const requiredStores = [
      { name: "Alkhunaizan", slug: "alkhunaizan" },
      { name: "Bin Momen", slug: "binmomen" },
      { name: "Tamkeen", slug: "tamkeen" }
    ];

    // Check if we need to create any required stores
    for (const storeInfo of requiredStores) {
      const storeExists = Array.from(uniqueStores.values()).some(
        store => store.slug === storeInfo.slug ||
                store.name.toLowerCase() === storeInfo.name.toLowerCase()
      );

      if (!storeExists) {
        try {
          // Create the missing store
          const newStore = await this.createStore({
            name: storeInfo.name,
            slug: storeInfo.slug,
            url: `https://${storeInfo.slug.toLowerCase()}.com`,
            active: true
          });

          // Add to our result set
          uniqueStores.set(storeInfo.name.toLowerCase(), newStore);
          console.log(`Created missing store: ${storeInfo.name}`);
        } catch (error) {
          console.error(`Error creating store ${storeInfo.name}:`, error);
        }
      }
    }

    // Return the unique stores
    return Array.from(uniqueStores.values());
  }

  async createStore(store: InsertStore): Promise<Store> {
    const [newStore] = await db.insert(stores).values(store).returning();
    return newStore;
  }

  async updateStore(id: number, store: Partial<InsertStore>): Promise<Store | undefined> {
    const [updatedStore] = await db
      .update(stores)
      .set({ ...store, updatedAt: new Date() })
      .where(eq(stores.id, id))
      .returning();
    return updatedStore;
  }

  // Category methods
  async getCategory(id: number): Promise<Category | undefined> {
    const [category] = await db.select().from(categories).where(eq(categories.id, id));
    return category;
  }

  async getCategoryBySlug(slug: string): Promise<Category | undefined> {
    const [category] = await db.select().from(categories).where(eq(categories.slug, slug));
    return category;
  }

  async listCategories(): Promise<Category[]> {
    // Get all categories from the database
    const allCategories = await db.select().from(categories).orderBy(categories.name);

    // Create a map to track unique normalized category names
    const uniqueCategories = new Map<string, Category>();

    // Process each category to handle case sensitivity and merge similar categories
    for (const category of allCategories) {
      // Normalize the category name (lowercase, trim whitespace)
      const normalizedName = category.name.trim().toLowerCase();

      // If we haven't seen this category before, add it
      if (!uniqueCategories.has(normalizedName)) {
        uniqueCategories.set(normalizedName, category);
      } else {
        // If we have seen this category before, keep the one with the lower ID (likely the original)
        const existingCategory = uniqueCategories.get(normalizedName)!;
        if (category.id < existingCategory.id) {
          uniqueCategories.set(normalizedName, category);
        }
      }
    }

    // Return the unique categories
    return Array.from(uniqueCategories.values());
  }

  async createCategory(category: InsertCategory): Promise<Category> {
    const [newCategory] = await db.insert(categories).values(category).returning();
    return newCategory;
  }

  async clearCategories(): Promise<number> {
    // Delete all categories from the database
    const result = await db.delete(categories).returning();
    return result.length;
  }

  // Brand methods
  async listBrands(): Promise<string[]> {
    // Get distinct brands from products table
    const result = await db
      .select({ brand: products.brand })
      .from(products)
      .where(sql`${products.brand} IS NOT NULL AND ${products.brand} != ''`)
      .groupBy(products.brand)
      .orderBy(products.brand);

    // Create a case-insensitive map to track unique brand names
    const brandMap = new Map<string, string>();

    // Process each brand to handle case sensitivity and whitespace
    for (const row of result) {
      const normalizedBrand = row.brand.trim();
      const lowerBrand = normalizedBrand.toLowerCase();

      // Only add if we haven't seen this brand before (case-insensitive)
      if (!brandMap.has(lowerBrand)) {
        brandMap.set(lowerBrand, normalizedBrand);
      }
    }

    // Convert map values to array and sort
    const uniqueBrands = Array.from(brandMap.values()).sort();

    return uniqueBrands;
  }

  // Get brands with product counts for filter component
  async listBrandsWithCounts(): Promise<Array<{ name: string; productCount: number }>> {
    // Get brands with their product counts
    const result = await db
      .select({
        brand: products.brand,
        count: sql<number>`COUNT(*)::int`
      })
      .from(products)
      .where(sql`${products.brand} IS NOT NULL AND ${products.brand} != ''`)
      .groupBy(products.brand)
      .orderBy(desc(sql`COUNT(*)`), products.brand);

    // Create a case-insensitive map to track unique brand names and their counts
    const brandMap = new Map<string, { name: string; productCount: number }>();

    // Process each brand to handle case sensitivity and whitespace
    for (const row of result) {
      const normalizedBrand = row.brand.trim();
      const lowerBrand = normalizedBrand.toLowerCase();

      // Only add if we haven't seen this brand before (case-insensitive)
      // If we have seen it, add the counts together
      if (!brandMap.has(lowerBrand)) {
        brandMap.set(lowerBrand, {
          name: normalizedBrand,
          productCount: row.count
        });
      } else {
        const existing = brandMap.get(lowerBrand)!;
        existing.productCount += row.count;
      }
    }

    // Convert map values to array and sort by product count (descending), then by name
    const brandsWithCounts = Array.from(brandMap.values()).sort((a, b) => {
      if (b.productCount !== a.productCount) {
        return b.productCount - a.productCount;
      }
      return a.name.localeCompare(b.name);
    });

    return brandsWithCounts;
  }

  // Notification methods
  async getNotification(id: number): Promise<Notification | undefined> {
    const [notification] = await db.select().from(notifications).where(eq(notifications.id, id));
    return notification;
  }

  async listNotifications(userId: string, read?: boolean): Promise<Notification[]> {
    try {
      let whereConditions = [eq(notifications.userId, userId)];

      if (read !== undefined) {
        whereConditions.push(eq(notifications.read, read));
      }

      const query = db
        .select({
          id: notifications.id,
          title: notifications.title,
          message: notifications.message,
          type: notifications.type,
          read: notifications.read,
          productId: notifications.productId,
          createdAt: notifications.createdAt,
          productName: products.name,
        })
        .from(notifications)
        .leftJoin(products, eq(notifications.productId, products.id))
        .where(and(...whereConditions))
        .orderBy(desc(notifications.createdAt))
        .limit(100); // Limit to prevent performance issues

      const result = await query;
      return result;
    } catch (error) {
      console.error('Error fetching notifications:', error);
      return [];
    }
  }

  async createNotification(notification: InsertNotification): Promise<Notification> {
    try {
      // Validate notification data before insertion
      if (!notification.title || !notification.message || !notification.type) {
        throw new Error('Missing required notification fields: title, message, and type are required');
      }

      // Ensure userId is valid (not null/undefined)
      if (!notification.userId) {
        console.warn('Creating notification without userId - using default admin user');
        notification.userId = '1'; // Default to admin user
      }

      // Validate productId if provided
      if (notification.productId !== undefined && notification.productId !== null) {
        if (typeof notification.productId !== 'number' || notification.productId <= 0) {
          console.warn('Invalid productId provided, setting to null:', notification.productId);
          notification.productId = null;
        }
      }

      // Create notification without createdAt - let defaultNow() handle it
      const notificationData = {
        userId: notification.userId,
        title: notification.title.trim(),
        message: notification.message.trim(),
        type: notification.type,
        read: notification.read ?? false,
        productId: notification.productId || null
        // Don't include createdAt - let the database default handle it
      };

      const [newNotification] = await db.insert(notifications).values(notificationData).returning();
      return newNotification;
    } catch (error) {
      console.error('Error creating notification:', error);
      console.error('Notification data:', notification);

      // Re-throw with more context
      throw new Error(`Failed to create notification: ${error.message}`);
    }
  }

  async markNotificationAsRead(id: number): Promise<boolean> {
    const result = await db
      .update(notifications)
      .set({ read: true })
      .where(eq(notifications.id, id))
      .returning();
    return result.length > 0;
  }

  async markAllNotificationsAsRead(userId: string): Promise<boolean> {
    const result = await db
      .update(notifications)
      .set({ read: true })
      .where(and(eq(notifications.userId, userId), eq(notifications.read, false)))
      .returning();
    return result.length > 0;
  }

  // Settings methods
  async getSetting(key: string): Promise<Settings | undefined> {
    const [setting] = await db.select().from(settings).where(eq(settings.key, key));
    return setting;
  }

  async updateSetting(key: string, value: any): Promise<Settings> {
    const [setting] = await db
      .insert(settings)
      .values({ key, value })
      .onConflictDoUpdate({
        target: settings.key,
        set: { value, updatedAt: new Date() },
      })
      .returning();
    return setting;
  }

  // Email methods
  async getEmailTemplate(id: number): Promise<EmailTemplate | undefined> {
    const [template] = await db.select().from(emailTemplates).where(eq(emailTemplates.id, id));
    return template;
  }

  async getEmailTemplateByName(name: string): Promise<EmailTemplate | undefined> {
    const [template] = await db.select().from(emailTemplates).where(eq(emailTemplates.name, name));
    return template;
  }

  async listEmailTemplates(): Promise<EmailTemplate[]> {
    const { withRetry } = await import('./db.js');
    return withRetry(() =>
      db.select().from(emailTemplates).orderBy(emailTemplates.name)
    );
  }

  async createEmailTemplate(template: InsertEmailTemplate): Promise<EmailTemplate> {
    const [newTemplate] = await db.insert(emailTemplates).values(template).returning();
    return newTemplate;
  }

  async updateEmailTemplate(id: number, template: Partial<InsertEmailTemplate>): Promise<EmailTemplate | undefined> {
    const [updatedTemplate] = await db
      .update(emailTemplates)
      .set({ ...template, updatedAt: new Date() })
      .where(eq(emailTemplates.id, id))
      .returning();
    return updatedTemplate;
  }

  // Email recipients methods
  async listEmailRecipients(): Promise<EmailRecipient[]> {
    try {
      return db.select().from(emailRecipients).orderBy(emailRecipients.email);
    } catch (error: any) {
      // If the new columns don't exist yet, fall back to basic query
      if (error.code === '42703') {
        console.log('New notification columns not found, using fallback query');
        return db.select({
          id: emailRecipients.id,
          email: emailRecipients.email,
          name: emailRecipients.name,
          type: emailRecipients.type,
          createdAt: emailRecipients.createdAt,
          updatedAt: emailRecipients.updatedAt,
          // Default values for missing columns
          receiveScheduledReports: sql<boolean>`true`,
          receiveChangeNotifications: sql<boolean>`true`,
          receiveNewProductNotifications: sql<boolean>`true`,
        }).from(emailRecipients).orderBy(emailRecipients.email);
      }
      throw error;
    }
  }

  async createEmailRecipient(recipient: InsertEmailRecipient): Promise<EmailRecipient> {
    const [newRecipient] = await db.insert(emailRecipients).values(recipient).returning();
    return newRecipient;
  }

  async deleteEmailRecipient(id: number): Promise<boolean> {
    const result = await db.delete(emailRecipients).where(eq(emailRecipients.id, id)).returning();
    return result.length > 0;
  }

  // Schedule methods
  async getSchedule(id: number): Promise<Schedule | undefined> {
    const [schedule] = await db.select().from(schedules).where(eq(schedules.id, id));
    return schedule;
  }

  async listSchedules(filter?: { type?: string }): Promise<Schedule[]> {
    let query = db.select().from(schedules);

    // Apply filters if provided
    if (filter) {
      if (filter.type) {
        query = query.where(eq(schedules.type, filter.type));
      }
    }

    // Order by type
    query = query.orderBy(schedules.type);

    return query;
  }

  async createSchedule(schedule: InsertSchedule): Promise<Schedule> {
    const [newSchedule] = await db.insert(schedules).values(schedule).returning();
    return newSchedule;
  }

  async updateSchedule(id: number, schedule: Partial<InsertSchedule>): Promise<Schedule | undefined> {
    const [updatedSchedule] = await db
      .update(schedules)
      .set({ ...schedule, updatedAt: new Date() })
      .where(eq(schedules.id, id))
      .returning();
    return updatedSchedule;
  }

  async updateScheduleNextRun(id: number, lastRun: Date | null, nextRun: Date): Promise<Schedule | undefined> {
    // If lastRun is null, only update the nextRun field
    if (lastRun === null) {
      const [updatedSchedule] = await db
        .update(schedules)
        .set({ nextRun, updatedAt: new Date() })
        .where(eq(schedules.id, id))
        .returning();
      return updatedSchedule;
    } else {
      // Otherwise update both lastRun and nextRun
      const [updatedSchedule] = await db
        .update(schedules)
        .set({ lastRun, nextRun, updatedAt: new Date() })
        .where(eq(schedules.id, id))
        .returning();
      return updatedSchedule;
    }
  }

  // Enhanced updateSchedule method that accepts any partial schedule data
  async updateScheduleData(id: number, data: Partial<Schedule>): Promise<Schedule | undefined> {
    try {
      // Remove id from data if present
      const { id: _, ...updateData } = data;

      // Add updatedAt timestamp
      const dataWithTimestamp = {
        ...updateData,
        updatedAt: new Date()
      };

      // Update the schedule
      const [updatedSchedule] = await db
        .update(schedules)
        .set(dataWithTimestamp)
        .where(eq(schedules.id, id))
        .returning();

      return updatedSchedule;
    } catch (error) {
      console.error(`Error updating schedule #${id}:`, error);
      throw error;
    }
  }

  async updateScheduleLastRun(id: number, lastRun: Date): Promise<Schedule | undefined> {
    try {
      console.log(`Updating last run time for schedule #${id} to ${lastRun.toISOString()}`);

      const [updatedSchedule] = await db
        .update(schedules)
        .set({ lastRun, updatedAt: new Date() })
        .where(eq(schedules.id, id))
        .returning();

      if (updatedSchedule) {
        console.log(`Successfully updated last run time for schedule #${id}`);
      } else {
        console.error(`Failed to update last run time for schedule #${id} - schedule not found`);
      }

      return updatedSchedule;
    } catch (error) {
      console.error(`Error updating last run time for schedule #${id}:`, error);
      throw error;
    }
  }

  async deleteSchedule(id: number): Promise<boolean> {
    try {
      const result = await db.delete(schedules).where(eq(schedules.id, id)).returning();
      console.log(`✅ Storage: Deleted schedule ${id}`);
      return result.length > 0;
    } catch (error) {
      console.error("❌ Storage: Error deleting schedule:", error);
      return false;
    }
  }

  // Stats methods
  async getStats(): Promise<any> {
    try {
      // Use the existing shared pool from db.ts instead of creating a new one
      const { pool } = await import('./db');

      // Single optimized query to get all stats at once
      const statsQuery = `
        WITH recent_changes AS (
          SELECT
            product_id,
            price,
            stock,
            timestamp,
            LAG(price) OVER (PARTITION BY product_id ORDER BY timestamp) as prev_price,
            LAG(stock) OVER (PARTITION BY product_id ORDER BY timestamp) as prev_stock,
            ROW_NUMBER() OVER (PARTITION BY product_id ORDER BY timestamp DESC) as rn
          FROM product_prices
          WHERE timestamp >= NOW() - INTERVAL '24 hours'
        ),
        latest_changes AS (
          SELECT
            product_id,
            CASE WHEN price != prev_price AND ABS(price - prev_price) > 0.01 THEN 1 ELSE 0 END as has_price_change,
            CASE WHEN stock != prev_stock THEN 1 ELSE 0 END as has_stock_change
          FROM recent_changes
          WHERE rn = 1 AND prev_price IS NOT NULL
        ),
        store_stats AS (
          SELECT
            s.id,
            s.name,
            s.slug,
            COUNT(p.id) as product_count,
            MAX(p.updated_at) as last_update,
            EXTRACT(EPOCH FROM (NOW() - MAX(p.updated_at)))/3600 as hours_since_update
          FROM stores s
          LEFT JOIN products p ON s.id = p.store_id
          WHERE s.active = true
          GROUP BY s.id, s.name, s.slug
        )
        SELECT
          (SELECT COUNT(*) FROM products) as total_products,
          (SELECT COUNT(*) FROM stores WHERE active = true) as active_stores,
          (SELECT COALESCE(SUM(has_price_change), 0) FROM latest_changes) as price_changes,
          (SELECT COALESCE(SUM(has_stock_change), 0) FROM latest_changes) as stock_changes,
          (SELECT json_agg(json_build_object(
            'id', id,
            'name', name,
            'slug', slug,
            'productCount', product_count,
            'lastUpdate', last_update,
            'hoursSinceUpdate', hours_since_update
          )) FROM store_stats) as stores
      `;

      const result = await pool.query(statsQuery);
      // Don't close the shared pool - it's managed by the application lifecycle

      const stats = result.rows[0];

      return {
        totalProducts: Number(stats.total_products),
        priceChanges: Number(stats.price_changes),
        activeStores: Number(stats.active_stores),
        stockChanges: Number(stats.stock_changes),
        stores: stats.stores || []
      };
    } catch (error) {
      console.error('Error in optimized getStats:', error);
      // Fallback to simpler query if the optimized one fails
      return this.getStatsSimple();
    }
  }

  // Fallback simple stats method
  async getStatsSimple(): Promise<any> {
    // Get total products
    const [{ totalProducts }] = await db
      .select({ totalProducts: sql<number>`count(*)` })
      .from(products);

    // Get active stores count and their product counts with timestamps
    const storeStats = await db
      .select({
        id: stores.id,
        name: stores.name,
        slug: stores.slug,
        productCount: sql<number>`count(${products.id})`,
        lastUpdate: sql<Date>`max(${products.updatedAt})`,
        hoursSinceUpdate: sql<number>`extract(epoch from (now() - max(${products.updatedAt})))/3600`
      })
      .from(stores)
      .leftJoin(products, eq(stores.id, products.storeId))
      .where(eq(stores.active, true))
      .groupBy(stores.id, stores.name, stores.slug)
      .orderBy(stores.name);

    return {
      totalProducts: Number(totalProducts),
      priceChanges: 0, // Simplified for performance
      activeStores: storeStats.length,
      stockChanges: 0, // Simplified for performance
      stores: storeStats.map(store => ({
        id: store.id,
        name: store.name,
        slug: store.slug,
        productCount: Number(store.productCount),
        lastUpdate: store.lastUpdate,
        hoursSinceUpdate: Number(store.hoursSinceUpdate) || null
      }))
    };
  }

  // Direct database query method
  async executeQuery(query: string, params?: any[]): Promise<any> {
    try {
      // Use the existing pool from db.ts
      const { pool } = await import('./db');

      // Execute the query
      const result = await pool.query(query, params);

      return result;
    } catch (error) {
      console.error("Error executing direct database query:", error);
      throw error;
    }
  }

  // User Favorites methods implementation
  async getUserFavorites(userId: string): Promise<(UserFavorite & { product: Product & { store: Store; category: Category } })[]> {
    const favorites = await db.query.userFavorites.findMany({
      where: eq(userFavorites.userId, userId),
      with: {
        product: {
          with: {
            store: true,
            category: true,
          },
        },
      },
      orderBy: desc(userFavorites.createdAt),
    });

    return favorites;
  }

  async addToFavorites(favorite: InsertUserFavorite): Promise<UserFavorite> {
    // Check if already exists
    const existing = await db
      .select()
      .from(userFavorites)
      .where(
        and(
          eq(userFavorites.userId, favorite.userId),
          eq(userFavorites.productId, favorite.productId)
        )
      )
      .limit(1);

    if (existing.length > 0) {
      // Update existing favorite settings
      const [updated] = await db
        .update(userFavorites)
        .set({
          ...favorite,
          updatedAt: new Date()
        })
        .where(eq(userFavorites.id, existing[0].id))
        .returning();
      return updated;
    }

    // Create new favorite
    const [newFavorite] = await db
      .insert(userFavorites)
      .values(favorite)
      .returning();
    return newFavorite;
  }

  async removeFromFavorites(userId: string, productId: number): Promise<boolean> {
    const result = await db
      .delete(userFavorites)
      .where(
        and(
          eq(userFavorites.userId, userId),
          eq(userFavorites.productId, productId)
        )
      )
      .returning();
    return result.length > 0;
  }

  async isFavorite(userId: string, productId: number): Promise<boolean> {
    const [favorite] = await db
      .select()
      .from(userFavorites)
      .where(
        and(
          eq(userFavorites.userId, userId),
          eq(userFavorites.productId, productId)
        )
      )
      .limit(1);
    return !!favorite;
  }

  async updateFavoriteSettings(userId: string, productId: number, settings: Partial<InsertUserFavorite>): Promise<UserFavorite | undefined> {
    const [updated] = await db
      .update(userFavorites)
      .set({
        ...settings,
        updatedAt: new Date()
      })
      .where(
        and(
          eq(userFavorites.userId, userId),
          eq(userFavorites.productId, productId)
        )
      )
      .returning();
    return updated;
  }

  async getUsersFollowingProduct(productId: number): Promise<(UserFavorite & { user: User })[]> {
    const followers = await db.query.userFavorites.findMany({
      where: and(
        eq(userFavorites.productId, productId),
        eq(userFavorites.emailNotifications, true)
      ),
      with: {
        user: true,
      },
    });

    return followers;
  }

  // Product Changes methods
  async createProductChange(change: InsertProductChange): Promise<ProductChange> {
    const [newChange] = await db.insert(productChanges).values(change).returning();
    return newChange;
  }

  async getProductChangeRecords(days: number = 7): Promise<ProductChange[]> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    return db
      .select()
      .from(productChanges)
      .where(gte(productChanges.createdAt, startDate))
      .orderBy(desc(productChanges.createdAt));
  }

  async cleanupBadNotifications(): Promise<number> {
    try {
      // Find notifications with "undefined" or "NaN" in the message
      const badNotifications = await db
        .select()
        .from(notifications)
        .where(
          or(
            like(notifications.message, '%undefined%'),
            like(notifications.message, '%NaN%'),
            like(notifications.message, '%null%')
          )
        );

      console.log(`Found ${badNotifications.length} bad notifications`);

      if (badNotifications.length === 0) {
        return 0;
      }

      // Delete bad notifications
      await db
        .delete(notifications)
        .where(
          or(
            like(notifications.message, '%undefined%'),
            like(notifications.message, '%NaN%'),
            like(notifications.message, '%null%')
          )
        );

      console.log(`🗑️  Deleted ${badNotifications.length} bad notifications`);
      return badNotifications.length;

    } catch (error) {
      console.error('❌ Error during cleanup:', error);
      return 0;
    }
  }

  async getUnsentProductChanges(): Promise<ProductChange[]> {
    return db
      .select()
      .from(productChanges)
      .where(eq(productChanges.notificationSent, false))
      .orderBy(desc(productChanges.createdAt));
  }

  async markProductChangesAsSent(changeIds: number[]): Promise<void> {
    if (changeIds.length === 0) return;

    console.log('🔍 markProductChangesAsSent called with:', { changeIds, type: typeof changeIds, isArray: Array.isArray(changeIds) });

    // Ensure changeIds is an array of numbers
    const validChangeIds = Array.isArray(changeIds) ? changeIds.filter(id => typeof id === 'number' && !isNaN(id)) : [];
    console.log('🔍 Valid change IDs:', validChangeIds);

    if (validChangeIds.length === 0) {
      console.log('⚠️ No valid change IDs to mark as sent');
      return;
    }

    await db
      .update(productChanges)
      .set({ notificationSent: true })
      .where(inArray(productChanges.id, validChangeIds));
  }

  async clearAllProductChanges(): Promise<void> {
    console.log('🗑️ Clearing all product changes...');
    const result = await db.delete(productChanges);
    console.log('✅ Cleared all product changes');
  }

  // Change Notification Settings methods
  async getChangeNotificationSettings(): Promise<ChangeNotificationSettings | undefined> {
    try {
      const [settings] = await db.select().from(changeNotificationSettings).limit(1);
      return settings;
    } catch (error: any) {
      // If the table doesn't exist yet, return default settings
      if (error.code === '42P01') {
        console.log('Change notification settings table not found, returning defaults');
        return {
          id: 1,
          enablePriceChangeNotifications: true,
          enableStockChangeNotifications: true,
          enableNewProductNotifications: true,
          minimumPriceChangePercentage: "0.00",
          emailSubject: "Product Changes Summary",
          createdAt: new Date(),
          updatedAt: new Date(),
        };
      }
      throw error;
    }
  }

  // Display Settings methods
  async getDisplaySettings(): Promise<DisplaySettings | undefined> {
    try {
      const [settings] = await db.select().from(displaySettings).limit(1);
      return settings;
    } catch (error: any) {
      // If the table doesn't exist yet, return default settings
      if (error.code === '42P01') {
        console.log('Display settings table not found, returning defaults');
        return {
          id: 1,
          hideStockQuantities: false,
          showStockStatus: true,
          stockDisplayMode: "full",
          createdAt: new Date(),
          updatedAt: new Date(),
        };
      }
      throw error;
    }
  }

  async updateDisplaySettings(settings: Partial<InsertDisplaySettings>): Promise<DisplaySettings> {
    try {
      // Check if settings exist
      const existing = await this.getDisplaySettings();

      if (existing && existing.id) {
        // Update existing settings
        const [updated] = await db
          .update(displaySettings)
          .set({
            ...settings,
            updatedAt: new Date(),
          })
          .where(eq(displaySettings.id, existing.id))
          .returning();
        return updated;
      } else {
        // Create new settings
        const [created] = await db
          .insert(displaySettings)
          .values({
            ...settings,
            createdAt: new Date(),
            updatedAt: new Date(),
          })
          .returning();
        return created;
      }
    } catch (error) {
      console.error('Error updating display settings:', error);
      throw error;
    }
  }

  async updateChangeNotificationSettings(settings: Partial<InsertChangeNotificationSettings>): Promise<ChangeNotificationSettings> {
    // First try to update existing settings
    const [updated] = await db
      .update(changeNotificationSettings)
      .set({ ...settings, updatedAt: new Date() })
      .returning();

    // If no settings exist, create new ones
    if (!updated) {
      const [created] = await db
        .insert(changeNotificationSettings)
        .values(settings)
        .returning();
      return created;
    }

    return updated;
  }

  // User Activity Log methods
  async createUserActivityLog(log: InsertUserActivityLog): Promise<UserActivityLog> {
    try {
      console.log('💾 Storage: Attempting to create user activity log:', log);
      const [newLog] = await db
        .insert(userActivityLogs)
        .values(log)
        .returning();
      console.log('✅ Storage: Successfully created activity log with ID:', newLog.id);
      return newLog;
    } catch (error) {
      console.error("❌ Storage: Error creating user activity log:", error);
      throw error;
    }
  }

  async getUserActivityLogs(userId?: string, limit: number = 100, offset: number = 0): Promise<UserActivityLog[]> {
    try {
      let query = db
        .select({
          id: userActivityLogs.id,
          userId: userActivityLogs.userId,
          activityType: userActivityLogs.activityType,
          description: userActivityLogs.description,
          details: userActivityLogs.details,
          ipAddress: userActivityLogs.ipAddress,
          userAgent: userActivityLogs.userAgent,
          sessionId: userActivityLogs.sessionId,
          createdAt: userActivityLogs.createdAt,
          username: users.username,
        })
        .from(userActivityLogs)
        .leftJoin(users, eq(userActivityLogs.userId, users.id))
        .orderBy(desc(userActivityLogs.createdAt))
        .limit(limit)
        .offset(offset);

      if (userId) {
        query = query.where(eq(userActivityLogs.userId, userId));
      }

      console.log('💾 Storage: Executing query for user activity logs...');
      const result = await query;
      console.log('✅ Storage: Successfully fetched', result.length, 'activity logs');
      return result;
    } catch (error) {
      console.error("Error fetching user activity logs:", error);
      return [];
    }
  }

  async getUserActivityLogsByType(activityType: string, userId?: string, limit: number = 50): Promise<UserActivityLog[]> {
    try {
      let query = db
        .select()
        .from(userActivityLogs)
        .where(eq(userActivityLogs.activityType, activityType))
        .orderBy(desc(userActivityLogs.createdAt))
        .limit(limit);

      if (userId) {
        query = query.where(and(
          eq(userActivityLogs.activityType, activityType),
          eq(userActivityLogs.userId, userId)
        ));
      }

      return query;
    } catch (error) {
      console.error(`Error fetching user activity logs by type ${activityType}:`, error);
      return [];
    }
  }

  async deleteOldActivityLogs(daysToKeep: number = 30): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const result = await db
        .delete(userActivityLogs)
        .where(lte(userActivityLogs.createdAt, cutoffDate))
        .returning();

      return result.length;
    } catch (error) {
      console.error("Error deleting old activity logs:", error);
      return 0;
    }
  }

  // Price History Methods
  async createPriceHistory(priceHistoryData: InsertPriceHistory): Promise<PriceHistory> {
    try {
      console.log('💾 Storage: Creating price history entry:', priceHistoryData);
      const [newEntry] = await db
        .insert(priceHistory)
        .values(priceHistoryData)
        .returning();
      console.log('✅ Storage: Successfully created price history entry with ID:', newEntry.id);
      return newEntry;
    } catch (error) {
      console.error("❌ Storage: Error creating price history:", error);
      throw error;
    }
  }

  async bulkCreatePriceHistory(priceHistoryData: InsertPriceHistory[]): Promise<PriceHistory[]> {
    try {
      if (!priceHistoryData || priceHistoryData.length === 0) {
        console.log('💾 Storage: No price history data to insert');
        return [];
      }

      console.log(`💾 Storage: Bulk creating ${priceHistoryData.length} price history entries...`);

      // Insert in chunks to avoid memory issues and database limits
      const chunkSize = 500;
      const results: PriceHistory[] = [];

      for (let i = 0; i < priceHistoryData.length; i += chunkSize) {
        const chunk = priceHistoryData.slice(i, i + chunkSize);
        console.log(`💾 Storage: Inserting chunk ${Math.floor(i / chunkSize) + 1}/${Math.ceil(priceHistoryData.length / chunkSize)} (${chunk.length} entries)`);

        try {
          const chunkResults = await db
            .insert(priceHistory)
            .values(chunk)
            .returning();

          results.push(...chunkResults);
          console.log(`✅ Storage: Successfully inserted chunk of ${chunkResults.length} entries`);
        } catch (chunkError) {
          console.error(`❌ Storage: Error inserting chunk starting at index ${i}:`, chunkError);
          // Continue with next chunk instead of failing completely
        }
      }

      console.log(`✅ Storage: Bulk insert completed. Successfully created ${results.length}/${priceHistoryData.length} price history entries`);
      return results;
    } catch (error) {
      console.error("❌ Storage: Error in bulk price history creation:", error);
      throw error;
    }
  }

  async getPriceHistory(productId: number, limit: number = 100): Promise<PriceHistory[]> {
    try {
      console.log('💾 Storage: getPriceHistory called with productId:', productId, 'limit:', limit);

      // Query the actual price_history table
      const rawData = await db
        .select({
          id: priceHistory.id,
          productId: priceHistory.productId,
          price: priceHistory.price,
          regularPrice: priceHistory.regularPrice,
          memberPrice: priceHistory.memberPrice,
          stock: priceHistory.stock,
          stockStatus: priceHistory.stockStatus,
          quantityAvailable: priceHistory.quantityAvailable,
          currency: priceHistory.currency,
          recordedAt: priceHistory.recordedAt,
          changeType: priceHistory.changeType,
          changeAmount: priceHistory.changeAmount,
          changePercentage: priceHistory.changePercentage,
          previousPrice: priceHistory.previousPrice,
          storeId: priceHistory.storeId,
          createdAt: priceHistory.createdAt
        })
        .from(priceHistory)
        .where(eq(priceHistory.productId, productId))
        .orderBy(desc(priceHistory.recordedAt))
        .limit(limit);

      console.log('✅ Storage: Found', rawData.length, 'price history entries from price_history table');

      // If no data in price_history table, fallback to product_prices table
      if (rawData.length === 0) {
        console.log('⚠️ Storage: No data in price_history table, falling back to product_prices');

        const fallbackData = await db
          .select({
            id: productPrices.id,
            productId: productPrices.productId,
            price: productPrices.price,
            currency: productPrices.currency,
            stock: productPrices.stock,
            status: productPrices.status,
            timestamp: productPrices.timestamp
          })
          .from(productPrices)
          .where(eq(productPrices.productId, productId))
          .orderBy(desc(productPrices.timestamp))
          .limit(limit);

        console.log('✅ Storage: Found', fallbackData.length, 'entries from product_prices table');

        // Transform product_prices data to PriceHistory format
        return fallbackData.map(entry => ({
          id: entry.id,
          productId: entry.productId,
          price: entry.price,
          regularPrice: entry.price, // Use same price as regular price
          memberPrice: null,
          stock: entry.stock,
          stockStatus: entry.status,
          quantityAvailable: null,
          currency: entry.currency,
          recordedAt: entry.timestamp,
          changeType: 'price_update',
          changeAmount: null,
          changePercentage: null,
          previousPrice: null,
          storeId: null,
          createdAt: entry.timestamp,
          updatedAt: entry.timestamp,
        }));
      }

      // Transform price_history data to PriceHistory format
      const result = rawData.map(entry => ({
        id: entry.id,
        productId: entry.productId,
        price: entry.price,
        regularPrice: entry.regularPrice,
        memberPrice: entry.memberPrice,
        stock: entry.stock,
        stockStatus: entry.stockStatus,
        quantityAvailable: entry.quantityAvailable,
        currency: entry.currency,
        recordedAt: entry.recordedAt,
        changeType: entry.changeType,
        changeAmount: entry.changeAmount,
        changePercentage: entry.changePercentage,
        previousPrice: entry.previousPrice,
        storeId: entry.storeId,
        createdAt: entry.createdAt,
        updatedAt: entry.recordedAt, // Use recordedAt as updatedAt
      }));

      console.log('✅ Storage: Successfully transformed', result.length, 'price history entries');
      return result;
    } catch (error) {
      console.error("❌ Storage: Error fetching price history:", error);
      return [];
    }
  }

  async getPriceHistoryByDateRange(
    productId: number,
    startDate: Date,
    endDate: Date
  ): Promise<PriceHistory[]> {
    try {
      console.log('💾 Storage: Fetching price history for date range:', { productId, startDate, endDate });

      // Query the actual price_history table first
      const rawData = await db
        .select({
          id: priceHistory.id,
          productId: priceHistory.productId,
          price: priceHistory.price,
          regularPrice: priceHistory.regularPrice,
          memberPrice: priceHistory.memberPrice,
          stock: priceHistory.stock,
          stockStatus: priceHistory.stockStatus,
          quantityAvailable: priceHistory.quantityAvailable,
          currency: priceHistory.currency,
          recordedAt: priceHistory.recordedAt,
          changeType: priceHistory.changeType,
          changeAmount: priceHistory.changeAmount,
          changePercentage: priceHistory.changePercentage,
          previousPrice: priceHistory.previousPrice,
          storeId: priceHistory.storeId,
          createdAt: priceHistory.createdAt
        })
        .from(priceHistory)
        .where(
          and(
            eq(priceHistory.productId, productId),
            gte(priceHistory.recordedAt, startDate),
            lte(priceHistory.recordedAt, endDate)
          )
        )
        .orderBy(asc(priceHistory.recordedAt));

      console.log('✅ Storage: Found', rawData.length, 'entries from price_history table for date range');

      // If no data in price_history table, fallback to product_prices table
      if (rawData.length === 0) {
        console.log('⚠️ Storage: No data in price_history table, falling back to product_prices for date range');

        const fallbackData = await db
          .select({
            id: productPrices.id,
            productId: productPrices.productId,
            price: productPrices.price,
            currency: productPrices.currency,
            stock: productPrices.stock,
            status: productPrices.status,
            timestamp: productPrices.timestamp
          })
          .from(productPrices)
          .where(
            and(
              eq(productPrices.productId, productId),
              gte(productPrices.timestamp, startDate),
              lte(productPrices.timestamp, endDate)
            )
          )
          .orderBy(asc(productPrices.timestamp));

        console.log('✅ Storage: Found', fallbackData.length, 'entries from product_prices table for date range');

        // Transform product_prices data to PriceHistory format
        return fallbackData.map(entry => ({
          id: entry.id,
          productId: entry.productId,
          price: entry.price,
          regularPrice: entry.price, // Use same price as regular price
          memberPrice: null,
          stock: entry.stock,
          stockStatus: entry.status,
          quantityAvailable: null,
          currency: entry.currency,
          recordedAt: entry.timestamp,
          changeType: 'price_update',
          changeAmount: null,
          changePercentage: null,
          previousPrice: null,
          storeId: null,
          createdAt: entry.timestamp,
          updatedAt: entry.timestamp,
        }));
      }

      // Transform price_history data to PriceHistory format
      const result = rawData.map(entry => ({
        id: entry.id,
        productId: entry.productId,
        price: entry.price,
        regularPrice: entry.regularPrice,
        memberPrice: entry.memberPrice,
        stock: entry.stock,
        stockStatus: entry.stockStatus,
        quantityAvailable: entry.quantityAvailable,
        currency: entry.currency,
        recordedAt: entry.recordedAt,
        changeType: entry.changeType,
        changeAmount: entry.changeAmount,
        changePercentage: entry.changePercentage,
        previousPrice: entry.previousPrice,
        storeId: entry.storeId,
        createdAt: entry.createdAt,
        updatedAt: entry.recordedAt, // Use recordedAt as updatedAt
      }));

      console.log('✅ Storage: Successfully transformed', result.length, 'price history entries for date range');
      return result;
    } catch (error) {
      console.error("❌ Storage: Error fetching price history by date range:", error);
      return [];
    }
  }

  async getMultipleProductsPriceHistory(productIds: number[], days: number = 30): Promise<PriceHistory[]> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      console.log('💾 Storage: Fetching price history for multiple products:', { productIds, days });

      // Validate and sanitize productIds array
      if (!Array.isArray(productIds) || productIds.length === 0) {
        console.warn('⚠️ Storage: Invalid or empty productIds array provided');
        return [];
      }

      // Convert to proper PostgreSQL array format
      const validProductIds = productIds.filter(id => Number.isInteger(id) && id > 0);
      if (validProductIds.length === 0) {
        console.warn('⚠️ Storage: No valid product IDs found');
        return [];
      }

      // Query the correct table (productPrices) and map to PriceHistory format
      const rawData = await db
        .select({
          id: productPrices.id,
          productId: productPrices.productId,
          price: productPrices.price,
          currency: productPrices.currency,
          stock: productPrices.stock,
          status: productPrices.status,
          timestamp: productPrices.timestamp
        })
        .from(productPrices)
        .where(
          and(
            sql`${productPrices.productId} = ANY(${sql`ARRAY[${sql.join(validProductIds.map(id => sql`${id}::integer`), sql`, `)}]::integer[]`})`,
            gte(productPrices.timestamp, startDate)
          )
        )
        .orderBy(asc(productPrices.productId), desc(productPrices.timestamp));

      // Transform to PriceHistory format
      const result = rawData.map(entry => ({
        id: entry.id,
        productId: entry.productId,
        price: entry.price,
        regularPrice: null,
        memberPrice: null,
        stock: entry.stock,
        stockStatus: entry.status,
        quantityAvailable: null,
        currency: entry.currency,
        recordedAt: entry.timestamp,
        changeType: 'price_update' as const,
        changeAmount: null,
        changePercentage: null,
        previousPrice: null,
        storeId: null,
        createdAt: entry.timestamp,
        updatedAt: entry.timestamp,
      }));

      console.log('✅ Storage: Successfully fetched', result.length, 'price history entries for multiple products');
      return result;
    } catch (error) {
      console.error("❌ Storage: Error fetching multiple products price history:", error);
      return [];
    }
  }

  // ===== PRODUCT REVIEWS & RATINGS =====

  async createProductReview(review: InsertProductReview): Promise<ProductReview | null> {
    try {
      const result = await db.insert(productReviews).values(review).returning();
      console.log('✅ Storage: Created product review for product', review.productId);
      return result[0] || null;
    } catch (error) {
      console.error("❌ Storage: Error creating product review:", error);
      return null;
    }
  }

  async getProductReviews(productId: number, limit: number = 10, offset: number = 0): Promise<ProductReview[]> {
    try {
      const result = await db
        .select()
        .from(productReviews)
        .where(eq(productReviews.productId, productId))
        .orderBy(desc(productReviews.createdAt))
        .limit(limit)
        .offset(offset);

      console.log('✅ Storage: Fetched', result.length, 'reviews for product', productId);
      return result;
    } catch (error) {
      console.error("❌ Storage: Error fetching product reviews:", error);
      return [];
    }
  }

  async getUserReview(userId: string, productId: number): Promise<ProductReview | null> {
    try {
      const result = await db
        .select()
        .from(productReviews)
        .where(and(eq(productReviews.userId, userId), eq(productReviews.productId, productId)))
        .limit(1);

      return result[0] || null;
    } catch (error) {
      console.error("❌ Storage: Error fetching user review:", error);
      return null;
    }
  }

  async updateProductReview(reviewId: number, updates: Partial<InsertProductReview>): Promise<ProductReview | null> {
    try {
      const result = await db
        .update(productReviews)
        .set({ ...updates, updatedAt: new Date() })
        .where(eq(productReviews.id, reviewId))
        .returning();

      console.log('✅ Storage: Updated product review', reviewId);
      return result[0] || null;
    } catch (error) {
      console.error("❌ Storage: Error updating product review:", error);
      return null;
    }
  }

  async deleteProductReview(reviewId: number): Promise<boolean> {
    try {
      await db.delete(productReviews).where(eq(productReviews.id, reviewId));
      console.log('✅ Storage: Deleted product review', reviewId);
      return true;
    } catch (error) {
      console.error("❌ Storage: Error deleting product review:", error);
      return false;
    }
  }

  async voteOnReview(vote: InsertReviewVote): Promise<ReviewVote | null> {
    try {
      const result = await db.insert(reviewVotes).values(vote).returning();
      console.log('✅ Storage: Created review vote');
      return result[0] || null;
    } catch (error) {
      console.error("❌ Storage: Error creating review vote:", error);
      return null;
    }
  }

  async getProductRatingStats(productId: number): Promise<{
    averageRating: number;
    totalReviews: number;
    ratingDistribution: { [key: number]: number };
  }> {
    try {
      const result = await db
        .select({
          rating: productReviews.rating,
          count: sql<number>`count(*)::int`,
        })
        .from(productReviews)
        .where(eq(productReviews.productId, productId))
        .groupBy(productReviews.rating);

      const totalReviews = result.reduce((sum, item) => sum + item.count, 0);
      const weightedSum = result.reduce((sum, item) => sum + (item.rating * item.count), 0);
      const averageRating = totalReviews > 0 ? weightedSum / totalReviews : 0;

      const ratingDistribution: { [key: number]: number } = {};
      for (let i = 1; i <= 5; i++) {
        ratingDistribution[i] = 0;
      }
      result.forEach(item => {
        ratingDistribution[item.rating] = item.count;
      });

      return {
        averageRating: Math.round(averageRating * 10) / 10,
        totalReviews,
        ratingDistribution,
      };
    } catch (error) {
      console.error("❌ Storage: Error fetching product rating stats:", error);
      return { averageRating: 0, totalReviews: 0, ratingDistribution: {} };
    }
  }

  // ===== PRODUCT COMPARISONS =====

  async createProductComparison(comparison: InsertProductComparison): Promise<ProductComparison | null> {
    try {
      const result = await db.insert(productComparisons).values(comparison).returning();
      console.log('✅ Storage: Created product comparison');
      return result[0] || null;
    } catch (error) {
      console.error("❌ Storage: Error creating product comparison:", error);
      return null;
    }
  }

  async getUserComparisons(userId: string): Promise<any[]> {
    try {
      const result = await db
        .select({
          comparison: productComparisons,
          productCount: sql<number>`count(${comparisonProducts.productId})::int`,
        })
        .from(productComparisons)
        .leftJoin(comparisonProducts, eq(productComparisons.id, comparisonProducts.comparisonId))
        .where(eq(productComparisons.userId, userId))
        .groupBy(productComparisons.id)
        .orderBy(desc(productComparisons.createdAt));

      const comparisonsWithCount = result.map(row => ({
        ...row.comparison,
        productCount: row.productCount || 0,
        isOwner: true, // User's own comparisons are always owned by them
      }));

      console.log('✅ Storage: Fetched', result.length, 'comparisons for user', userId);
      return comparisonsWithCount;
    } catch (error) {
      console.error("❌ Storage: Error fetching user comparisons:", error);
      return [];
    }
  }

  async getUserAndPublicComparisons(userId: string): Promise<any[]> {
    try {
      const result = await db
        .select({
          comparison: productComparisons,
          productCount: sql<number>`count(${comparisonProducts.productId})::int`,
          user: users,
        })
        .from(productComparisons)
        .leftJoin(comparisonProducts, eq(productComparisons.id, comparisonProducts.comparisonId))
        .leftJoin(users, eq(productComparisons.userId, users.id))
        .where(
          or(
            eq(productComparisons.userId, userId), // User's own comparisons
            eq(productComparisons.isPublic, true)   // Public comparisons from others
          )
        )
        .groupBy(productComparisons.id, users.id)
        .orderBy(desc(productComparisons.createdAt));

      const comparisonsWithCount = result.map(row => ({
        ...row.comparison,
        productCount: row.productCount || 0,
        isOwner: row.comparison.userId === userId, // Add flag to indicate ownership
        creatorName: row.user ? (
          row.user.firstName && row.user.lastName
            ? `${row.user.firstName} ${row.user.lastName}`.trim()
            : row.user.username || 'Unknown User'
        ) : 'Unknown User',
      }));

      console.log('✅ Storage: Fetched', result.length, 'comparisons (own + public) for user', userId);
      return comparisonsWithCount;
    } catch (error) {
      console.error("❌ Storage: Error fetching user and public comparisons:", error);
      return [];
    }
  }

  async getAllComparisons(): Promise<any[]> {
    try {
      console.log('🔍 DEBUG: getAllComparisons() method called at', new Date().toISOString());
      console.log('🚨 ADMIN FETCH: Starting to fetch all comparisons for admin user');

      const result = await db
        .select({
          comparison: productComparisons,
          productCount: sql<number>`count(${comparisonProducts.productId})::int`,
          user: users,
        })
        .from(productComparisons)
        .leftJoin(comparisonProducts, eq(productComparisons.id, comparisonProducts.comparisonId))
        .leftJoin(users, eq(productComparisons.userId, users.id))
        .groupBy(productComparisons.id, users.id)
        .orderBy(desc(productComparisons.createdAt));

      console.log('🚨 ADMIN FETCH: Database query completed, processing results...');

      const comparisonsWithCount = result.map(row => {
        const creatorName = row.user ? (
          row.user.firstName && row.user.lastName
            ? `${row.user.firstName} ${row.user.lastName}`.trim()
            : row.user.username || 'Unknown User'
        ) : 'Unknown User';

        console.log(`🔍 Admin view - Comparison ${row.comparison.id}: userId=${row.comparison.userId}, user=${JSON.stringify(row.user)}, creatorName="${creatorName}"`);

        const finalComparison = {
          ...row.comparison,
          productCount: row.productCount || 0,
          isOwner: false, // Admin view - no ownership flag needed
          creatorName,
        };

        console.log(`🚨 FINAL COMPARISON ${row.comparison.id}:`, JSON.stringify(finalComparison, null, 2));

        return finalComparison;
      });

      console.log('✅ Storage: Fetched all', result.length, 'comparisons for admin');
      console.log('🚨 ADMIN FETCH: Returning comparisons with creatorName field');
      return comparisonsWithCount;
    } catch (error) {
      console.error("❌ Storage: Error fetching all comparisons:", error);
      return [];
    }
  }

  async addProductToComparison(comparisonId: number, productId: number): Promise<ComparisonProduct | null> {
    try {
      const result = await db.insert(comparisonProducts).values({
        comparisonId,
        productId,
      }).returning();

      console.log('✅ Storage: Added product', productId, 'to comparison', comparisonId);
      return result[0] || null;
    } catch (error) {
      console.error("❌ Storage: Error adding product to comparison:", error);
      return null;
    }
  }

  async removeProductFromComparison(comparisonId: number, productId: number): Promise<boolean> {
    try {
      await db
        .delete(comparisonProducts)
        .where(and(
          eq(comparisonProducts.comparisonId, comparisonId),
          eq(comparisonProducts.productId, productId)
        ));

      console.log('✅ Storage: Removed product', productId, 'from comparison', comparisonId);
      return true;
    } catch (error) {
      console.error("❌ Storage: Error removing product from comparison:", error);
      return false;
    }
  }

  async getComparisonWithProducts(comparisonId: number): Promise<{
    comparison: ProductComparison;
    products: Product[];
  } | null> {
    try {
      // Get comparison details
      const comparisonResult = await db
        .select()
        .from(productComparisons)
        .where(eq(productComparisons.id, comparisonId))
        .limit(1);

      if (!comparisonResult[0]) {
        return null;
      }

      // Get products in comparison
      const productsResult = await db
        .select({
          product: products,
          store: stores,
          category: categories,
        })
        .from(comparisonProducts)
        .innerJoin(products, eq(comparisonProducts.productId, products.id))
        .leftJoin(stores, eq(products.storeId, stores.id))
        .leftJoin(categories, eq(products.categoryId, categories.id))
        .where(eq(comparisonProducts.comparisonId, comparisonId))
        .orderBy(comparisonProducts.addedAt);

      const productsWithDetails = productsResult.map(row => ({
        ...row.product,
        store: row.store,
        category: row.category,
      }));

      return {
        comparison: comparisonResult[0],
        products: productsWithDetails,
      };
    } catch (error) {
      console.error("❌ Storage: Error fetching comparison with products:", error);
      return null;
    }
  }

  async updateComparisonVisibility(comparisonId: number, isPublic: boolean): Promise<boolean> {
    try {
      await db
        .update(productComparisons)
        .set({ isPublic })
        .where(eq(productComparisons.id, comparisonId));

      console.log('✅ Storage: Updated comparison', comparisonId, 'visibility to', isPublic ? 'public' : 'private');
      return true;
    } catch (error) {
      console.error("❌ Storage: Error updating comparison visibility:", error);
      return false;
    }
  }

  async deleteComparison(comparisonId: number): Promise<boolean> {
    try {
      await db.delete(productComparisons).where(eq(productComparisons.id, comparisonId));
      console.log('✅ Storage: Deleted comparison', comparisonId);
      return true;
    } catch (error) {
      console.error("❌ Storage: Error deleting comparison:", error);
      return false;
    }
  }

  async getAllComparisonsWithProducts(): Promise<any[]> {
    try {
      // Get all comparisons with product counts
      const comparisonsResult = await db
        .select({
          comparison: productComparisons,
          productCount: sql<number>`count(${comparisonProducts.productId})::int`,
        })
        .from(productComparisons)
        .leftJoin(comparisonProducts, eq(productComparisons.id, comparisonProducts.comparisonId))
        .groupBy(productComparisons.id)
        .orderBy(desc(productComparisons.createdAt));

      const comparisonsWithProducts = [];

      // For each comparison, get its products
      for (const row of comparisonsResult) {
        const comparison = {
          ...row.comparison,
          productCount: row.productCount || 0,
          products: []
        };

        // Get products for this comparison
        const productsResult = await db
          .select({
            product: products,
            store: stores,
            category: categories,
          })
          .from(comparisonProducts)
          .innerJoin(products, eq(comparisonProducts.productId, products.id))
          .leftJoin(stores, eq(products.storeId, stores.id))
          .leftJoin(categories, eq(products.categoryId, categories.id))
          .where(eq(comparisonProducts.comparisonId, comparison.id))
          .orderBy(comparisonProducts.addedAt);

        comparison.products = productsResult.map(productRow => ({
          ...productRow.product,
          store: productRow.store,
          category: productRow.category,
        }));

        comparisonsWithProducts.push(comparison);
      }

      console.log('✅ Storage: Fetched', comparisonsWithProducts.length, 'comparisons with products');
      return comparisonsWithProducts;
    } catch (error) {
      console.error("❌ Storage: Error fetching all comparisons with products:", error);
      return [];
    }
  }

  // ===== ENHANCED WISHLISTS =====

  async createUserWishlist(wishlist: InsertUserWishlist): Promise<UserWishlist | null> {
    try {
      const result = await db.insert(userWishlists).values(wishlist).returning();
      console.log('✅ Storage: Created wishlist for user', wishlist.userId);
      return result[0] || null;
    } catch (error) {
      console.error("❌ Storage: Error creating wishlist:", error);
      return null;
    }
  }

  async getUserWishlists(userId: string): Promise<UserWishlist[]> {
    try {
      const result = await db
        .select()
        .from(userWishlists)
        .where(eq(userWishlists.userId, userId))
        .orderBy(desc(userWishlists.isDefault), desc(userWishlists.createdAt));

      console.log('✅ Storage: Fetched', result.length, 'wishlists for user', userId);
      return result;
    } catch (error) {
      console.error("❌ Storage: Error fetching user wishlists:", error);
      return [];
    }
  }

  async getDefaultWishlist(userId: string): Promise<UserWishlist | null> {
    try {
      const result = await db
        .select()
        .from(userWishlists)
        .where(and(eq(userWishlists.userId, userId), eq(userWishlists.isDefault, true)))
        .limit(1);

      return result[0] || null;
    } catch (error) {
      console.error("❌ Storage: Error fetching default wishlist:", error);
      return null;
    }
  }

  async addToWishlist(item: InsertWishlistItem): Promise<WishlistItem | null> {
    try {
      const result = await db.insert(wishlistItems).values(item).returning();
      console.log('✅ Storage: Added product', item.productId, 'to wishlist', item.wishlistId);
      return result[0] || null;
    } catch (error) {
      console.error("❌ Storage: Error adding to wishlist:", error);
      return null;
    }
  }

  async removeFromWishlist(wishlistId: number, productId: number): Promise<boolean> {
    try {
      await db
        .delete(wishlistItems)
        .where(and(
          eq(wishlistItems.wishlistId, wishlistId),
          eq(wishlistItems.productId, productId)
        ));

      console.log('✅ Storage: Removed product', productId, 'from wishlist', wishlistId);
      return true;
    } catch (error) {
      console.error("❌ Storage: Error removing from wishlist:", error);
      return false;
    }
  }

  async getWishlistItems(wishlistId: number): Promise<WishlistItem[]> {
    try {
      const result = await db
        .select({
          item: wishlistItems,
          product: products,
          store: stores,
          category: categories,
        })
        .from(wishlistItems)
        .innerJoin(products, eq(wishlistItems.productId, products.id))
        .leftJoin(stores, eq(products.storeId, stores.id))
        .leftJoin(categories, eq(products.categoryId, categories.id))
        .where(eq(wishlistItems.wishlistId, wishlistId))
        .orderBy(desc(wishlistItems.priority), desc(wishlistItems.addedAt));

      const itemsWithDetails = result.map(row => ({
        ...row.item,
        product: {
          ...row.product,
          store: row.store,
          category: row.category,
        },
      }));

      console.log('✅ Storage: Fetched', result.length, 'items for wishlist', wishlistId);
      return itemsWithDetails;
    } catch (error) {
      console.error("❌ Storage: Error fetching wishlist items:", error);
      return [];
    }
  }

  async updateWishlistItem(itemId: number, updates: Partial<InsertWishlistItem>): Promise<WishlistItem | null> {
    try {
      const result = await db
        .update(wishlistItems)
        .set({ ...updates, updatedAt: new Date() })
        .where(eq(wishlistItems.id, itemId))
        .returning();

      console.log('✅ Storage: Updated wishlist item', itemId);
      return result[0] || null;
    } catch (error) {
      console.error("❌ Storage: Error updating wishlist item:", error);
      return null;
    }
  }

  async isProductInWishlist(userId: string, productId: number): Promise<boolean> {
    try {
      const result = await db
        .select({ id: wishlistItems.id })
        .from(wishlistItems)
        .innerJoin(userWishlists, eq(wishlistItems.wishlistId, userWishlists.id))
        .where(and(
          eq(userWishlists.userId, userId),
          eq(wishlistItems.productId, productId)
        ))
        .limit(1);

      return result.length > 0;
    } catch (error) {
      console.error("❌ Storage: Error checking if product is in wishlist:", error);
      return false;
    }
  }

  // ===== USER BEHAVIOR TRACKING =====

  async trackUserBehavior(behavior: InsertUserBehavior): Promise<UserBehavior | null> {
    try {
      const result = await db.insert(userBehavior).values(behavior).returning();
      return result[0] || null;
    } catch (error) {
      console.error("❌ Storage: Error tracking user behavior:", error);
      return null;
    }
  }

  async getUserBehaviorHistory(userId: string, limit: number = 50): Promise<UserBehavior[]> {
    try {
      const result = await db
        .select()
        .from(userBehavior)
        .where(eq(userBehavior.userId, userId))
        .orderBy(desc(userBehavior.createdAt))
        .limit(limit);

      return result;
    } catch (error) {
      console.error("❌ Storage: Error fetching user behavior history:", error);
      return [];
    }
  }

  // ===== PRODUCT RECOMMENDATIONS =====

  async createProductRecommendation(recommendation: InsertProductRecommendation): Promise<ProductRecommendation | null> {
    try {
      const result = await db.insert(productRecommendations).values(recommendation).returning();
      return result[0] || null;
    } catch (error) {
      console.error("❌ Storage: Error creating product recommendation:", error);
      return null;
    }
  }

  async getUserRecommendations(userId: string, type?: string, limit: number = 10): Promise<ProductRecommendation[]> {
    try {
      let query = db
        .select({
          recommendation: productRecommendations,
          product: products,
          store: stores,
          category: categories,
        })
        .from(productRecommendations)
        .innerJoin(products, eq(productRecommendations.productId, products.id))
        .leftJoin(stores, eq(products.storeId, stores.id))
        .leftJoin(categories, eq(products.categoryId, categories.id))
        .where(eq(productRecommendations.userId, userId));

      if (type) {
        query = query.where(eq(productRecommendations.recommendationType, type));
      }

      const result = await query
        .orderBy(desc(productRecommendations.score), desc(productRecommendations.createdAt))
        .limit(limit);

      const recommendationsWithDetails = result.map(row => ({
        ...row.recommendation,
        product: {
          ...row.product,
          store: row.store,
          category: row.category,
        },
      }));

      return recommendationsWithDetails;
    } catch (error) {
      console.error("❌ Storage: Error fetching user recommendations:", error);
      return [];
    }
  }

  async markRecommendationAsShown(recommendationId: number): Promise<boolean> {
    try {
      await db
        .update(productRecommendations)
        .set({ shown: true })
        .where(eq(productRecommendations.id, recommendationId));

      return true;
    } catch (error) {
      console.error("❌ Storage: Error marking recommendation as shown:", error);
      return false;
    }
  }

  async markRecommendationAsClicked(recommendationId: number): Promise<boolean> {
    try {
      await db
        .update(productRecommendations)
        .set({ clicked: true })
        .where(eq(productRecommendations.id, recommendationId));

      return true;
    } catch (error) {
      console.error("❌ Storage: Error marking recommendation as clicked:", error);
      return false;
    }
  }

  // ===== BUSINESS INTELLIGENCE =====

  async getStorePerformanceMetrics(): Promise<any[]> {
    try {
      const result = await db
        .select({
          id: stores.id,
          name: stores.name,
          totalProducts: sql<number>`count(${products.id})::int`,
          avgPrice: sql<number>`avg(${products.price})::numeric`,
          inStockProducts: sql<number>`count(case when ${products.stock} > 0 then 1 end)::int`,
          stockPercentage: sql<number>`(count(case when ${products.stock} > 0 then 1 end)::float / count(${products.id})::float * 100)::numeric`,
        })
        .from(stores)
        .leftJoin(products, eq(stores.id, products.storeId))
        .groupBy(stores.id, stores.name)
        .orderBy(desc(sql`count(${products.id})`));

      // Calculate additional metrics
      const storePerformance = result.map((store, index) => ({
        ...store,
        avgPrice: parseFloat(store.avgPrice?.toString() || '0'),
        stockPercentage: parseFloat(store.stockPercentage?.toString() || '0'),
        priceRank: index + 1,
        marketShare: 0 // Will be calculated below
      }));

      // Calculate market share
      const totalProducts = storePerformance.reduce((sum, store) => sum + store.totalProducts, 0);
      storePerformance.forEach(store => {
        store.marketShare = totalProducts > 0 ? (store.totalProducts / totalProducts) * 100 : 0;
      });

      console.log('✅ Storage: Fetched store performance metrics for', result.length, 'stores');
      return storePerformance;
    } catch (error) {
      console.error("❌ Storage: Error fetching store performance metrics:", error);
      return [];
    }
  }

  async getCategoryAnalysis(): Promise<any[]> {
    try {
      const result = await db
        .select({
          id: categories.id,
          name: categories.name,
          productCount: sql<number>`count(${products.id})::int`,
          avgPrice: sql<number>`avg(${products.price})::numeric`,
          minPrice: sql<number>`min(${products.price})::numeric`,
          maxPrice: sql<number>`max(${products.price})::numeric`,
          topBrands: sql<string[]>`array_agg(distinct ${products.brand}) filter (where ${products.brand} is not null)`,
        })
        .from(categories)
        .leftJoin(products, eq(categories.id, products.categoryId))
        .groupBy(categories.id, categories.name)
        .orderBy(desc(sql`count(${products.id})`));

      const categoryAnalysis = result.map(category => ({
        ...category,
        avgPrice: parseFloat(category.avgPrice?.toString() || '0'),
        minPrice: parseFloat(category.minPrice?.toString() || '0'),
        maxPrice: parseFloat(category.maxPrice?.toString() || '0'),
        priceRange: parseFloat(category.maxPrice?.toString() || '0') - parseFloat(category.minPrice?.toString() || '0'),
        topBrands: (category.topBrands || []).slice(0, 5)
      }));

      console.log('✅ Storage: Fetched category analysis for', result.length, 'categories');
      return categoryAnalysis;
    } catch (error) {
      console.error("❌ Storage: Error fetching category analysis:", error);
      return [];
    }
  }

  async getBrandComparison(): Promise<any[]> {
    try {
      const result = await db
        .select({
          brand: products.brand,
          productCount: sql<number>`count(${products.id})::int`,
          avgPrice: sql<number>`avg(${products.price})::numeric`,
          storeCount: sql<number>`count(distinct ${products.storeId})::int`,
          availability: sql<number>`(count(case when ${products.stock} > 0 then 1 end)::float / count(${products.id})::float * 100)::numeric`,
        })
        .from(products)
        .where(isNotNull(products.brand))
        .groupBy(products.brand)
        .orderBy(desc(sql`count(${products.id})`));

      // Calculate price positioning
      const avgPrices = result.map(r => parseFloat(r.avgPrice?.toString() || '0')).filter(p => p > 0);
      const priceThresholds = {
        low: avgPrices.length > 0 ? avgPrices.sort((a, b) => a - b)[Math.floor(avgPrices.length * 0.33)] : 0,
        high: avgPrices.length > 0 ? avgPrices.sort((a, b) => a - b)[Math.floor(avgPrices.length * 0.67)] : 0
      };

      const brandComparison = result.map(brand => {
        const avgPrice = parseFloat(brand.avgPrice?.toString() || '0');
        let pricePosition: 'low' | 'medium' | 'high' = 'medium';

        if (avgPrice <= priceThresholds.low) pricePosition = 'low';
        else if (avgPrice >= priceThresholds.high) pricePosition = 'high';

        return {
          ...brand,
          avgPrice,
          availability: parseFloat(brand.availability?.toString() || '0'),
          pricePosition
        };
      });

      console.log('✅ Storage: Fetched brand comparison for', result.length, 'brands');
      return brandComparison;
    } catch (error) {
      console.error("❌ Storage: Error fetching brand comparison:", error);
      return [];
    }
  }

  async getMarketShareAnalysis(): Promise<any[]> {
    try {
      const result = await db
        .select({
          storeId: stores.id,
          storeName: stores.name,
          categoryId: categories.id,
          categoryName: categories.name,
          productCount: sql<number>`count(${products.id})::int`,
        })
        .from(stores)
        .leftJoin(products, eq(stores.id, products.storeId))
        .leftJoin(categories, eq(products.categoryId, categories.id))
        .where(isNotNull(categories.id))
        .groupBy(stores.id, stores.name, categories.id, categories.name)
        .orderBy(desc(sql`count(${products.id})`));

      // Calculate market share per category
      const categoryTotals = new Map<number, number>();
      result.forEach(row => {
        const current = categoryTotals.get(row.categoryId!) || 0;
        categoryTotals.set(row.categoryId!, current + row.productCount);
      });

      const marketShare = result.map(row => {
        const totalInCategory = categoryTotals.get(row.categoryId!) || 1;
        const marketSharePercent = (row.productCount / totalInCategory) * 100;

        let dominanceLevel: 'low' | 'medium' | 'high' = 'low';
        if (marketSharePercent >= 50) dominanceLevel = 'high';
        else if (marketSharePercent >= 25) dominanceLevel = 'medium';

        return {
          ...row,
          marketShare: marketSharePercent,
          dominanceLevel
        };
      });

      console.log('✅ Storage: Fetched market share analysis for', result.length, 'store-category combinations');
      return marketShare;
    } catch (error) {
      console.error("❌ Storage: Error fetching market share analysis:", error);
      return [];
    }
  }

  async getCategoryPriceTrends(categoryId: number, period: string): Promise<any[]> {
    try {
      // This would require a price history table to implement properly
      // For now, return mock data structure
      console.log('✅ Storage: Price trends requested for category', categoryId, 'period', period);
      return [];
    } catch (error) {
      console.error("❌ Storage: Error fetching category price trends:", error);
      return [];
    }
  }

  async getStoreComparisonByCategory(categoryId: number): Promise<any[]> {
    try {
      const result = await db
        .select({
          storeId: stores.id,
          storeName: stores.name,
          productCount: sql<number>`count(${products.id})::int`,
          avgPrice: sql<number>`avg(${products.price})::numeric`,
          minPrice: sql<number>`min(${products.price})::numeric`,
          maxPrice: sql<number>`max(${products.price})::numeric`,
          inStockCount: sql<number>`count(case when ${products.stock} > 0 then 1 end)::int`,
        })
        .from(stores)
        .leftJoin(products, eq(stores.id, products.storeId))
        .where(eq(products.categoryId, categoryId))
        .groupBy(stores.id, stores.name)
        .orderBy(desc(sql`count(${products.id})`));

      console.log('✅ Storage: Fetched store comparison for category', categoryId);
      return result.map(row => ({
        ...row,
        avgPrice: parseFloat(row.avgPrice?.toString() || '0'),
        minPrice: parseFloat(row.minPrice?.toString() || '0'),
        maxPrice: parseFloat(row.maxPrice?.toString() || '0'),
      }));
    } catch (error) {
      console.error("❌ Storage: Error fetching store comparison by category:", error);
      return [];
    }
  }

  async getBrandPerformanceByStore(storeId: number): Promise<any[]> {
    try {
      const result = await db
        .select({
          brand: products.brand,
          productCount: sql<number>`count(${products.id})::int`,
          avgPrice: sql<number>`avg(${products.price})::numeric`,
          inStockCount: sql<number>`count(case when ${products.stock} > 0 then 1 end)::int`,
          availability: sql<number>`(count(case when ${products.stock} > 0 then 1 end)::float / count(${products.id})::float * 100)::numeric`,
        })
        .from(products)
        .where(and(eq(products.storeId, storeId), isNotNull(products.brand)))
        .groupBy(products.brand)
        .orderBy(desc(sql`count(${products.id})`));

      console.log('✅ Storage: Fetched brand performance for store', storeId);
      return result.map(row => ({
        ...row,
        avgPrice: parseFloat(row.avgPrice?.toString() || '0'),
        availability: parseFloat(row.availability?.toString() || '0'),
      }));
    } catch (error) {
      console.error("❌ Storage: Error fetching brand performance by store:", error);
      return [];
    }
  }

  async generateBIReport(reportType: string, filters: any, userId: string): Promise<any> {
    try {
      // This would generate comprehensive BI reports based on type and filters
      console.log('✅ Storage: BI report generation requested', reportType, 'by user', userId);
      return {
        reportType,
        filters,
        generatedAt: new Date().toISOString(),
        data: [] // Would contain actual report data
      };
    } catch (error) {
      console.error("❌ Storage: Error generating BI report:", error);
      return null;
    }
  }

  async getBIDashboardSummary(): Promise<any> {
    try {
      const [storeCount, productCount, categoryCount, brandCount] = await Promise.all([
        db.select({ count: sql<number>`count(*)::int` }).from(stores),
        db.select({ count: sql<number>`count(*)::int` }).from(products),
        db.select({ count: sql<number>`count(*)::int` }).from(categories),
        db.select({ count: sql<number>`count(distinct ${products.brand})::int` }).from(products).where(isNotNull(products.brand)),
      ]);

      console.log('✅ Storage: Fetched BI dashboard summary');
      return {
        totalStores: storeCount[0]?.count || 0,
        totalProducts: productCount[0]?.count || 0,
        totalCategories: categoryCount[0]?.count || 0,
        totalBrands: brandCount[0]?.count || 0,
      };
    } catch (error) {
      console.error("❌ Storage: Error fetching BI dashboard summary:", error);
      return {
        totalStores: 0,
        totalProducts: 0,
        totalCategories: 0,
        totalBrands: 0,
      };
    }
  }

  // ===== SAVED SEARCHES =====

  async getUserSavedSearches(userId: string): Promise<any[]> {
    try {
      const result = await db
        .select()
        .from(savedSearches)
        .where(eq(savedSearches.userId, userId))
        .orderBy(desc(savedSearches.updatedAt));

      console.log('✅ Storage: Fetched', result.length, 'saved searches for user', userId);
      return result;
    } catch (error) {
      console.error("❌ Storage: Error fetching saved searches:", error);
      return [];
    }
  }

  async createSavedSearch(searchData: any): Promise<any> {
    try {
      const [result] = await db
        .insert(savedSearches)
        .values(searchData)
        .returning();

      console.log('✅ Storage: Created saved search', result.id);
      return result;
    } catch (error) {
      console.error("❌ Storage: Error creating saved search:", error);
      throw error;
    }
  }

  async getSavedSearch(searchId: number): Promise<any> {
    try {
      const [result] = await db
        .select()
        .from(savedSearches)
        .where(eq(savedSearches.id, searchId));

      return result || null;
    } catch (error) {
      console.error("❌ Storage: Error fetching saved search:", error);
      return null;
    }
  }

  async updateSavedSearch(searchId: number, searchData: any): Promise<any> {
    try {
      const [result] = await db
        .update(savedSearches)
        .set({ ...searchData, updatedAt: new Date() })
        .where(eq(savedSearches.id, searchId))
        .returning();

      console.log('✅ Storage: Updated saved search', searchId);
      return result;
    } catch (error) {
      console.error("❌ Storage: Error updating saved search:", error);
      throw error;
    }
  }

  async deleteSavedSearch(searchId: number): Promise<boolean> {
    try {
      await db
        .delete(savedSearches)
        .where(eq(savedSearches.id, searchId));

      console.log('✅ Storage: Deleted saved search', searchId);
      return true;
    } catch (error) {
      console.error("❌ Storage: Error deleting saved search:", error);
      return false;
    }
  }

  async executeSearch(searchQuery: string, filters: any): Promise<any[]> {
    try {
      // This would execute the search with the given query and filters
      // For now, return a basic product search
      let query = db.select().from(products);

      if (searchQuery) {
        query = query.where(
          or(
            ilike(products.name, `%${searchQuery}%`),
            ilike(products.brand, `%${searchQuery}%`),
            ilike(products.model, `%${searchQuery}%`)
          )
        ) as any;
      }

      const results = await query.limit(100);
      console.log('✅ Storage: Executed search with', results.length, 'results');
      return results;
    } catch (error) {
      console.error("❌ Storage: Error executing search:", error);
      return [];
    }
  }

  async updateSearchExecution(searchId: number): Promise<void> {
    try {
      await db
        .update(savedSearches)
        .set({
          executionCount: sql`${savedSearches.executionCount} + 1`,
          lastExecuted: new Date()
        })
        .where(eq(savedSearches.id, searchId));

      console.log('✅ Storage: Updated search execution for', searchId);
    } catch (error) {
      console.error("❌ Storage: Error updating search execution:", error);
    }
  }

  async getUserSearchHistory(userId: string, limit: number = 50): Promise<any[]> {
    try {
      const result = await db
        .select()
        .from(searchHistory)
        .where(eq(searchHistory.userId, userId))
        .orderBy(desc(searchHistory.executedAt))
        .limit(limit);

      console.log('✅ Storage: Fetched', result.length, 'search history items for user', userId);
      return result;
    } catch (error) {
      console.error("❌ Storage: Error fetching search history:", error);
      return [];
    }
  }

  async executeAdvancedSearch(query: string, filters: any, pagination: any): Promise<any> {
    try {
      console.log('🔍 Advanced search executed with query:', query, 'filters:', filters);

      // Use the enhanced listProducts method which already has improved search
      const result = await this.listProducts({
        search: query,
        storeIds: filters.storeIds,
        categoryId: filters.categoryId,
        brands: filters.brands,
        minPrice: filters.minPrice,
        maxPrice: filters.maxPrice,
        inStock: filters.inStock,
        sort: filters.sort || 'updatedAt',
        order: filters.order || 'desc',
        page: pagination.page,
        limit: pagination.limit,
      });

      console.log('✅ Storage: Advanced search completed with', result.total, 'total results');
      return {
        results: result.products,
        total: result.total,
        page: pagination.page,
        limit: pagination.limit,
      };
    } catch (error) {
      console.error("❌ Storage: Error executing advanced search:", error);
      return { results: [], total: 0, page: pagination.page || 1, limit: pagination.limit || 20 };
    }
  }

  async logSearchActivity(userId: string, query: string, filters: any, resultCount: number): Promise<void> {
    try {
      await db
        .insert(searchHistory)
        .values({
          userId,
          searchQuery: query,
          filters,
          resultCount,
        });

      console.log('✅ Storage: Logged search activity for user', userId);
    } catch (error) {
      console.error("❌ Storage: Error logging search activity:", error);
    }
  }

  async getSearchSuggestions(query: string, type: string, limit: number): Promise<any[]> {
    try {
      let suggestions: any[] = [];
      const normalizedQuery = query.trim();

      if (type === 'products' || type === 'all') {
        // Product name suggestions
        const productSuggestions = await db
          .select({
            value: products.name,
            type: sql`'product'`,
            relevance: sql`1`
          })
          .from(products)
          .where(ilike(products.name, `%${normalizedQuery}%`))
          .limit(Math.ceil(limit / 4));
        suggestions.push(...productSuggestions);
      }

      if (type === 'models' || type === 'all') {
        // Model number suggestions (high priority for partial matching)
        const modelSuggestions = await db
          .select({
            value: products.model,
            type: sql`'model'`,
            relevance: sql`CASE
              WHEN ${products.model} ILIKE ${normalizedQuery + '%'} THEN 3
              WHEN ${products.model} ILIKE ${normalizedQuery} THEN 4
              ELSE 2
            END`
          })
          .from(products)
          .where(and(
            isNotNull(products.model),
            or(
              ilike(products.model, `${normalizedQuery}%`), // Prefix match
              ilike(products.model, `%${normalizedQuery}%`) // Contains match
            )
          ))
          .groupBy(products.model)
          .orderBy(desc(sql`relevance`))
          .limit(Math.ceil(limit / 4));
        suggestions.push(...modelSuggestions);
      }

      if (type === 'skus' || type === 'all') {
        // SKU suggestions
        const skuSuggestions = await db
          .select({
            value: products.sku,
            type: sql`'sku'`,
            relevance: sql`CASE
              WHEN ${products.sku} ILIKE ${normalizedQuery + '%'} THEN 3
              WHEN ${products.sku} ILIKE ${normalizedQuery} THEN 4
              ELSE 2
            END`
          })
          .from(products)
          .where(and(
            isNotNull(products.sku),
            or(
              ilike(products.sku, `${normalizedQuery}%`), // Prefix match
              ilike(products.sku, `%${normalizedQuery}%`) // Contains match
            )
          ))
          .groupBy(products.sku)
          .orderBy(desc(sql`relevance`))
          .limit(Math.ceil(limit / 4));
        suggestions.push(...skuSuggestions);
      }

      if (type === 'brands' || type === 'all') {
        // Brand suggestions
        const brandSuggestions = await db
          .select({
            value: products.brand,
            type: sql`'brand'`,
            relevance: sql`1`
          })
          .from(products)
          .where(and(isNotNull(products.brand), ilike(products.brand, `%${normalizedQuery}%`)))
          .groupBy(products.brand)
          .limit(Math.ceil(limit / 4));
        suggestions.push(...brandSuggestions);
      }

      // Sort by relevance and limit results
      const sortedSuggestions = suggestions
        .sort((a, b) => (b.relevance || 1) - (a.relevance || 1))
        .slice(0, limit);

      console.log('✅ Storage: Fetched', sortedSuggestions.length, 'search suggestions for query:', normalizedQuery);
      return sortedSuggestions;
    } catch (error) {
      console.error("❌ Storage: Error fetching search suggestions:", error);
      return [];
    }
  }

  async getPopularSearches(limit: number, period: string): Promise<any[]> {
    try {
      // This would analyze search history to find popular searches
      // For now, return empty array
      console.log('✅ Storage: Popular searches requested for period', period);
      return [];
    } catch (error) {
      console.error("❌ Storage: Error fetching popular searches:", error);
      return [];
    }
  }

  // ===== CUSTOM REPORTS =====

  async getUserCustomReports(userId: string, includePublic: boolean = false): Promise<any[]> {
    try {
      let query = db.select().from(customReports);

      if (includePublic) {
        query = query.where(
          or(
            eq(customReports.userId, userId),
            eq(customReports.isPublic, true)
          )
        ) as any;
      } else {
        query = query.where(eq(customReports.userId, userId)) as any;
      }

      const result = await query.orderBy(desc(customReports.updatedAt));

      console.log('✅ Storage: Fetched', result.length, 'custom reports for user', userId);
      return result;
    } catch (error) {
      console.error("❌ Storage: Error fetching custom reports:", error);
      return [];
    }
  }

  async createCustomReport(reportData: any): Promise<any> {
    try {
      const [result] = await db
        .insert(customReports)
        .values(reportData)
        .returning();

      console.log('✅ Storage: Created custom report', result.id);
      return result;
    } catch (error) {
      console.error("❌ Storage: Error creating custom report:", error);
      throw error;
    }
  }

  async getCustomReport(reportId: number): Promise<any> {
    try {
      const [result] = await db
        .select()
        .from(customReports)
        .where(eq(customReports.id, reportId));

      return result || null;
    } catch (error) {
      console.error("❌ Storage: Error fetching custom report:", error);
      return null;
    }
  }

  async updateCustomReport(reportId: number, reportData: any): Promise<any> {
    try {
      const [result] = await db
        .update(customReports)
        .set({ ...reportData, updatedAt: new Date() })
        .where(eq(customReports.id, reportId))
        .returning();

      console.log('✅ Storage: Updated custom report', reportId);
      return result;
    } catch (error) {
      console.error("❌ Storage: Error updating custom report:", error);
      throw error;
    }
  }

  async deleteCustomReport(reportId: number): Promise<boolean> {
    try {
      await db
        .delete(customReports)
        .where(eq(customReports.id, reportId));

      console.log('✅ Storage: Deleted custom report', reportId);
      return true;
    } catch (error) {
      console.error("❌ Storage: Error deleting custom report:", error);
      return false;
    }
  }

  async generateReportData(fields: any[], filters: any[], limit: number): Promise<any[]> {
    try {
      // This would generate report data based on the selected fields and filters
      // For now, return basic product data
      const results = await db
        .select()
        .from(products)
        .limit(Math.min(limit, 1000));

      console.log('✅ Storage: Generated report data with', results.length, 'rows');
      return results;
    } catch (error) {
      console.error("❌ Storage: Error generating report data:", error);
      return [];
    }
  }

  async logReportGeneration(userId: string, fieldsCount: number, filtersCount: number, resultCount: number): Promise<void> {
    try {
      await db
        .insert(reportExecutions)
        .values({
          userId,
          executionType: 'generate',
          fieldsCount,
          filtersCount,
          resultCount,
        });

      console.log('✅ Storage: Logged report generation for user', userId);
    } catch (error) {
      console.error("❌ Storage: Error logging report generation:", error);
    }
  }

  async getReportTemplates(): Promise<any[]> {
    try {
      // This would return predefined report templates
      console.log('✅ Storage: Report templates requested');
      return [
        {
          id: 'store-performance',
          name: 'Store Performance Report',
          description: 'Compare performance metrics across all stores',
          fields: ['store_name', 'product_count', 'avg_price', 'total_stock'],
          visualizations: [{ type: 'bar_chart', title: 'Store Comparison' }]
        },
        {
          id: 'category-analysis',
          name: 'Category Analysis Report',
          description: 'Analyze product distribution and pricing by category',
          fields: ['category_name', 'product_count', 'min_price', 'max_price', 'avg_price'],
          visualizations: [{ type: 'pie_chart', title: 'Category Distribution' }]
        }
      ];
    } catch (error) {
      console.error("❌ Storage: Error fetching report templates:", error);
      return [];
    }
  }

  async getDailySnapshotStats(date?: Date): Promise<{
    date: string;
    totalSnapshots: number;
    storeBreakdown: { storeId: number; storeName: string; count: number }[];
    avgPrice: number;
    minPrice: number;
    maxPrice: number;
  }> {
    try {
      const targetDate = date || new Date();
      const dateStr = targetDate.toISOString().split('T')[0];
      const startOfDay = new Date(targetDate);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(targetDate);
      endOfDay.setHours(23, 59, 59, 999);

      console.log(`💾 Storage: Getting daily snapshot stats for ${dateStr}`);

      // Get total snapshots for the day
      const totalResult = await db
        .select({
          count: sql<number>`count(*)`,
          avgPrice: sql<number>`avg(${priceHistory.price})`,
          minPrice: sql<number>`min(${priceHistory.price})`,
          maxPrice: sql<number>`max(${priceHistory.price})`
        })
        .from(priceHistory)
        .where(
          and(
            eq(priceHistory.changeType, 'daily_snapshot'),
            gte(priceHistory.recordedAt, startOfDay),
            lte(priceHistory.recordedAt, endOfDay)
          )
        );

      // Get store breakdown
      const storeBreakdown = await db
        .select({
          storeId: priceHistory.storeId,
          storeName: stores.name,
          count: sql<number>`count(*)`
        })
        .from(priceHistory)
        .leftJoin(stores, eq(priceHistory.storeId, stores.id))
        .where(
          and(
            eq(priceHistory.changeType, 'daily_snapshot'),
            gte(priceHistory.recordedAt, startOfDay),
            lte(priceHistory.recordedAt, endOfDay)
          )
        )
        .groupBy(priceHistory.storeId, stores.name);

      const stats = totalResult[0] || { count: 0, avgPrice: 0, minPrice: 0, maxPrice: 0 };

      return {
        date: dateStr,
        totalSnapshots: Number(stats.count),
        storeBreakdown: storeBreakdown.map(row => ({
          storeId: row.storeId || 0,
          storeName: row.storeName || 'Unknown',
          count: Number(row.count)
        })),
        avgPrice: Number(stats.avgPrice) || 0,
        minPrice: Number(stats.minPrice) || 0,
        maxPrice: Number(stats.maxPrice) || 0
      };

    } catch (error) {
      console.error("❌ Storage: Error getting daily snapshot stats:", error);
      return {
        date: date?.toISOString().split('T')[0] || new Date().toISOString().split('T')[0],
        totalSnapshots: 0,
        storeBreakdown: [],
        avgPrice: 0,
        minPrice: 0,
        maxPrice: 0
      };
    }
  }

  async cleanupOldPriceHistory(keepDays: number = 365): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - keepDays);

      console.log(`💾 Storage: Cleaning up price history older than ${cutoffDate.toISOString()}`);

      const result = await db
        .delete(priceHistory)
        .where(
          and(
            lt(priceHistory.recordedAt, cutoffDate),
            eq(priceHistory.changeType, 'daily_snapshot') // Only clean up daily snapshots, keep price changes
          )
        );

      console.log(`✅ Storage: Cleaned up old price history records`);
      return result.rowCount || 0;

    } catch (error) {
      console.error("❌ Storage: Error cleaning up old price history:", error);
      throw error;
    }
  }

  async getPriceHistoryAnalytics(productId: number, days: number = 30): Promise<{
    productId: number;
    totalEntries: number;
    priceChanges: number;
    avgPrice: number;
    minPrice: number;
    maxPrice: number;
    volatility: number;
    trend: 'up' | 'down' | 'stable';
    lastUpdated: Date | null;
  }> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      console.log(`💾 Storage: Getting price analytics for product ${productId} over ${days} days from product_prices table`);

      // Simplified approach: Get all price data for the product in the time range
      const priceData = await db
        .select({
          price: productPrices.price,
          timestamp: productPrices.timestamp
        })
        .from(productPrices)
        .where(
          and(
            eq(productPrices.productId, productId),
            gte(productPrices.timestamp, startDate)
          )
        )
        .orderBy(productPrices.timestamp);

      console.log(`💾 Storage: Found ${priceData.length} price entries for product ${productId}`);

      if (priceData.length === 0) {
        console.log(`💾 Storage: No price data found for product ${productId} in the last ${days} days`);
        return {
          productId,
          totalEntries: 0,
          priceChanges: 0,
          avgPrice: 0,
          minPrice: 0,
          maxPrice: 0,
          volatility: 0,
          trend: 'stable',
          lastUpdated: null
        };
      }

      // Filter out invalid prices and convert to numbers
      const validPrices = priceData
        .map(entry => ({
          price: parseFloat(entry.price) || 0,
          timestamp: entry.timestamp
        }))
        .filter(entry => entry.price > 0);

      if (validPrices.length === 0) {
        console.log(`💾 Storage: No valid price data found for product ${productId}`);
        return {
          productId,
          totalEntries: priceData.length,
          priceChanges: 0,
          avgPrice: 0,
          minPrice: 0,
          maxPrice: 0,
          volatility: 0,
          trend: 'stable',
          lastUpdated: priceData[priceData.length - 1]?.timestamp || null
        };
      }

      // Calculate analytics
      const prices = validPrices.map(entry => entry.price);
      const totalEntries = priceData.length;
      const avgPrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;
      const minPrice = Math.min(...prices);
      const maxPrice = Math.max(...prices);
      const volatility = avgPrice > 0 ? ((maxPrice - minPrice) / avgPrice) * 100 : 0;

      // Calculate price changes
      let priceChanges = 0;
      for (let i = 1; i < validPrices.length; i++) {
        if (validPrices[i].price !== validPrices[i - 1].price) {
          priceChanges++;
        }
      }

      // Determine trend
      const firstPrice = validPrices[0].price;
      const lastPrice = validPrices[validPrices.length - 1].price;
      let trend: 'up' | 'down' | 'stable' = 'stable';

      if (firstPrice > 0) {
        const changePercent = ((lastPrice - firstPrice) / firstPrice) * 100;
        if (changePercent > 5) trend = 'up';
        else if (changePercent < -5) trend = 'down';
      }

      const lastUpdated = priceData[priceData.length - 1]?.timestamp || null;

      const analytics_result = {
        productId,
        totalEntries,
        priceChanges,
        avgPrice: Math.round(avgPrice * 100) / 100, // Round to 2 decimal places
        minPrice: Math.round(minPrice * 100) / 100,
        maxPrice: Math.round(maxPrice * 100) / 100,
        volatility: Math.round(volatility * 100) / 100,
        trend,
        lastUpdated
      };

      console.log(`💾 Storage: Price analytics for product ${productId}:`, analytics_result);
      return analytics_result;

    } catch (error) {
      console.error(`❌ Storage: Error getting price analytics for product ${productId}:`, error);
      console.error(`❌ Storage: Error details:`, error.message);

      // Return default values instead of throwing to prevent API failures
      return {
        productId,
        totalEntries: 0,
        priceChanges: 0,
        avgPrice: 0,
        minPrice: 0,
        maxPrice: 0,
        volatility: 0,
        trend: 'stable',
        lastUpdated: null
      };
    }
  }

  async getDailyPriceSnapshotStats(date?: Date): Promise<{
    date: string;
    totalSnapshots: number;
    storeBreakdown: { storeId: number; storeName: string; count: number }[];
    avgPrice: number;
    minPrice: number;
    maxPrice: number;
  }> {
    try {
      const targetDate = date || new Date();
      const dateStr = targetDate.toISOString().split('T')[0];
      const startOfDay = new Date(targetDate);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(targetDate);
      endOfDay.setHours(23, 59, 59, 999);

      console.log(`💾 Storage: Getting daily price snapshot stats for ${dateStr} from product_prices table`);

      // Get total snapshots for the day from product_prices
      const totalResult = await db
        .select({
          count: sql<number>`count(*)`,
          avgPrice: sql<number>`avg(${productPrices.price})`,
          minPrice: sql<number>`min(${productPrices.price})`,
          maxPrice: sql<number>`max(${productPrices.price})`
        })
        .from(productPrices)
        .where(
          and(
            gte(productPrices.timestamp, startOfDay),
            lte(productPrices.timestamp, endOfDay)
          )
        );

      // Get store breakdown by joining with products table
      const storeBreakdown = await db
        .select({
          storeId: products.storeId,
          storeName: stores.name,
          count: sql<number>`count(*)`
        })
        .from(productPrices)
        .leftJoin(products, eq(productPrices.productId, products.id))
        .leftJoin(stores, eq(products.storeId, stores.id))
        .where(
          and(
            gte(productPrices.timestamp, startOfDay),
            lte(productPrices.timestamp, endOfDay)
          )
        )
        .groupBy(products.storeId, stores.name);

      const stats = totalResult[0] || { count: 0, avgPrice: 0, minPrice: 0, maxPrice: 0 };

      return {
        date: dateStr,
        totalSnapshots: Number(stats.count),
        storeBreakdown: storeBreakdown.map(row => ({
          storeId: row.storeId || 0,
          storeName: row.storeName || 'Unknown',
          count: Number(row.count)
        })),
        avgPrice: Number(stats.avgPrice) || 0,
        minPrice: Number(stats.minPrice) || 0,
        maxPrice: Number(stats.maxPrice) || 0
      };

    } catch (error) {
      console.error("❌ Storage: Error getting daily price snapshot stats:", error);
      return {
        date: date?.toISOString().split('T')[0] || new Date().toISOString().split('T')[0],
        totalSnapshots: 0,
        storeBreakdown: [],
        avgPrice: 0,
        minPrice: 0,
        maxPrice: 0
      };
    }
  }
}

export const storage = new DatabaseStorage();
