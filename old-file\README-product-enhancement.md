# Product Enhancement System

This system ensures consistent product data across all stores, particularly focusing on:
1. Standardized model numbers
2. Size extraction
3. Unit of Measurement (UOM) standardization
4. Duplicate prevention

## Overview

The product enhancement system consists of:

1. **Shared Library (`shared/product-enhancer.js`)**: Contains functions for extracting size and UOM, generating standardized models, and enhancing products.

2. **Fetcher Updates (`update-fetchers.js`)**: <PERSON><PERSON><PERSON> to update all fetchers to use the shared library.

3. **UOM Standardization (`standardize-uom-values.js`)**: Script to standardize UOM values across all products.

4. **Various Enhancement Scripts**: Scripts for specific stores to fix models, size, and UOM values.

## How It Works

### 1. Shared Library

The shared library (`shared/product-enhancer.js`) provides:

- **Size and UOM Extraction**: Extracts size and UOM from product name, model, and category.
- **Model Generation**: Generates standardized models for each store (Zagzoog, Tamkeen, Bin Momen).
- **UOM Standardization**: Standardizes UOM values to ensure consistency.
- **Product Enhancement**: Combines all enhancements into a single function.

### 2. Fetcher Integration

When fetchers are updated to use the shared library:

1. They import the `product-enhancer` module.
2. They replace their own size and UOM extraction with the shared function.
3. They use the appropriate model generation function for their store.
4. They enhance products with consistent size, UOM, and model values.

### 3. UOM Standardization

The UOM standardization script:

1. Gets all distinct UOM values from the database.
2. Standardizes each UOM value using the shared function.
3. Updates all products with the standardized UOM values.

## Store-Specific Model Formats

### Zagzoog Models

Format: `ZAG-[BRAND]-[CATEGORY][SIZE]-[UNIQUE_ID]`

Examples:
- `ZAG-SAM-TV55-123456` (Samsung TV, 55 inch)
- `ZAG-LG-AC180-789012` (LG Air Conditioner, 18000 BTU)
- `ZAG-WHI-RF400-456789` (Whirlpool Refrigerator, 400 Liter)

### Tamkeen Models

Format: `TAM-[BRAND]-[CATEGORY][SIZE]-[UNIQUE_ID]`

Examples:
- `TAM-SON-TV65-123456` (Sony TV, 65 inch)
- `TAM-PAN-AC240-789012` (Panasonic Air Conditioner, 24000 BTU)
- `TAM-BOC-WM8-456789` (Bosch Washing Machine, 8 KG)

### Bin Momen Models

Format: `BIN-[BRAND]-[CATEGORY][SIZE]-[UNIQUE_ID]`

Examples:
- `BIN-HIS-TV50-123456` (Hisense TV, 50 inch)
- `BIN-MID-RF350-789012` (Midea Refrigerator, 350 Liter)
- `BIN-ELE-DW14-456789` (Electrolux Dishwasher, 14 Place Setting)

## Category Codes

- `TV`: TVs & Entertainment
- `AC`: Air Conditioners
- `RF`: Refrigerators & Freezers
- `WM`: Washing Machines & Dryers
- `DW`: Dishwashers
- `KA`: Kitchen Appliances
- `SA`: Small Appliances
- `HA`: Home Appliances (default)

## Size and UOM Extraction

The system extracts size and UOM from:

1. **Product Name**: Looks for patterns like "55 inch TV" or "8 KG washing machine".
2. **Model Number**: Extracts size from model numbers like "55Q60B" (55 inch TV) or "WF80F5E" (8 KG washing machine).
3. **Category**: Uses default values based on category if no size or UOM is found.

## UOM Standardization

The system standardizes UOM values to ensure consistency:

- `Liter` (not "l", "lt", "ltr", etc.)
- `KG` (not "kg", "kgs", etc.)
- `Inch` (not "inch", "inches", etc.)
- `BTU` (not "btu", "btus", etc.)
- `Ft` (not "ft", "feet", etc.)
- `Place Setting` (not "place setting", "place settings", etc.)

## Usage

### 1. Update Fetchers

Run the `update-fetchers.js` script to update all fetchers to use the shared library:

```bash
node update-fetchers.js
```

### 2. Standardize UOM Values

Run the `standardize-uom-values.js` script to standardize UOM values across all products:

```bash
node standardize-uom-values.js
```

### 3. Fix Specific Stores

Run the store-specific scripts to fix models, size, and UOM values:

```bash
node fix-zagzoog-models-only.js
node fix-tamkeen-size-uom.js
node fix-zagzoog-size-uom.js
```

## Benefits

1. **Consistency**: All products have consistent model, size, and UOM values.
2. **Improved Data Quality**: Size and UOM are extracted more accurately.
3. **Duplicate Prevention**: Standardized models help prevent duplicates.
4. **Easier Maintenance**: Shared library makes it easier to maintain and improve the system.

## Future Improvements

1. **More Store Support**: Add support for more stores.
2. **Better Category Detection**: Improve category detection for better model generation.
3. **More UOM Standardization**: Add more UOM standardization rules.
4. **Duplicate Removal**: Add a script to safely remove duplicate products.
