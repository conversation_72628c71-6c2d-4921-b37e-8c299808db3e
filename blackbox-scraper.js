// BlackBox store scraper with category-based faceted search API
import axios from 'axios';
import https from 'https';
import http from 'http';
import fs from 'fs';

// Import utilities from storeScraper.js
import { extractSizeAndUOM, createCategorySlug, normalizeCategoryName } from './storeScraper.js';

// Import storage for database operations
import { storage } from './storage.js';

/**
 * Get bearer token from database for manual token management
 */
async function getBearerTokenFromDatabase() {
  try {
    if (storage && typeof storage.getBearerToken === 'function') {
      const tokenData = await storage.getBearerToken('blackbox');
      if (tokenData && tokenData.token) {
        console.log('🔑 Using bearer token from database (manual configuration)');
        return tokenData.token;
      }
    }
    return null;
  } catch (error) {
    console.warn('⚠️ Could not retrieve bearer token from database:', error?.message || error);
    return null;
  }
}

// BlackBox store configuration with category-based API endpoints
const BLACKBOX_CONFIG = {
  name: '<PERSON><PERSON><PERSON>',
  slug: 'blackbox',
  url: 'https://www.blackbox.com.sa/en/',
  baseUrl: 'https://www.blackbox.com.sa',
  apiBaseUrl: 'https://api.ops.blackbox.com.sa/api/v1',
  searchEndpoint: '/search/products/searchQuery', // Legacy endpoint
  categoryEndpoint: '/search/products/facets/category', // New category-based endpoint
  
  // API configuration (updated to match working request)
  api: {
    pageSize: 30, // Match working request
    sortBy: 'position',
    sortDir: 'ASC',
    selectFields: [
      'sku',
      'name',
      'option_text_brand',
      'option_text_a_brand',
      'prices_with_tax',
      'image',
      '_media_',
      'category',
      'suggest',
      'stock',
      'label',
      'keywords',
      'rewrite_url',
      'attributes',
      'eligible_for_tabby',
      'eligible_for_tamara',
      'alKhobarRegion',
      'allRegions',
      'eligible_for_mispay',
      'product_fees',
      'option_text_eligible_for_tabby',
      'option_text_eligible_for_tamara',
      'back_orders',
      'show_outofstock',
      'reviews_count',
      'up_sell_products',
      'custom_related_products',
      'cart_rule',
      'free_gifts',
      'qty',
      'brand_logo',
      'highlight',
      'entity_id',
      'after_cashback_price',
      'eligible_for_madfu',
      'after_cashback_to_date',
      'after_cashback_from_date'
    ]
  },
  
  // Verified working category-based configuration with confirmed API endpoints
  categories: [
    {
      name: 'Air Conditioners',
      slug: 'air-conditioners',
      categoryId: 175,
      apiUrl: 'https://api.ops.blackbox.com.sa/api/v1/search/products/facets/category/175',
      description: 'Air conditioning units, cooling systems, and HVAC equipment',
      active: true,
      lastUpdated: 'Jun 15, 2025, 1:04 PM'
    },
    {
      name: 'TVs & Audio',
      slug: 'tvs-audio',
      categoryId: 106,
      apiUrl: 'https://api.ops.blackbox.com.sa/api/v1/search/products/facets/category/106',
      description: 'TVs, soundbars, audio systems, and entertainment devices',
      active: true,
      lastUpdated: 'Jun 15, 2025, 1:05 PM'
    },
    {
      name: 'Home Appliances',
      slug: 'home-appliances',
      categoryId: 218,
      apiUrl: 'https://api.ops.blackbox.com.sa/api/v1/search/products/facets/category/218',
      description: 'Large home appliances, refrigerators, washing machines, and kitchen equipment',
      active: true,
      lastUpdated: 'Jun 15, 2025, 1:05 PM'
    }
  ],

  // Additional category IDs to investigate (common e-commerce category ranges)
  categoryIdsToInvestigate: [
    // Electronics categories (common ranges)
    100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110,
    // Home & Garden categories
    150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160,
    // Technology categories
    175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185,
    // Appliances categories
    200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210,
  ],

  // Request configuration
  request: {
    retryCount: 3,
    retryDelay: 2000,
    timeout: 30000,
    concurrentRequests: 2,
    delayBetweenRequests: 1000, // Reduced for API calls
    maxProducts: 0, // 0 = no limit for API
    useWorkerThreads: false,
    verbose: true,
    useMockData: process.env.BLACKBOX_USE_MOCK_DATA === 'true' || false // Enable mock data for testing
  },

  // Authentication configuration (will be updated dynamically)
  auth: {
    bearerToken: process.env.BLACKBOX_BEARER_TOKEN || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTYwMjU1OTQsInN0b3JlTmFtZSI6ImVuIn0.POQ0-0E9J62ZFNpoWQf5CkXjDLDGxVPc4nEbYR9WLU4', // COMPLETE WORKING TOKEN FROM POSTMAN - Updated 2025-01-10
    tokenExpiry: 1754418587000, // Token expiry from JWT payload (exp: 1754418587) - Valid until 2025-06-05
    refreshThreshold: 300000, // Refresh token 5 minutes before expiry

    // Backup tokens to try if main token fails (add fresh tokens here)
    backupTokens: [
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTYwMjU1OTQsInN0b3JlTmFtZSI6ImVuIn0.POQ0-0E9J62ZFNpoWQf5CkXjDLDGxVPc4nEbYR9WLU4', // Complete working Postman token as backup
    ],

    // Alternative authentication methods
    apiKey: process.env.BLACKBOX_API_KEY || null,
    clientId: process.env.BLACKBOX_CLIENT_ID || null,
    clientSecret: process.env.BLACKBOX_CLIENT_SECRET || null,

    // Manual token configuration for production
    manualToken: null // Can be set manually when credentials are available
  }
};

// HTTP agents for connection pooling with working TLS configuration
const httpsAgent = new https.Agent({
  keepAlive: true,
  keepAliveMsecs: 1000,
  maxSockets: 50,
  maxFreeSockets: 10,
  timeout: 60000,
  freeSocketTimeout: 30000,
  rejectUnauthorized: false,
  // Working TLS settings that match the successful configuration
  minVersion: 'TLSv1.2',
  maxVersion: 'TLSv1.3',
  ciphers: [
    'TLS_AES_256_GCM_SHA384',
    'TLS_CHACHA20_POLY1305_SHA256',
    'TLS_AES_128_GCM_SHA256'
  ].join(':')
});

const httpAgent = new http.Agent({
  keepAlive: true,
  keepAliveMsecs: 1000,
  maxSockets: 50,
  maxFreeSockets: 10,
  timeout: 60000,
  freeSocketTimeout: 30000
});

// Create axios instance with session management
const axiosInstance = axios.create({
  timeout: BLACKBOX_CONFIG.request.timeout,
  httpsAgent,
  httpAgent,
  withCredentials: true,
  maxRedirects: 5,
  headers: {
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Origin': 'https://www.blackbox.com.sa',
    'Referer': 'https://www.blackbox.com.sa/',
    'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
    'Sec-Ch-Ua-Mobile': '?0',
    'Sec-Ch-Ua-Platform': '"Windows"',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-site',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache'
  }
});

// Global session state
let sessionEstablished = false;
let sessionCookies = '';
let sessionHeaders = {};
let browserCookies = {};
let cookiesLoaded = false;

/**
 * Parse Netscape cookie format
 */
function parseNetscapeCookies(cookieString) {
  const cookies = {};
  const lines = cookieString.split('\n');

  for (const line of lines) {
    if (line.startsWith('#') || !line.trim()) continue;

    const parts = line.split('\t');
    if (parts.length >= 7) {
      const [domain, flag, path, secure, expiration, name, value] = parts;
      if (domain.includes('blackbox.com.sa')) {
        cookies[name] = value;
      }
    }
  }

  return cookies;
}

/**
 * Parse Chrome/Firefox JSON cookies
 */
function parseJSONCookies(cookieArray) {
  const cookies = {};

  for (const cookie of cookieArray) {
    if (cookie.domain && cookie.domain.includes('blackbox.com.sa')) {
      cookies[cookie.name] = cookie.value;
    }
  }

  return cookies;
}

/**
 * Load cookies from cookies.json file
 */
async function loadBrowserCookies() {
  if (cookiesLoaded) {
    console.log('🍪 Cookies already loaded');
    return browserCookies;
  }

  console.log('🍪 Loading browser cookies from cookies.json...');

  try {
    if (!fs.existsSync('cookies.json')) {
      console.log('❌ cookies.json file not found');
      console.log('📝 Please export cookies from your browser and save as cookies.json');
      return {};
    }

    const cookieData = fs.readFileSync('cookies.json', 'utf8');

    try {
      // Try JSON format first (Chrome/Firefox export)
      const parsed = JSON.parse(cookieData);
      if (Array.isArray(parsed)) {
        browserCookies = parseJSONCookies(parsed);
      } else {
        browserCookies = parsed;
      }
    } catch {
      // Try Netscape format
      browserCookies = parseNetscapeCookies(cookieData);
    }

    cookiesLoaded = true;
    console.log(`✅ Loaded ${Object.keys(browserCookies).length} cookies from browser session`);

    // Log cookie names for debugging
    if (Object.keys(browserCookies).length > 0) {
      console.log('🔍 Cookie names:', Object.keys(browserCookies).join(', '));
    }

    return browserCookies;

  } catch (error) {
    console.error('❌ Error loading cookies:', error.message);
    return {};
  }
}

/**
 * Convert cookies object to cookie string
 */
function cookiesToString(cookies) {
  return Object.entries(cookies)
    .map(([name, value]) => `${name}=${value}`)
    .join('; ');
}

/**
 * Validate cookie session by making a test request
 */
async function validateCookieSession() {
  console.log('🔍 Validating cookie session...');

  const cookies = await loadBrowserCookies();

  if (Object.keys(cookies).length === 0) {
    console.log('❌ No cookies available for validation');
    return false;
  }

  const cookieString = cookiesToString(cookies);

  try {
    // Test with a simple API endpoint
    const response = await axiosInstance.get(
      'https://api.ops.blackbox.com.sa/api/v1/search/products/facets/category/218?pageNo=0&pageSize=1',
      {
        headers: {
          'Cookie': cookieString,
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
          'Accept-Encoding': 'gzip, deflate, br',
          'Origin': 'https://www.blackbox.com.sa',
          'Referer': 'https://www.blackbox.com.sa/',
          'Authorization': `Bearer ${BLACKBOX_CONFIG.auth.bearerToken}`,
          'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
          'Sec-Ch-Ua-Mobile': '?0',
          'Sec-Ch-Ua-Platform': '"Windows"',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'same-site',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        },
        timeout: 15000
      }
    );

    if (response.status === 200 && response.data) {
      console.log('✅ Cookie session is valid and working!');
      return true;
    } else {
      console.log(`❌ Cookie session validation failed: ${response.status}`);
      return false;
    }

  } catch (error) {
    console.log(`❌ Cookie session validation error: ${error.response?.status || 'Network error'} - ${error.message}`);

    if (error.response?.status === 403) {
      console.log('🔄 Cookies may be expired or invalid');
    }

    return false;
  }
}

/**
 * Alternative approach: Use direct API call with enhanced headers
 */
async function makeDirectApiCall(url, attempt = 1) {
  console.log(`🔍 DEBUG: Making direct API call with browser cookies (attempt ${attempt}): ${url}`);

  try {
    // Load browser cookies for authentication
    const cookies = await loadBrowserCookies();

    // Headers that match the exact working configuration (lowercase)
    const headers = {
      'accept': 'application/json, text/plain, */*',
      'accept-language': 'en-US,en;q=0.9,ar;q=0.8',
      'authorization': `Bearer ${BLACKBOX_CONFIG.auth.bearerToken}`,
      'origin': 'https://www.blackbox.com.sa',
      'priority': 'u=1, i',
      'referer': 'https://www.blackbox.com.sa/',
      'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-site',
      'user-agent': 'KWS',
      'cache-control': 'no-cache',
      'host': 'api.ops.blackbox.com.sa'
    };

    // Add browser cookies for authentication
    if (Object.keys(cookies).length > 0) {
      headers['Cookie'] = cookiesToString(cookies);
      console.log(`🍪 Using ${Object.keys(cookies).length} browser cookies for direct API call`);
    } else {
      console.log('⚠️ No browser cookies available for direct API call');
    }

    // Use a completely different approach - mimic the exact browser request
    const response = await axiosInstance.get(url, {
      headers,
      timeout: 15000,
      maxRedirects: 0, // Don't follow redirects
      httpsAgent: httpsAgent,  // Use the working HTTPS agent configuration
      validateStatus: function (status) {
        return status >= 200 && status < 300; // Only accept 2xx responses
      }
    });

    console.log(`✅ Direct API call successful: ${response.status}`);
    return response.data;

  } catch (error) {
    console.log(`❌ Direct API call failed (attempt ${attempt}): ${error.response?.status} ${error.message}`);

    if (error.response?.status === 403 && attempt < 3) {
      // Try with different approach
      console.log(`🔄 Retrying with alternative headers...`);
      await delay(2000);
      return makeDirectApiCall(url, attempt + 1);
    }

    throw error;
  }
}

/**
 * Establish session by visiting the main website first
 */
async function establishSession() {
  if (sessionEstablished) {
    console.log('🔍 DEBUG: Session already established');
    return true;
  }

  console.log('🔧 Establishing session with BlackBox website...');

  try {
    // Step 1: Visit main website to get initial cookies
    const mainPageResponse = await axiosInstance.get(BLACKBOX_CONFIG.url, {
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      }
    });

    // Extract cookies from response
    const cookies = mainPageResponse.headers['set-cookie'];
    if (cookies) {
      sessionCookies = cookies.map(cookie => cookie.split(';')[0]).join('; ');
      console.log('🍪 Session cookies extracted:', sessionCookies.substring(0, 100) + '...');
    }

    // Step 2: Visit a category page to establish API session
    await delay(1000);

    const categoryPageResponse = await axiosInstance.get(`${BLACKBOX_CONFIG.url}/en/category/tvs-audio`, {
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Cookie': sessionCookies,
        'Referer': BLACKBOX_CONFIG.url + '/',
        'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      }
    });

    // Update session cookies if new ones are provided
    const newCookies = categoryPageResponse.headers['set-cookie'];
    if (newCookies) {
      const additionalCookies = newCookies.map(cookie => cookie.split(';')[0]).join('; ');
      sessionCookies = sessionCookies ? sessionCookies + '; ' + additionalCookies : additionalCookies;
    }

    sessionEstablished = true;
    console.log('✅ Session established successfully');
    return true;

  } catch (error) {
    console.error('❌ Failed to establish session:', error.message);
    return false;
  }
}

/**
 * Extract brand from product name
 */
function extractBrand(productName) {
  if (!productName) return 'BlackBox';
  
  const commonBrands = [
    'Samsung', 'LG', 'Apple', 'Sony', 'Panasonic', 'Philips', 'Hitachi', 'Haier',
    'Midea', 'TCL', 'Hisense', 'Gree', 'Mando', 'Platinum', 'Nikai', 'Basic',
    'Kenwood', 'Delonghi', 'Moulinex', 'Lenovo', 'HP', 'Asus', 'Huawei', 'Xiaomi',
    'Dansat', 'Eufy', 'Beurer', 'Witforms', 'Tineco', 'Power N'
  ];
  
  const upperName = productName.toUpperCase();
  for (const brand of commonBrands) {
    if (upperName.includes(brand.toUpperCase())) {
      return brand;
    }
  }
  
  // Try to extract brand from the beginning of the name
  const words = productName.split(' ');
  if (words.length > 0) {
    const firstWord = words[0].trim();
    if (firstWord.length > 2 && /^[A-Za-z]+$/.test(firstWord)) {
      return firstWord;
    }
  }
  
  return 'BlackBox';
}

/**
 * Enhanced model extraction for BlackBox products with brand-specific patterns
 */
function extractModel(productName, brand, sku = '') {
  if (!productName) return '';

  console.log(`🔍 DEBUG: Model extraction - Name: "${productName}", Brand: "${brand}", SKU: "${sku}"`);

  // Remove brand from name to find model
  let cleanName = productName.replace(new RegExp(brand, 'gi'), '').trim();
  console.log(`🔍 DEBUG: Clean name after brand removal: "${cleanName}"`);

  // BlackBox-specific brand patterns
  const brandSpecificPatterns = {
    samsung: [
      /\b(QA\d{2}[A-Z0-9]+)\b/gi,           // Samsung TV models: QA75QN90DAUXSA
      /\b([A-Z]{2}\d{2}[A-Z0-9]+)\b/gi,     // General Samsung pattern
      /\b(UN\d{2}[A-Z0-9]+)\b/gi,           // Samsung UN series
      /\b(UE\d{2}[A-Z0-9]+)\b/gi,           // Samsung UE series
    ],
    lg: [
      /\b(OLED\d{2}[A-Z0-9]+)\b/gi,         // LG OLED: OLED65C3PSA
      /\b(\d{2}[A-Z]{2,4}\d+[A-Z]*)\b/gi,   // LG pattern: 65UP7750PVB
      /\b([A-Z]{2,4}\d{2}[A-Z0-9]+)\b/gi,   // LG general pattern
      /\b(LREL\d+[A-Z]*)\b/gi,              // LG appliances: LREL6323D
    ],
    sony: [
      /\b(KD-\d{2}[A-Z0-9]+)\b/gi,          // Sony TV: KD-55X80J
      /\b(XBR-\d{2}[A-Z0-9]+)\b/gi,         // Sony XBR series
      /\b([A-Z]{2,3}\d{2}[A-Z0-9]+)\b/gi,   // Sony general pattern
    ],
    tcl: [
      /\b(\d{2}[A-Z]\d{3}[A-Z]*)\b/gi,      // TCL pattern: 55C735
      /\b(TCL\d{2}[A-Z0-9]+)\b/gi,          // TCL with prefix
    ],
    midea: [
      /\b(MST\d+[A-Z0-9\-]*)\b/gi,          // Midea AC: MST1AB-18CR
      /\b(\d{2}LMG\d+[A-Z]*)\b/gi,          // Midea gas oven: 36LMG5G022
      /\b([A-Z]{2,4}\d+[A-Z0-9\-]*)\b/gi,   // Midea general
    ],
    haier: [
      /\b(HW\d{2}-[A-Z0-9]+)\b/gi,          // Haier washing: HW80-B14876
      /\b(HR\d{2}[A-Z0-9]+)\b/gi,           // Haier refrigerator
      /\b([A-Z]{2}\d{2}[A-Z0-9\-]+)\b/gi,   // Haier general
    ],
    beko: [
      /\b(BEKO[A-Z0-9]+)\b/gi,              // Beko with prefix
      /\b([A-Z]{3,4}\d{3,4}[A-Z]*)\b/gi,    // Beko general pattern
    ]
  };

  // Try brand-specific patterns first
  const brandLower = brand.toLowerCase();
  if (brandSpecificPatterns[brandLower]) {
    console.log(`🔍 DEBUG: Trying brand-specific patterns for ${brandLower}`);
    for (const pattern of brandSpecificPatterns[brandLower]) {
      const matches = cleanName.match(pattern);
      if (matches && matches[0]) {
        const model = matches[0].trim();
        console.log(`✅ Found brand-specific model: "${model}"`);
        return model;
      }
    }
  }

  // General BlackBox model patterns (ordered by specificity)
  const generalPatterns = [
    /\b([A-Z]{2,4}\d{2,4}[A-Z0-9]{2,8})\b/gi,  // Complex alphanumeric: QA75QN90DAUXSA
    /\b([A-Z]{2,3}\d{2,3}[A-Z]{1,4}\d*)\b/gi,  // Medium complexity: OLED65C3PSA
    /\b(\d{2,3}[A-Z]{1,4}\d{2,4}[A-Z]*)\b/gi,  // Number-letter-number: 55Q60B
    /\b([A-Z]{2,}\d{2,}[A-Z]*\d*)\b/gi,        // Basic alphanumeric: WTV17HHD
    /\b([A-Z]+\d+[A-Z]*)\b/gi,                 // Letter-number: A556ELBVMEA
    /\b(\d+[A-Z]+\d*)\b/gi,                    // Number-letter: 27000BTU
  ];

  console.log(`🔍 DEBUG: Trying general model patterns`);
  for (const pattern of generalPatterns) {
    const matches = cleanName.match(pattern);
    if (matches && matches[0]) {
      const model = matches[0].trim();
      // Validate model length and content
      if (model.length >= 4 && model.length <= 20 && /[A-Za-z]/.test(model) && /\d/.test(model)) {
        console.log(`✅ Found general pattern model: "${model}"`);
        return model;
      }
    }
  }

  // Fallback: Extract from SKU if available
  if (sku && sku.length > 5) {
    console.log(`🔍 DEBUG: Trying SKU-based model extraction from: "${sku}"`);
    // Remove common prefixes and extract meaningful part
    const skuClean = sku.replace(/^(BLACKBOX-|BB-|BLK-)/i, '');
    if (skuClean.length >= 4 && skuClean.length <= 20) {
      console.log(`✅ Using SKU-based model: "${skuClean}"`);
      return skuClean;
    }
  }

  // Final fallback: use first meaningful alphanumeric word
  const words = cleanName.split(/[\s\-,\.]+/);
  for (const word of words) {
    if (word.length >= 4 && word.length <= 15 && /[A-Za-z]/.test(word) && /\d/.test(word)) {
      console.log(`✅ Using fallback word model: "${word}"`);
      return word.trim();
    }
  }

  console.log(`⚠️ No model found for product: "${productName}"`);
  return '';
}

/**
 * Delay function for rate limiting
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Manually configure authentication credentials
 */
function configureAuthentication(options = {}) {
  console.log('🔧 Configuring BlackBox authentication...');

  if (options.bearerToken) {
    BLACKBOX_CONFIG.auth.bearerToken = options.bearerToken;
    BLACKBOX_CONFIG.auth.tokenExpiry = Date.now() + (24 * 60 * 60 * 1000); // 24 hours
    console.log('✅ Bearer token configured manually');
  }

  if (options.apiKey) {
    BLACKBOX_CONFIG.auth.apiKey = options.apiKey;
    console.log('✅ API key configured');
  }

  if (options.clientId && options.clientSecret) {
    BLACKBOX_CONFIG.auth.clientId = options.clientId;
    BLACKBOX_CONFIG.auth.clientSecret = options.clientSecret;
    console.log('✅ Client credentials configured');
  }

  if (options.manualToken) {
    BLACKBOX_CONFIG.auth.manualToken = options.manualToken;
    console.log('✅ Manual token configured');
  }

  console.log('🔧 Authentication configuration updated');
  return BLACKBOX_CONFIG.auth;
}

/**
 * Test Blackbox authentication with current token
 */
async function testBlackboxAuthentication() {
  console.log('🧪 Testing Blackbox authentication...');

  try {
    const token = await getBearerToken();

    if (!token) {
      return { success: false, error: 'No bearer token available' };
    }

    console.log(`🔑 Testing token: ${token.substring(0, 30)}...`);

    const response = await axios.get(`${BLACKBOX_CONFIG.api.baseUrl}/search/products/facets/category/175`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Origin': BLACKBOX_CONFIG.api.origin,
        'Referer': BLACKBOX_CONFIG.api.referer,
        'User-Agent': BLACKBOX_CONFIG.api.userAgent
      },
      params: {
        pageNo: 0,
        pageSize: 1
      },
      httpsAgent: new https.Agent({ rejectUnauthorized: false }),
      timeout: 15000
    });

    console.log(`✅ Authentication test successful - Status: ${response.status}`);

    if (response.data && response.data.products) {
      console.log(`📦 API response contains ${response.data.products.length} products`);
    }

    return {
      success: true,
      status: response.status,
      token: token.substring(0, 30) + '...',
      dataSize: JSON.stringify(response.data).length
    };

  } catch (error) {
    const status = error.response?.status;
    const statusText = error.response?.statusText;

    console.log(`❌ Authentication test failed - Status: ${status} ${statusText}`);

    if (status === 401) {
      console.log('🔑 Token is expired or invalid - need fresh token');
    } else if (status === 403) {
      console.log('🚫 Access forbidden - insufficient permissions');
    } else if (status === 429) {
      console.log('⏱️ Rate limit exceeded');
    }

    return {
      success: false,
      error: error.message,
      status: status,
      needsFreshToken: status === 401
    };
  }
}

/**
 * Get or refresh Bearer token for API authentication
 */
async function getBearerToken() {
  console.log('🔍 DEBUG: getBearerToken called');

  // Check for manually configured token first
  if (BLACKBOX_CONFIG.auth.manualToken) {
    console.log('🔍 DEBUG: Using manually configured token');
    return BLACKBOX_CONFIG.auth.manualToken;
  }

  // Check for environment variable token
  if (process.env.BLACKBOX_BEARER_TOKEN) {
    console.log('🔍 DEBUG: Using environment variable token');
    BLACKBOX_CONFIG.auth.bearerToken = process.env.BLACKBOX_BEARER_TOKEN;
    BLACKBOX_CONFIG.auth.tokenExpiry = Date.now() + (24 * 60 * 60 * 1000);
    return process.env.BLACKBOX_BEARER_TOKEN;
  }

  // Check if we have a valid cached token
  if (BLACKBOX_CONFIG.auth.bearerToken && BLACKBOX_CONFIG.auth.tokenExpiry) {
    const now = Date.now();
    const timeUntilExpiry = BLACKBOX_CONFIG.auth.tokenExpiry - now;

    console.log(`🔍 DEBUG: Existing token check - expires in ${timeUntilExpiry}ms, threshold: ${BLACKBOX_CONFIG.auth.refreshThreshold}ms`);

    if (timeUntilExpiry > BLACKBOX_CONFIG.auth.refreshThreshold) {
      console.log('🔍 DEBUG: Using existing valid token');
      return BLACKBOX_CONFIG.auth.bearerToken;
    }
  }

  try {
    console.log('🔑 Attempting to obtain Bearer token...');
    console.log(`🔍 DEBUG: Requesting main website: ${BLACKBOX_CONFIG.url}`);

    // Try to get token from the main website first (without User-Agent)
    const response = await axiosInstance.get(BLACKBOX_CONFIG.url, {
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive'
        // No User-Agent to avoid browser detection
      }
    });

    console.log(`🔍 DEBUG: Website response status: ${response.status}`);
    console.log(`🔍 DEBUG: Website response size: ${response.data ? response.data.length : 0} characters`);

    // Look for token in the response (it might be in script tags or meta tags)
    // This is a common pattern where tokens are embedded in the initial page load
    const tokenPatterns = [
      /bearer["\s]*:[\s]*["']([^"']+)["']/i,
      /token["\s]*:[\s]*["']([^"']+)["']/i,
      /authorization["\s]*:[\s]*["']Bearer\s+([^"']+)["']/i,
      /"access_token"["\s]*:[\s]*["']([^"']+)["']/i,
      /"accessToken"["\s]*:[\s]*["']([^"']+)["']/i,
      /window\.token\s*=\s*["']([^"']+)["']/i,
      /window\.authToken\s*=\s*["']([^"']+)["']/i,
      /data-token\s*=\s*["']([^"']+)["']/i
    ];

    console.log('🔍 DEBUG: Searching for token patterns in response...');

    for (let i = 0; i < tokenPatterns.length; i++) {
      const pattern = tokenPatterns[i];
      const tokenMatch = response.data.match(pattern);

      if (tokenMatch && tokenMatch[1]) {
        const token = tokenMatch[1];
        console.log(`🔍 DEBUG: Found token with pattern ${i + 1}: ${token.substring(0, 20)}...`);

        BLACKBOX_CONFIG.auth.bearerToken = token;
        BLACKBOX_CONFIG.auth.tokenExpiry = Date.now() + (24 * 60 * 60 * 1000); // Assume 24 hour expiry

        console.log('✅ Bearer token obtained successfully');
        return token;
      }
    }

    // Also check for any JWT-like tokens (base64 encoded with dots)
    const jwtPattern = /[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+/g;
    const jwtMatches = response.data.match(jwtPattern);

    if (jwtMatches && jwtMatches.length > 0) {
      console.log(`🔍 DEBUG: Found ${jwtMatches.length} potential JWT tokens`);
      // Use the longest one as it's likely to be the most complete
      const longestJwt = jwtMatches.reduce((a, b) => a.length > b.length ? a : b);

      if (longestJwt.length > 50) { // Reasonable minimum for a JWT
        console.log(`🔍 DEBUG: Using JWT token: ${longestJwt.substring(0, 20)}...`);

        BLACKBOX_CONFIG.auth.bearerToken = longestJwt;
        BLACKBOX_CONFIG.auth.tokenExpiry = Date.now() + (24 * 60 * 60 * 1000);

        console.log('✅ Bearer token (JWT) obtained successfully');
        return longestJwt;
      }
    }

    // For now, return null and handle gracefully
    console.log('⚠️ Could not obtain Bearer token automatically');
    console.log('🔍 DEBUG: No token patterns matched in response');
    return null;

  } catch (error) {
    console.error('❌ Error obtaining Bearer token:', error.message);
    console.log(`🔍 DEBUG: Token acquisition error details:`, {
      status: error.response?.status,
      statusText: error.response?.statusText,
      hasData: !!error.response?.data
    });
    return null;
  }
}

/**
 * Try backup tokens when main token fails
 * @returns {string|null} Working backup token or null
 */
async function tryBackupTokens() {
  console.log('🔄 Trying backup tokens...');

  if (!BLACKBOX_CONFIG.auth.backupTokens || BLACKBOX_CONFIG.auth.backupTokens.length === 0) {
    console.log('⚠️ No backup tokens configured');
    return null;
  }

  for (let i = 0; i < BLACKBOX_CONFIG.auth.backupTokens.length; i++) {
    const token = BLACKBOX_CONFIG.auth.backupTokens[i];
    console.log(`🧪 Testing backup token ${i + 1}/${BLACKBOX_CONFIG.auth.backupTokens.length}...`);

    try {
      // Test the token with a simple API call
      const testResponse = await axios.get(`${BLACKBOX_CONFIG.api.baseUrl}/search/products/facets/category/218`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Origin': BLACKBOX_CONFIG.api.origin,
          'Referer': BLACKBOX_CONFIG.api.referer,
          'User-Agent': BLACKBOX_CONFIG.api.userAgent
        },
        params: { pageNo: 0, pageSize: 1 },
        httpsAgent: new https.Agent({ rejectUnauthorized: false }),
        timeout: 10000
      });

      if (testResponse.status === 200) {
        console.log(`✅ Backup token ${i + 1} works! Updating configuration...`);
        BLACKBOX_CONFIG.auth.bearerToken = token;
        BLACKBOX_CONFIG.auth.tokenExpiry = Date.now() + (24 * 60 * 60 * 1000); // Assume 24h validity
        return token;
      }
    } catch (error) {
      console.log(`❌ Backup token ${i + 1} failed: ${error.response?.status || error.message}`);
    }
  }

  console.log('❌ All backup tokens failed');
  return null;
}

/**
 * Build category-based API URL with parameters
 * Uses specific API URLs from category configuration when available
 */
function buildCategoryApiUrl(categoryId, pageNo = 0, pageSize = null) {
  // First, try to find the category in our configuration to use its specific API URL
  const category = BLACKBOX_CONFIG.categories.find(cat => cat.categoryId === categoryId);

  let baseUrl;
  if (category && category.apiUrl) {
    // Use the specific API URL from category configuration
    baseUrl = category.apiUrl;
    console.log(`🔍 Using specific API URL for category ${categoryId}: ${baseUrl}`);
  } else {
    // Fallback to constructed URL
    baseUrl = BLACKBOX_CONFIG.apiBaseUrl + BLACKBOX_CONFIG.categoryEndpoint + '/' + categoryId;
    console.log(`🔍 Using constructed API URL for category ${categoryId}: ${baseUrl}`);
  }

  const params = new URLSearchParams();

  params.append('pageNo', pageNo.toString());
  params.append('pageSize', (pageSize || BLACKBOX_CONFIG.api.pageSize).toString());
  params.append('sortBy', BLACKBOX_CONFIG.api.sortBy);
  params.append('sortDir', BLACKBOX_CONFIG.api.sortDir);

  // Add select fields
  BLACKBOX_CONFIG.api.selectFields.forEach(field => {
    params.append('select', field);
  });

  return `${baseUrl}?${params.toString()}`;
}

/**
 * Build search API URL with parameters (matching working request exactly)
 */
function buildSearchApiUrl(searchTerm, pageNo = 0, pageSize = null) {
  const actualPageSize = pageSize || BLACKBOX_CONFIG.api.pageSize;
  const selectParams = BLACKBOX_CONFIG.api.selectFields.map(field => `select=${field}`).join('&');

  // Match the exact URL structure from working request (note the double && before select params)
  return `${BLACKBOX_CONFIG.apiBaseUrl}/api/v1/search/products/searchQuery?search=${encodeURIComponent(searchTerm)}&pageNo=${pageNo}&pageSize=${actualPageSize}&sortBy=${BLACKBOX_CONFIG.api.sortBy}&sortDir=${BLACKBOX_CONFIG.api.sortDir}&&${selectParams}`;
}

/**
 * Investigate a category ID to determine what products it contains
 */
async function investigateCategory(categoryId) {
  console.log(`🔍 Investigating category ID: ${categoryId}`);

  try {
    const response = await makeCategoryApiRequest(categoryId, 0, 5); // Get just 5 products for investigation

    if (!response || !response.data) {
      console.log(`⚠️ No data returned for category ${categoryId}`);
      return null;
    }

    const products = Array.isArray(response.data) ? response.data : [response.data];

    if (products.length === 0) {
      console.log(`📄 No products found in category ${categoryId}`);
      return null;
    }

    // Analyze product names to determine category type
    const productNames = products.map(p => p.name || '').filter(name => name.length > 0);
    const categoryInfo = {
      categoryId: categoryId,
      productCount: products.length,
      sampleProducts: productNames.slice(0, 3),
      detectedType: detectCategoryType(productNames),
      totalProducts: response.pagination?.total || products.length
    };

    console.log(`✅ Category ${categoryId}: ${categoryInfo.detectedType} (${categoryInfo.totalProducts} total products)`);
    console.log(`   Sample products: ${categoryInfo.sampleProducts.join(', ')}`);

    return categoryInfo;

  } catch (error) {
    console.log(`❌ Error investigating category ${categoryId}: ${error.message}`);
    return null;
  }
}

/**
 * Detect category type based on product names
 */
function detectCategoryType(productNames) {
  const allNames = productNames.join(' ').toLowerCase();

  const categoryPatterns = [
    { type: 'TV & Audio', keywords: ['tv', 'television', 'smart tv', 'led tv', 'audio', 'speaker', 'soundbar', 'headphones'] },
    { type: 'Smart Phones', keywords: ['phone', 'smartphone', 'mobile', 'iphone', 'samsung galaxy', 'huawei', 'xiaomi'] },
    { type: 'Laptop & PCs', keywords: ['laptop', 'computer', 'pc', 'desktop', 'gaming pc', 'macbook', 'notebook'] },
    { type: 'Home Appliances', keywords: ['refrigerator', 'washing machine', 'dishwasher', 'microwave', 'oven', 'fridge'] },
    { type: 'Air Conditions', keywords: ['air conditioner', 'ac', 'split ac', 'window ac', 'portable ac', 'cooling'] },
    { type: 'Games', keywords: ['playstation', 'xbox', 'nintendo', 'gaming', 'ps5', 'ps4', 'controller', 'console'] },
    { type: 'Personal Care', keywords: ['hair dryer', 'toothbrush', 'shaver', 'straightener', 'beauty', 'grooming'] },
    { type: 'Accessories', keywords: ['case', 'cover', 'charger', 'cable', 'adapter', 'accessory'] },
    { type: 'Small Appliances', keywords: ['blender', 'mixer', 'coffee', 'kettle', 'toaster', 'iron'] }
  ];

  let bestMatch = { type: 'Unknown', score: 0 };

  for (const pattern of categoryPatterns) {
    let score = 0;
    for (const keyword of pattern.keywords) {
      if (allNames.includes(keyword)) {
        score++;
      }
    }

    if (score > bestMatch.score) {
      bestMatch = { type: pattern.type, score: score };
    }
  }

  return bestMatch.score > 0 ? bestMatch.type : 'Unknown';
}

/**
 * Discover and investigate all available categories
 */
async function discoverCategories() {
  console.log('🔍 Discovering BlackBox categories...');

  const discoveredCategories = [];
  const categoryIds = [
    // Known categories from user
    175, 106, 218,
    // Additional investigation range
    ...BLACKBOX_CONFIG.categoryIdsToInvestigate
  ];

  console.log(`🔎 Investigating ${categoryIds.length} potential category IDs...`);

  // Process categories in batches to avoid overwhelming the API
  const batchSize = 5;
  for (let i = 0; i < categoryIds.length; i += batchSize) {
    const batch = categoryIds.slice(i, i + batchSize);

    console.log(`\n📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(categoryIds.length / batchSize)}: [${batch.join(', ')}]`);

    const batchPromises = batch.map(async (categoryId) => {
      try {
        const categoryInfo = await investigateCategory(categoryId);
        if (categoryInfo && categoryInfo.totalProducts > 0) {
          return categoryInfo;
        }
        return null;
      } catch (error) {
        console.log(`⚠️ Error investigating category ${categoryId}: ${error.message}`);
        return null;
      }
    });

    const batchResults = await Promise.all(batchPromises);
    const validCategories = batchResults.filter(cat => cat !== null);
    discoveredCategories.push(...validCategories);

    console.log(`✅ Batch completed: ${validCategories.length} valid categories found`);

    // Add delay between batches to respect rate limits
    if (i + batchSize < categoryIds.length) {
      await delay(BLACKBOX_CONFIG.request.delayBetweenRequests * 2);
    }
  }

  // Sort by product count (descending)
  discoveredCategories.sort((a, b) => b.totalProducts - a.totalProducts);

  console.log(`\n🎉 Category discovery completed!`);
  console.log(`📊 Found ${discoveredCategories.length} valid categories:`);

  discoveredCategories.forEach((cat, index) => {
    console.log(`${index + 1}. Category ${cat.categoryId}: ${cat.detectedType} (${cat.totalProducts} products)`);
  });

  return discoveredCategories;
}

/**
 * Update configuration with discovered categories
 */
function updateCategoriesConfig(discoveredCategories) {
  console.log('🔄 Updating categories configuration...');

  // Create new categories configuration based on discovered categories
  const newCategories = discoveredCategories.map(cat => ({
    name: cat.detectedType !== 'Unknown' ? cat.detectedType : `Category ${cat.categoryId}`,
    slug: cat.detectedType !== 'Unknown'
      ? createCategorySlug(cat.detectedType)
      : `category-${cat.categoryId}`,
    categoryId: cat.categoryId,
    description: `${cat.detectedType} - ${cat.totalProducts} products`,
    totalProducts: cat.totalProducts,
    sampleProducts: cat.sampleProducts
  }));

  // Update the configuration
  BLACKBOX_CONFIG.categories = newCategories;

  console.log(`✅ Updated configuration with ${newCategories.length} categories`);
  return newCategories;
}

/**
 * Make category-based API request with authentication and retry logic
 */
async function makeCategoryApiRequest(categoryId, pageNo = 0, pageSize = null, retries = BLACKBOX_CONFIG.request.retryCount) {
  const url = buildCategoryApiUrl(categoryId, pageNo, pageSize);

  console.log(`🔍 DEBUG: Making category API request for category ${categoryId}, page ${pageNo}`);
  console.log(`🔍 DEBUG: Request URL: ${url}`);

  for (let i = 0; i <= retries; i++) {
    try {
      if (BLACKBOX_CONFIG.request.verbose) {
        console.log(`🌐 Category API Request: ${categoryId} (page ${pageNo}, attempt ${i + 1})`);
      }

      // Get Bearer token
      const token = await getBearerToken();
      console.log(`🔍 DEBUG: Bearer token status: ${token ? 'Available' : 'Not available'}`);

      // Load browser cookies for authentication
      const cookies = await loadBrowserCookies();

      // Headers that match the exact working configuration (lowercase)
      const headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en-US,en;q=0.9,ar;q=0.8',
        'origin': 'https://www.blackbox.com.sa',
        'priority': 'u=1, i',
        'referer': 'https://www.blackbox.com.sa/',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'KWS',
        'cache-control': 'no-cache',
        'host': 'api.ops.blackbox.com.sa'
      };

      // Add browser cookies for authentication
      if (Object.keys(cookies).length > 0) {
        headers['Cookie'] = cookiesToString(cookies);
        console.log('🍪 Using browser cookies for API request');
      } else {
        console.log('⚠️ No browser cookies available - request may fail');
      }

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      console.log(`🔍 DEBUG: Request headers:`, Object.keys(headers));

      // Try direct API call first (bypass session establishment)
      try {
        console.log(`🚀 Attempting direct API call...`);
        const directResponse = await makeDirectApiCall(url);
        console.log(`✅ Direct API call successful!`);
        return directResponse;
      } catch (directError) {
        console.log(`❌ Direct API call failed: ${directError.message}`);
        console.log(`🔄 Falling back to session-based approach...`);
      }

      const response = await axiosInstance.get(url, {
        headers,
        httpsAgent: httpsAgent  // Use the working HTTPS agent configuration
      });

      console.log(`🔍 DEBUG: Response status: ${response.status}`);
      console.log(`🔍 DEBUG: Response headers:`, Object.keys(response.headers || {}));

      if (response.status === 200) {
        console.log(`🔍 DEBUG: Response data structure:`, {
          hasData: !!response.data,
          dataType: typeof response.data,
          isArray: Array.isArray(response.data),
          dataKeys: response.data ? Object.keys(response.data) : 'No data',
          dataLength: Array.isArray(response.data) ? response.data.length : 'Not array'
        });

        // Log first few characters of response for debugging
        const responseStr = JSON.stringify(response.data);
        console.log(`🔍 DEBUG: Response preview (first 200 chars): ${responseStr.substring(0, 200)}...`);

        return response.data;
      }

      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    } catch (error) {
      const status = error.response?.status;
      let errorMessage = error.message;

      console.log(`🔍 DEBUG: Error details:`, {
        status: status,
        message: error.message,
        hasResponse: !!error.response,
        responseData: error.response?.data ? JSON.stringify(error.response.data).substring(0, 200) : 'No response data'
      });

      if (status === 401) {
        errorMessage = 'Authentication failed - Bearer token invalid or expired';
        // Clear token to force refresh on next attempt
        BLACKBOX_CONFIG.auth.bearerToken = null;
        BLACKBOX_CONFIG.auth.tokenExpiry = null;
      } else if (status === 403) {
        errorMessage = 'Access forbidden - API access denied';
      } else if (status === 429) {
        errorMessage = 'Rate limit exceeded - too many requests';
      }

      console.error(`❌ Category API request failed (attempt ${i + 1}): ${errorMessage}`);

      if (i === retries) {
        if (status === 401) {
          console.log(`💡 Authentication required. Bearer token may need to be manually configured.`);
        } else if (status === 429) {
          console.log(`💡 Rate limit hit. Consider reducing request frequency.`);
        }
        throw error;
      }

      // Longer delay for rate limits
      const delayTime = status === 429
        ? BLACKBOX_CONFIG.request.retryDelay * (i + 2) * 3
        : BLACKBOX_CONFIG.request.retryDelay * (i + 1);

      await delay(delayTime);
    }
  }
}

/**
 * Make legacy search API request (fallback)
 */
async function makeSearchApiRequest(searchTerm, pageNo = 0, pageSize = null, retries = BLACKBOX_CONFIG.request.retryCount) {
  const url = buildSearchApiUrl(searchTerm, pageNo, pageSize);

  for (let i = 0; i <= retries; i++) {
    try {
      if (BLACKBOX_CONFIG.request.verbose) {
        console.log(`🌐 Search API Request: ${searchTerm} (page ${pageNo}, attempt ${i + 1})`);
      }

      // Get Bearer token
      const token = await getBearerToken();

      const headers = {};
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await axiosInstance.get(url, { headers });

      if (response.status === 200) {
        return response.data;
      }

      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    } catch (error) {
      const status = error.response?.status;
      let errorMessage = error.message;

      if (status === 401) {
        errorMessage = 'Authentication failed - Bearer token invalid or expired';
        // Clear token to force refresh on next attempt
        BLACKBOX_CONFIG.auth.bearerToken = null;
        BLACKBOX_CONFIG.auth.tokenExpiry = null;

        // Try backup tokens if available
        console.log('🔄 Attempting to use backup tokens...');
        const backupToken = await tryBackupTokens();
        if (backupToken) {
          console.log('✅ Backup token found, retrying request...');
          // Retry the request with the backup token
          continue;
        } else {
          console.log('❌ No working backup tokens available');
        }
      } else if (status === 403) {
        errorMessage = 'Access forbidden - API access denied';
      } else if (status === 429) {
        errorMessage = 'Rate limit exceeded - too many requests';
      }

      console.error(`❌ Search API request failed (attempt ${i + 1}): ${errorMessage}`);

      if (i === retries) {
        if (status === 401) {
          console.log(`💡 Authentication required. Bearer token may need to be manually configured.`);
        } else if (status === 429) {
          console.log(`💡 Rate limit hit. Consider reducing request frequency.`);
        }
        throw error;
      }

      // Longer delay for rate limits
      const delayTime = status === 429
        ? BLACKBOX_CONFIG.request.retryDelay * (i + 2) * 3
        : BLACKBOX_CONFIG.request.retryDelay * (i + 1);

      await delay(delayTime);
    }
  }
}

/**
 * Generate BlackBox product slug from name
 */
function generateProductSlug(name) {
  if (!name) return '';

  return name
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
}

/**
 * Generate BlackBox product URL in correct format
 */
function generateBlackBoxProductUrl(name, sku) {
  const slug = generateProductSlug(name);
  return `https://www.blackbox.com.sa/en/product/${slug}-p-${sku}`;
}

/**
 * Generate BlackBox image URL in correct CDN format
 * Verified working format: https://store.ops.blackbox.com.sa/media/webps/jpg/media/catalog/product/b/e/beko_electric_stone_oven_4_electric_burners_60x60_cm_white_6_.webp
 *
 * FIXED: Prevents double file extensions like .jpg.webp by more aggressively removing ALL image extensions
 */
function generateBlackBoxImageUrl(imagePath, preferHighRes = false) {
  if (!imagePath) return '';

  // If it's already a full URL, return as is
  if (imagePath.startsWith('http')) {
    return imagePath;
  }

  // Remove leading slash if present
  let cleanPath = imagePath.startsWith('/') ? imagePath.substring(1) : imagePath;

  // Ensure the path doesn't already contain the full media path first
  if (cleanPath.startsWith('media/catalog/product/')) {
    cleanPath = cleanPath.replace('media/catalog/product/', '');
  }

  // More aggressive file extension removal to prevent double extensions
  // This regex matches common image extensions at the end of the string (case insensitive)
  // Updated to include more extensions and use more robust pattern
  cleanPath = cleanPath.replace(/\.(jpe?g|png|gif|webp|bmp|tiff?|svg|ico)$/i, '');

  // Additional safety check: remove any remaining common extensions that might have been missed
  // This handles edge cases where the regex might not catch everything
  const commonExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff', '.tif', '.svg', '.ico'];
  commonExtensions.forEach(ext => {
    if (cleanPath.toLowerCase().endsWith(ext.toLowerCase())) {
      cleanPath = cleanPath.substring(0, cleanPath.length - ext.length);
    }
  });

  // Add high-res variant suffix if requested (testing showed _1 variant has higher resolution)
  if (preferHighRes && !cleanPath.endsWith('_1') && !cleanPath.endsWith('_main')) {
    cleanPath += '_1';
  }

  // Generate the correct CDN URL format with .webp extension
  // Using the verified working format from manual testing
  return `https://store.ops.blackbox.com.sa/media/webps/jpg/media/catalog/product/${cleanPath}.webp`;
}

/**
 * Enhanced stock information extraction for BlackBox API response
 */
function extractStockInfo(item) {
  let stock = 0;
  let stockStatus = 'Out of Stock';

  console.log(`🔍 DEBUG: Enhanced stock data analysis for item:`, {
    itemName: item.name || 'Unknown',
    itemSku: item.sku || 'Unknown',
    hasStock: !!item.stock,
    stockType: typeof item.stock,
    stockValue: item.stock,
    stockKeys: item.stock && typeof item.stock === 'object' ? Object.keys(item.stock) : 'Not object',
    hasStatus: !!item.status,
    statusValue: item.status,
    hasBackOrders: !!item.back_orders,
    backOrdersValue: item.back_orders,
    hasInventory: !!item.inventory,
    inventoryType: typeof item.inventory,
    inventoryValue: item.inventory,
    hasQuantity: !!item.quantity,
    quantityType: typeof item.quantity,
    quantityValue: item.quantity,
    hasAvailability: !!item.availability,
    availabilityValue: item.availability,
    allKeys: Object.keys(item).slice(0, 20) // Limit to first 20 keys for readability
  });

  // Log the full item structure for the first few products to understand the API response
  if (Math.random() < 0.1) { // Log 10% of products for debugging
    console.log(`🔍 DEBUG: Full item structure sample:`, JSON.stringify(item, null, 2).substring(0, 1000) + '...');
  }

  // PRIORITY 1: Check BlackBox API specific stock fields
  const stockFields = [
    'stock.qty',
    'stock.quantity',
    'stock.available_qty',
    'stock.salable_qty',
    'inventory.qty',
    'inventory.quantity',
    'inventory.available',
    'quantity',
    'qty',
    'available_qty',
    'salable_qty',
    'stock_qty',
    'stock_quantity'
  ];

  for (const fieldPath of stockFields) {
    const value = getNestedValue(item, fieldPath);
    if (value !== undefined && value !== null) {
      const numValue = parseInt(value);
      if (!isNaN(numValue) && numValue >= 0) {
        stock = numValue;
        console.log(`✅ Found stock quantity in ${fieldPath}: ${stock}`);
        break;
      }
    }
  }

  // PRIORITY 2: Check stock object structure
  if (stock === 0 && item.stock && typeof item.stock === 'object') {
    console.log(`🔍 DEBUG: Analyzing stock object:`, item.stock);

    // Handle various stock object formats
    const stockObj = item.stock;

    // Try different quantity field names
    const qtyFields = ['qty', 'quantity', 'available_qty', 'salable_qty', 'is_qty_decimal', 'stock_qty'];
    for (const field of qtyFields) {
      if (stockObj[field] !== undefined && stockObj[field] !== null) {
        const numValue = parseInt(stockObj[field]);
        if (!isNaN(numValue) && numValue >= 0) {
          stock = numValue;
          console.log(`✅ Found stock in stock.${field}: ${stock}`);
          break;
        }
      }
    }

    // Check boolean stock indicators
    const booleanFields = ['is_in_stock', 'in_stock', 'available', 'is_available'];
    for (const field of booleanFields) {
      if (stockObj[field] === true || stockObj[field] === 1 || stockObj[field] === '1') {
        stock = stock || 1; // Ensure at least 1 if marked as in stock
        console.log(`✅ Found positive stock indicator in stock.${field}: true`);
        break;
      } else if (stockObj[field] === false || stockObj[field] === 0 || stockObj[field] === '0') {
        stock = 0;
        console.log(`❌ Found negative stock indicator in stock.${field}: false`);
        break;
      }
    }
  }

  // PRIORITY 3: Check direct numeric stock field
  if (stock === 0 && item.stock && typeof item.stock === 'number') {
    stock = parseInt(item.stock);
    console.log(`✅ Found direct numeric stock: ${stock}`);
  } else if (stock === 0 && item.stock && typeof item.stock === 'string') {
    const numValue = parseInt(item.stock);
    if (!isNaN(numValue)) {
      stock = numValue;
      console.log(`✅ Found string stock converted to number: ${stock}`);
    }
  }

  // PRIORITY 4: Check inventory field
  if (stock === 0 && item.inventory) {
    if (typeof item.inventory === 'object') {
      const invQty = parseInt(item.inventory.qty || item.inventory.quantity || item.inventory.available || 0);
      if (invQty > 0) {
        stock = invQty;
        console.log(`✅ Found inventory quantity: ${stock}`);
      }
    } else if (typeof item.inventory === 'number') {
      stock = parseInt(item.inventory);
      console.log(`✅ Found direct inventory number: ${stock}`);
    }
  }

  // PRIORITY 5: Check availability indicators
  if (stock === 0) {
    const availabilityFields = ['availability', 'available', 'in_stock', 'is_available'];
    for (const field of availabilityFields) {
      if (item[field] === true || item[field] === 1 || item[field] === '1' ||
          (typeof item[field] === 'string' && item[field].toLowerCase() === 'true')) {
        stock = 1; // Default to 1 if available but no quantity specified
        console.log(`✅ Found availability indicator in ${field}: available`);
        break;
      }
    }
  }

  // PRIORITY 6: Check back_orders field for additional stock availability
  if (item.back_orders && (item.back_orders === 'yes' || item.back_orders === true || item.back_orders === 1)) {
    stock = stock || 1; // Allow back orders as available stock
    console.log(`✅ Back orders available, ensuring stock > 0`);
  }

  // PRIORITY 7: Check product status for availability
  if (stock === 0 && item.status) {
    const status = item.status.toString().toLowerCase();
    if (status === 'enabled' || status === 'active' || status === '1' || status === 'true' || status === 'in stock') {
      stock = 1; // Assume available if status is active but no quantity specified
      console.log(`✅ Product status indicates availability: ${status}`);
    }
  }

  // Determine final stock status
  if (stock > 0) {
    stockStatus = 'In Stock';
  } else {
    stockStatus = 'Out of Stock';
  }

  console.log(`🔍 DEBUG: Final stock determination - Quantity: ${stock}, Status: ${stockStatus}`);

  return { stock, stockStatus };
}

/**
 * Helper function to get nested object values using dot notation
 */
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

/**
 * Enhanced size and UOM extraction for BlackBox API with API data priority
 */
function extractBlackBoxSizeAndUOM(item, productName, categoryName = '') {
  const result = { size: null, uom: null };

  console.log(`🔍 DEBUG: Size/UOM extraction for: "${productName}"`);
  console.log(`🔍 DEBUG: Category: "${categoryName}"`);
  console.log(`🔍 DEBUG: Available item fields:`, Object.keys(item));

  // PRIORITY 1: Extract from BlackBox API response fields
  const apiSizeFields = [
    'size',
    'dimensions',
    'specifications.size',
    'specifications.dimensions',
    'attributes.size',
    'attributes.dimensions',
    'product_size',
    'dimension',
    'capacity',
    'volume'
  ];

  const apiUomFields = [
    'uom',
    'unit',
    'measurement_unit',
    'size_unit',
    'dimension_unit',
    'specifications.unit',
    'specifications.uom',
    'attributes.unit',
    'attributes.uom'
  ];

  // Check for size in API fields
  for (const fieldPath of apiSizeFields) {
    const value = getNestedValue(item, fieldPath);
    if (value && typeof value === 'string' && value.trim()) {
      console.log(`✅ Found size in API field ${fieldPath}: "${value}"`);
      // Parse size from API field
      const parsed = parseApiSizeValue(value);
      if (parsed.size && parsed.uom) {
        console.log(`✅ Parsed API size: ${parsed.size} ${parsed.uom}`);
        return parsed;
      }
    }
  }

  // Check for UOM in API fields
  let apiUom = null;
  for (const fieldPath of apiUomFields) {
    const value = getNestedValue(item, fieldPath);
    if (value && typeof value === 'string' && value.trim()) {
      apiUom = value.trim();
      console.log(`✅ Found UOM in API field ${fieldPath}: "${apiUom}"`);
      break;
    }
  }

  // PRIORITY 2: Extract from product name with BlackBox-specific patterns
  const nameResult = extractSizeFromProductName(productName, categoryName);
  if (nameResult.size && nameResult.uom) {
    // If we found UOM in API but not size, combine them
    if (apiUom && !nameResult.uom) {
      nameResult.uom = apiUom;
    }
    console.log(`✅ Extracted from product name: ${nameResult.size} ${nameResult.uom}`);
    return nameResult;
  }

  // PRIORITY 3: Use API UOM with extracted size if available
  if (nameResult.size && apiUom) {
    console.log(`✅ Combined name size with API UOM: ${nameResult.size} ${apiUom}`);
    return { size: nameResult.size, uom: apiUom };
  }

  // PRIORITY 4: Fallback to original extractSizeAndUOM function
  console.log(`🔍 DEBUG: Falling back to original extractSizeAndUOM function`);
  const fallbackResult = extractSizeAndUOM({ name: productName }, 'blackbox', categoryName);

  if (fallbackResult.size && fallbackResult.uom) {
    console.log(`✅ Fallback extraction successful: ${fallbackResult.size} ${fallbackResult.uom}`);
    return fallbackResult;
  }

  console.log(`⚠️ No size/UOM found for product: "${productName}"`);
  return result;
}

/**
 * Parse size value from API response field
 */
function parseApiSizeValue(value) {
  const result = { size: null, uom: null };

  if (!value || typeof value !== 'string') return result;

  const cleanValue = value.trim().toLowerCase();

  // Common API size patterns
  const patterns = [
    { regex: /(\d+(?:\.\d+)?)\s*(inch|inches|in|")/i, uom: 'Inch' },
    { regex: /(\d+(?:\.\d+)?)\s*(cm|centimeter|centimeters)/i, uom: 'CM' },
    { regex: /(\d+(?:\.\d+)?)\s*(kg|kilogram|kilograms)/i, uom: 'KG' },
    { regex: /(\d+(?:\.\d+)?)\s*(liter|liters|l|lt)/i, uom: 'Liter' },
    { regex: /(\d+(?:\.\d+)?)\s*(btu|btus)/i, uom: 'BTU' },
    { regex: /(\d+(?:\.\d+)?)\s*(ton|tons)/i, uom: 'Ton' },
    { regex: /(\d+(?:\.\d+)?)\s*(cu\.?\s*ft|cubic\s*feet|cuft)/i, uom: 'CuFt' },
    { regex: /(\d+(?:\.\d+)?)\s*(watt|watts|w)/i, uom: 'Watt' },
    { regex: /(\d+(?:\.\d+)?)\s*(mm|millimeter|millimeters)/i, uom: 'MM' }
  ];

  for (const pattern of patterns) {
    const match = cleanValue.match(pattern.regex);
    if (match) {
      result.size = match[1];
      result.uom = pattern.uom;
      return result;
    }
  }

  return result;
}

/**
 * Extract size and UOM from product name with BlackBox-specific patterns
 */
function extractSizeFromProductName(productName, categoryName = '') {
  const result = { size: null, uom: null };

  if (!productName || typeof productName !== 'string') return result;

  const name = productName.toLowerCase();
  const category = categoryName.toLowerCase();

  console.log(`🔍 DEBUG: Extracting size from name: "${productName}"`);

  // TV and Display sizes (Priority: Inch)
  if (name.includes('tv') || name.includes('television') || name.includes('smart tv') ||
      name.includes('led') || name.includes('qled') || name.includes('oled') ||
      category.includes('tv') || category.includes('audio')) {

    console.log(`🔍 DEBUG: Detected TV/Display product`);

    // BlackBox TV patterns: "75inch", "65 inch", "55″", "43""
    const tvPatterns = [
      /(\d{2,3})\s*inch/i,
      /(\d{2,3})\s*-inch/i,
      /(\d{2,3})\s*″/,
      /(\d{2,3})\s*"/,
      /(\d{2,3})\s*'/,
      /(\d{2,3})\s*in\b/i,
      /(\d{2,3})\s*بوصة/,  // Arabic inch
      /(\d{2,3})\s*انش/,   // Arabic inch
    ];

    for (const pattern of tvPatterns) {
      const match = name.match(pattern);
      if (match) {
        const size = parseInt(match[1]);
        if (size >= 20 && size <= 100) {
          result.size = size.toString();
          result.uom = 'Inch';
          console.log(`✅ Found TV size: ${result.size} ${result.uom}`);
          return result;
        }
      }
    }
  }

  // Air Conditioner BTU/Ton
  if (name.includes('air conditioner') || name.includes('split') || name.includes('ac ') ||
      name.includes('مكيف') || name.includes('تكييف') ||
      category.includes('air') || category.includes('condition')) {

    console.log(`🔍 DEBUG: Detected Air Conditioner product`);

    // BTU patterns
    const btuPatterns = [
      /(\d{4,6}(?:,\d{3})?)\s*-?\s*btu/i,
      /(\d{4,6})\s*-?\s*وحدة/,  // Arabic BTU
    ];

    for (const pattern of btuPatterns) {
      const match = name.match(pattern);
      if (match) {
        let btu = match[1].replace(',', '');
        const btuNum = parseInt(btu);
        if (btuNum >= 6000 && btuNum <= 60000) {
          result.size = btuNum.toString();
          result.uom = 'BTU';
          console.log(`✅ Found AC BTU: ${result.size} ${result.uom}`);
          return result;
        }
      }
    }

    // Ton patterns (convert to BTU)
    const tonPatterns = [
      /(\d{1,2}(?:\.\d)?)\s*-?\s*ton/i,
      /(\d{1,2}(?:\.\d)?)\s*-?\s*طن/,  // Arabic ton
    ];

    for (const pattern of tonPatterns) {
      const match = name.match(pattern);
      if (match) {
        const tons = parseFloat(match[1]);
        if (tons >= 0.5 && tons <= 5) {
          const btu = Math.round(tons * 12000);
          result.size = btu.toString();
          result.uom = 'BTU';
          console.log(`✅ Found AC Ton converted to BTU: ${result.size} ${result.uom}`);
          return result;
        }
      }
    }
  }

  // Washing Machine/Dryer capacity (KG)
  if (name.includes('washing') || name.includes('washer') || name.includes('dryer') ||
      name.includes('غسالة') || name.includes('مجفف') || name.includes('غسيل') ||
      category.includes('washing') || category.includes('appliance')) {

    console.log(`🔍 DEBUG: Detected Washing Machine/Dryer product`);

    const kgPatterns = [
      /(\d{1,2}(?:\.\d)?)\s*-?\s*kg/i,
      /(\d{1,2}(?:\.\d)?)\s*-?\s*كيلو/,  // Arabic KG
      /(\d{1,2}(?:\.\d)?)\s*-?\s*كجم/,   // Arabic KG
      /capacity\s*[:\-]?\s*(\d{1,2}(?:\.\d)?)/i,
      /(\d{1,2}(?:\.\d)?)\s*كيلوجرام/,  // Arabic kilogram
    ];

    for (const pattern of kgPatterns) {
      const match = name.match(pattern);
      if (match) {
        const kg = parseFloat(match[1]);
        if (kg >= 3 && kg <= 30) {
          result.size = kg.toString();
          result.uom = 'KG';
          console.log(`✅ Found Washing Machine capacity: ${result.size} ${result.uom}`);
          return result;
        }
      }
    }
  }

  // Refrigerator/Freezer capacity
  if (name.includes('refrigerator') || name.includes('fridge') || name.includes('freezer') ||
      name.includes('ثلاجة') || name.includes('فريزر') || name.includes('تبريد') ||
      category.includes('refrigerator') || category.includes('appliance')) {

    console.log(`🔍 DEBUG: Detected Refrigerator/Freezer product`);

    // Cubic feet patterns (preferred for refrigerators)
    const cuftPatterns = [
      /(\d{1,2}(?:\.\d)?)\s*-?\s*(?:cu\.?\s*ft|cubic\s*feet|قدم)/i,
      /(\d{1,2}(?:\.\d)?)\s*-?\s*ft/i,
      /(\d{1,2}(?:\.\d)?)\s*-?\s*cuft/i,
    ];

    for (const pattern of cuftPatterns) {
      const match = name.match(pattern);
      if (match) {
        const cuft = parseFloat(match[1]);
        if (cuft >= 1 && cuft <= 30) {
          result.size = cuft.toString();
          result.uom = 'CuFt';
          console.log(`✅ Found Refrigerator capacity: ${result.size} ${result.uom}`);
          return result;
        }
      }
    }

    // Liter patterns (fallback)
    const literPatterns = [
      /(\d{2,4})\s*-?\s*(?:l|liter|liters|لتر|ليتر)/i,
      /(\d{2,4})\s*-?\s*lt/i,
    ];

    for (const pattern of literPatterns) {
      const match = name.match(pattern);
      if (match) {
        const liters = parseInt(match[1]);
        if (liters >= 50 && liters <= 1000) {
          result.size = liters.toString();
          result.uom = 'Liter';
          console.log(`✅ Found Refrigerator capacity in liters: ${result.size} ${result.uom}`);
          return result;
        }
      }
    }
  }

  // Oven dimensions (CM)
  if (name.includes('oven') || name.includes('فرن') || name.includes('gas oven') ||
      category.includes('oven') || category.includes('appliance')) {

    console.log(`🔍 DEBUG: Detected Oven product`);

    // Dimension patterns: "60x90cm", "60 x 90 cm"
    const dimensionPatterns = [
      /(\d{2,3})\s*x\s*(\d{2,3})\s*cm/i,
      /(\d{2,3})\s*×\s*(\d{2,3})\s*cm/i,
      /(\d{2,3})\s*cm\s*x\s*(\d{2,3})\s*cm/i,
    ];

    for (const pattern of dimensionPatterns) {
      const match = name.match(pattern);
      if (match) {
        const width = parseInt(match[1]);
        const height = parseInt(match[2]);
        if (width >= 30 && width <= 120 && height >= 30 && height <= 120) {
          result.size = `${width}x${height}`;
          result.uom = 'CM';
          console.log(`✅ Found Oven dimensions: ${result.size} ${result.uom}`);
          return result;
        }
      }
    }

    // Single dimension patterns
    const singleDimPatterns = [
      /(\d{2,3})\s*cm/i,
      /(\d{2,3})\s*سم/,  // Arabic cm
    ];

    for (const pattern of singleDimPatterns) {
      const match = name.match(pattern);
      if (match) {
        const size = parseInt(match[1]);
        if (size >= 30 && size <= 120) {
          result.size = size.toString();
          result.uom = 'CM';
          console.log(`✅ Found Oven size: ${result.size} ${result.uom}`);
          return result;
        }
      }
    }
  }

  console.log(`⚠️ No size pattern matched for: "${productName}"`);
  return result;
}

/**
 * Extract product data from API response item with improved URL and stock handling
 */
function extractProductFromApiResponse(item, categoryName) {
  try {
    console.log(`🔍 DEBUG: Extracting product from API response`);
    console.log(`🔍 DEBUG: Item structure:`, {
      hasItem: !!item,
      itemType: typeof item,
      itemKeys: item ? Object.keys(item) : 'No item',
      hasSku: !!(item && item.sku),
      hasName: !!(item && item.name)
    });

    if (!item) {
      console.log(`🔍 DEBUG: No item provided for extraction`);
      return null;
    }

    if (!item.sku) {
      console.log(`🔍 DEBUG: Item missing SKU:`, item);
      return null;
    }

    // Extract basic product information
    const sku = item.sku || '';

    // Handle name field which can be an array or string
    let name = '';
    if (Array.isArray(item.name)) {
      name = item.name[0] || '';
    } else {
      name = item.name || '';
    }

    const description = item.description || name;

    console.log(`🔍 DEBUG: Basic product info - SKU: ${sku}, Name: ${name}`);

    // Extract brand from various fields (handle arrays)
    let brand = '';
    if (Array.isArray(item.option_text_brand)) {
      brand = item.option_text_brand[0] || '';
    } else if (Array.isArray(item.option_text_a_brand)) {
      brand = item.option_text_a_brand[0] || '';
    } else {
      brand = item.option_text_brand || item.option_text_a_brand || '';
    }

    if (!brand) {
      brand = extractBrand(name);
    }

    console.log(`🔍 DEBUG: Brand extracted: ${brand}`);

    // Extract model with enhanced BlackBox-specific patterns
    const model = extractModel(name, brand, sku);

    // Enhanced price extraction following SWSG store pattern for consistent price management
    let currentPrice = 0;
    let regularPrice = 0;

    console.log(`🔍 DEBUG: Price data analysis:`, {
      hasPricesWithTax: !!(item.prices_with_tax),
      pricesType: typeof item.prices_with_tax,
      pricesKeys: item.prices_with_tax && typeof item.prices_with_tax === 'object' ? Object.keys(item.prices_with_tax) : 'Not object',
      rawPricesData: item.prices_with_tax
    });

    // PRIORITY 1: Extract from prices_with_tax object (BlackBox API primary source)
    if (item.prices_with_tax && typeof item.prices_with_tax === 'object') {
      // BlackBox API price structure analysis
      const priceData = item.prices_with_tax;

      // Try different price field combinations based on BlackBox API structure
      const currentPriceFields = ['price', 'final_price', 'special_price', 'sale_price'];
      const regularPriceFields = ['original_price', 'regular_price', 'list_price', 'msrp', 'price'];

      // Extract current price (discounted/sale price)
      for (const field of currentPriceFields) {
        if (priceData[field] && parseFloat(priceData[field]) > 0) {
          currentPrice = parseFloat(priceData[field]);
          console.log(`✅ Found current price in ${field}: ${currentPrice}`);
          break;
        }
      }

      // Extract regular price (original/list price)
      for (const field of regularPriceFields) {
        if (priceData[field] && parseFloat(priceData[field]) > 0) {
          regularPrice = parseFloat(priceData[field]);
          console.log(`✅ Found regular price in ${field}: ${regularPrice}`);
          break;
        }
      }

      // If we found current but not regular, use current as regular
      if (currentPrice > 0 && regularPrice === 0) {
        regularPrice = currentPrice;
        console.log(`📝 Using current price as regular price: ${regularPrice}`);
      }

      // If we found regular but not current, use regular as current
      if (regularPrice > 0 && currentPrice === 0) {
        currentPrice = regularPrice;
        console.log(`📝 Using regular price as current price: ${currentPrice}`);
      }

    } else if (item.prices_with_tax && (typeof item.prices_with_tax === 'string' || typeof item.prices_with_tax === 'number')) {
      // Handle simple price format
      currentPrice = parseFloat(item.prices_with_tax);
      regularPrice = currentPrice;
      console.log(`✅ Simple price format: ${currentPrice}`);
    }

    // PRIORITY 2: Fallback to other price fields if prices_with_tax is not available
    if (currentPrice === 0 && regularPrice === 0) {
      console.log(`🔍 Trying fallback price fields...`);

      const fallbackFields = ['price', 'cost', 'amount', 'value'];
      for (const field of fallbackFields) {
        if (item[field] && parseFloat(item[field]) > 0) {
          currentPrice = parseFloat(item[field]);
          regularPrice = currentPrice;
          console.log(`✅ Found fallback price in ${field}: ${currentPrice}`);
          break;
        }
      }
    }

    console.log(`🔍 DEBUG: Final extracted prices - Current: ${currentPrice}, Regular: ${regularPrice}`);

    // Extract stock information using improved logic
    const { stock, stockStatus } = extractStockInfo(item);

    // Extract image URL using correct BlackBox CDN format
    let imageUrl = '';
    if (item.image) {
      let imagePath = '';
      if (Array.isArray(item.image)) {
        imagePath = item.image[0] || '';
      } else {
        imagePath = item.image;
      }

      if (imagePath) {
        imageUrl = generateBlackBoxImageUrl(imagePath);
      }
    }

    // Generate product URL using correct BlackBox format
    const productUrl = generateBlackBoxProductUrl(name, sku);

    // Extract size and UOM with BlackBox API data and enhanced patterns
    const { size, uom } = extractBlackBoxSizeAndUOM(item, name, categoryName);

    // Normalize category
    const normalizedCategory = normalizeCategoryName(categoryName);
    const blackboxCategorySlug = createCategorySlug(normalizedCategory);

    // SWSG-style price management for consistent display across catalog and product details
    // Following the same pattern as SWSG store for proper price handling

    let finalCurrentPrice = currentPrice;
    let finalRegularPrice = regularPrice;
    let promotionalPrice = null; // This will be the 'price' field for discounts

    // Apply SWSG-style price logic
    if (currentPrice > 0 && regularPrice > 0) {
      // Check if there's a meaningful discount (more than 1% and more than 1 SAR)
      const discountAmount = regularPrice - currentPrice;
      const discountPercent = (discountAmount / regularPrice) * 100;

      if (discountAmount > 1 && discountPercent > 1) {
        // There's a meaningful discount - show promotional pricing
        promotionalPrice = currentPrice.toString();
        finalCurrentPrice = currentPrice;
        finalRegularPrice = regularPrice;
        console.log(`💰 Promotional pricing: ${currentPrice} SAR (was ${regularPrice} SAR, ${discountPercent.toFixed(1)}% off)`);
      } else {
        // No meaningful discount - treat as regular pricing
        promotionalPrice = null;
        finalCurrentPrice = regularPrice; // Use regular price as current
        finalRegularPrice = regularPrice;
        console.log(`💰 Regular pricing: ${regularPrice} SAR (no promotion)`);
      }
    } else if (currentPrice > 0) {
      // Only current price available
      promotionalPrice = null;
      finalCurrentPrice = currentPrice;
      finalRegularPrice = currentPrice;
      console.log(`💰 Single price: ${currentPrice} SAR`);
    } else if (regularPrice > 0) {
      // Only regular price available
      promotionalPrice = null;
      finalCurrentPrice = regularPrice;
      finalRegularPrice = regularPrice;
      console.log(`💰 Regular price only: ${regularPrice} SAR`);
    } else {
      // No valid prices found - set defaults
      promotionalPrice = null;
      finalCurrentPrice = 0;
      finalRegularPrice = 0;
      console.log(`⚠️ No valid prices found for product: ${name}`);
    }

    // Create product object with SWSG-style price structure
    const product = {
      name: name,
      description: description,
      brand: brand,
      model: model || '',
      sku: sku, // Use actual SKU from BlackBox API without artificial prefix
      price: promotionalPrice, // Promotional/discount price (null if no promotion)
      current_price: finalCurrentPrice.toString(), // Current effective price
      regular_price: finalRegularPrice.toString(), // Regular/list price
      externalId: `blackbox-${sku}`,
      url: productUrl, // Now uses correct BlackBox URL format
      imageUrl: imageUrl, // Now uses correct CDN format
      stock: stock,
      stockStatus: stockStatus, // Now uses improved stock detection
      category: normalizedCategory,
      categorySlug: blackboxCategorySlug,
      store: BLACKBOX_CONFIG.name,
      source: 'blackbox', // Add source field for price utility compatibility
      size: size || '',
      uom: uom || '',
      originalSku: sku // Keep original SKU for reference
    };

    console.log(`🔍 DEBUG: Final product object:`, {
      name: product.name,
      sku: product.sku,
      url: product.url,
      imageUrl: product.imageUrl,
      stock: product.stock,
      stockStatus: product.stockStatus,
      price: product.price, // Promotional price (null if no promotion)
      current_price: product.current_price, // Current effective price
      regular_price: product.regular_price, // Regular/list price
      hasPromotion: product.price !== null,
      source: product.source, // Should be 'blackbox'
      store: product.store // Should be 'BlackBox'
    });

    // Additional debugging for price and stock issues
    console.log(`💰 PRICE DEBUG: Current=${product.current_price}, Regular=${product.regular_price}, Promotional=${product.price}`);
    console.log(`📦 STOCK DEBUG: Quantity=${product.stock}, Status=${product.stockStatus}`);

    // Validate that we have the required data
    if (!product.current_price || parseFloat(product.current_price) === 0) {
      console.warn(`⚠️ WARNING: Product ${product.name} has invalid current_price: ${product.current_price}`);
    }
    if (!product.regular_price || parseFloat(product.regular_price) === 0) {
      console.warn(`⚠️ WARNING: Product ${product.name} has invalid regular_price: ${product.regular_price}`);
    }
    if (product.stock === 0 && product.stockStatus === 'Out of Stock') {
      console.warn(`⚠️ WARNING: Product ${product.name} shows as out of stock despite API data`);
    }

    return product;

  } catch (error) {
    console.error(`❌ Error extracting product from API response:`, error.message);
    return null;
  }
}

/**
 * Search products using search API with pagination
 */
async function searchProductsApi(searchTerm, categoryName) {
  console.log(`🔍 Searching API for: "${searchTerm}" in category: ${categoryName}`);

  const allProducts = [];
  let pageNo = 0;
  let hasMorePages = true;

  try {
    while (hasMorePages) {
      const response = await makeSearchApiRequest(searchTerm, pageNo);

      console.log(`🔍 DEBUG: API Response structure:`, {
        hasResponse: !!response,
        responseType: typeof response,
        responseKeys: response ? Object.keys(response) : 'No response',
        hasData: !!(response && response.data),
        dataType: response && response.data ? typeof response.data : 'No data',
        dataLength: response && response.data && Array.isArray(response.data) ? response.data.length : 'Not array'
      });

      if (!response) {
        console.log(`⚠️ No response returned for search term: ${searchTerm}, page: ${pageNo}`);
        break;
      }

      // Handle the actual API response structure
      let products = [];
      if (response.data && Array.isArray(response.data)) {
        products = response.data;
      } else if (Array.isArray(response)) {
        products = response;
      } else {
        console.log(`⚠️ Unexpected response structure for search term: ${searchTerm}, page: ${pageNo}`);
        console.log(`🔍 DEBUG: Full response:`, JSON.stringify(response, null, 2).substring(0, 500));
        break;
      }

      if (products.length === 0) {
        console.log(`📄 No more products found for: ${searchTerm} (page ${pageNo})`);
        hasMorePages = false;
        break;
      }

      console.log(`📦 Found ${products.length} products for "${searchTerm}" (page ${pageNo})`);

      // Extract products from API response
      for (const item of products) {
        console.log(`🔍 DEBUG: Processing product item:`, {
          hasSku: !!(item && item.sku),
          hasName: !!(item && item.name),
          itemKeys: item ? Object.keys(item).slice(0, 10) : 'No item'
        });

        const product = extractProductFromApiResponse(item, categoryName);
        if (product) {
          allProducts.push(product);
          console.log(`✅ Successfully extracted product: ${product.name}`);
        } else {
          console.log(`❌ Failed to extract product from item`);
        }
      }

      // Check if we should continue to next page
      pageNo++;

      // Stop if we've reached the configured max products limit
      if (BLACKBOX_CONFIG.request.maxProducts > 0 &&
          allProducts.length >= BLACKBOX_CONFIG.request.maxProducts) {
        console.log(`🛑 Reached max products limit (${BLACKBOX_CONFIG.request.maxProducts})`);
        hasMorePages = false;
        break;
      }

      // Check if response indicates no more pages
      if (response.pagination) {
        const totalPages = response.pagination.total_pages || response.pagination.totalPages;
        if (totalPages && pageNo >= totalPages) {
          hasMorePages = false;
        }
      } else if (products.length < BLACKBOX_CONFIG.api.pageSize) {
        // If we got fewer products than page size, likely no more pages
        hasMorePages = false;
      }

      // Add delay between page requests
      if (hasMorePages) {
        await delay(BLACKBOX_CONFIG.request.delayBetweenRequests);
      }
    }

    console.log(`✅ Total products found for "${searchTerm}": ${allProducts.length}`);
    return allProducts;

  } catch (error) {
    console.error(`❌ Error searching for "${searchTerm}":`, error.message);
    return [];
  }
}

/**
 * Scrape products from a category using category-based API with pagination
 */
async function scrapeCategoryProductsApi(categoryId, categoryName) {
  console.log(`🔍 Scraping category ${categoryId}: ${categoryName}`);

  const allProducts = [];
  let pageNo = 0;
  let hasMorePages = true;

  try {
    while (hasMorePages) {
      const response = await makeCategoryApiRequest(categoryId, pageNo);

      if (!response) {
        console.log(`⚠️ No response returned for category ${categoryId}, page: ${pageNo}`);
        break;
      }

      console.log(`🔍 DEBUG: Response structure for category ${categoryId}:`, {
        hasResponse: !!response,
        responseType: typeof response,
        responseKeys: response ? Object.keys(response) : 'No response',
        hasData: !!(response && response.data),
        dataType: response && response.data ? typeof response.data : 'No data'
      });

      // Handle BlackBox API response structure
      let products = [];

      if (response.data) {
        // Check for different possible product locations in BlackBox API response
        if (Array.isArray(response.data)) {
          // Products directly in data array
          products = response.data;
          console.log(`📦 Found products in response.data array: ${products.length} items`);
        } else if (response.data.products && Array.isArray(response.data.products)) {
          // Products in data.products
          products = response.data.products;
          console.log(`📦 Found products in response.data.products: ${products.length} items`);
        } else if (response.data.items && Array.isArray(response.data.items)) {
          // Products in data.items
          products = response.data.items;
          console.log(`📦 Found products in response.data.items: ${products.length} items`);
        } else if (response.data.results && Array.isArray(response.data.results)) {
          // Products in data.results
          products = response.data.results;
          console.log(`📦 Found products in response.data.results: ${products.length} items`);
        } else {
          // Check if data itself contains product-like objects
          console.log(`🔍 DEBUG: Checking data structure:`, {
            dataKeys: Object.keys(response.data),
            hasAggregate: !!response.data.aggregate,
            hasFilters: !!response.data.filters
          });

          // For BlackBox API, check common product field names
          const productFieldNames = ['products', 'items', 'results', 'content', 'docs', 'hits'];

          for (const fieldName of productFieldNames) {
            if (response.data[fieldName] && Array.isArray(response.data[fieldName])) {
              products = response.data[fieldName];
              console.log(`📦 Found products in response.data.${fieldName}: ${products.length} items`);
              break;
            }
          }

          // If still no products found, check all keys for arrays that might contain products
          if (products.length === 0) {
            for (const [key, value] of Object.entries(response.data)) {
              if (Array.isArray(value) && value.length > 0) {
                // Check if this array contains product-like objects
                const firstItem = value[0];
                if (firstItem && typeof firstItem === 'object' && (firstItem.sku || firstItem.name || firstItem.id)) {
                  products = value;
                  console.log(`📦 Found products in response.data.${key}: ${products.length} items`);
                  break;
                }
              }
            }
          }
        }
      } else if (Array.isArray(response)) {
        // Products directly in response array
        products = response;
        console.log(`📦 Found products in response array: ${products.length} items`);
      }

      if (products.length === 0) {
        console.log(`📄 No products found for category ${categoryId} (page ${pageNo})`);
        console.log(`🔍 DEBUG: Full response structure:`, JSON.stringify(response, null, 2).substring(0, 500) + '...');
        hasMorePages = false;
        break;
      }

      console.log(`📦 Found ${products.length} products for category ${categoryId} (page ${pageNo})`);

      // Extract products from API response
      for (const item of products) {
        const product = extractProductFromApiResponse(item, categoryName);
        if (product) {
          allProducts.push(product);
        }
      }

      // Check if we should continue to next page
      pageNo++;

      // Stop if we've reached the configured max products limit
      if (BLACKBOX_CONFIG.request.maxProducts > 0 &&
          allProducts.length >= BLACKBOX_CONFIG.request.maxProducts) {
        console.log(`🛑 Reached max products limit (${BLACKBOX_CONFIG.request.maxProducts})`);
        hasMorePages = false;
        break;
      }

      // Check if response indicates no more pages
      if (response.pagination) {
        const totalPages = response.pagination.total_pages || response.pagination.totalPages;
        if (totalPages && pageNo >= totalPages) {
          hasMorePages = false;
        }
      } else if (products.length < BLACKBOX_CONFIG.api.pageSize) {
        // If we got fewer products than page size, likely no more pages
        hasMorePages = false;
      }

      // Add delay between page requests
      if (hasMorePages) {
        await delay(BLACKBOX_CONFIG.request.delayBetweenRequests);
      }
    }

    console.log(`✅ Total products found for category ${categoryId}: ${allProducts.length}`);
    return allProducts;

  } catch (error) {
    console.error(`❌ Error scraping category ${categoryId}:`, error.message);
    return [];
  }
}

/**
 * Scrape products from a single category using category-based API
 */
async function scrapeCategoryProducts(category) {
  console.log(`\n📂 Processing category: ${category.name} (ID: ${category.categoryId})`);
  const categoryStartTime = Date.now();

  try {
    // Use category-based API to get all products from this category
    const allProducts = await scrapeCategoryProductsApi(category.categoryId, category.name);

    const categoryTime = ((Date.now() - categoryStartTime) / 1000).toFixed(2);
    console.log(`✅ Category ${category.name}: ${allProducts.length} products in ${categoryTime}s`);

    return allProducts;

  } catch (error) {
    console.error(`❌ Error processing category ${category.name}:`, error.message);
    return [];
  }
}

/**
 * Main BlackBox scraping function using category-based API
 */
async function scrapeBlackBox(useCache = false, useBulkOperations = true, discoverNewCategories = false) {
  console.log(`🚀 Starting BlackBox Category-Based API scraper with Session Hijacking`);
  console.log(`   useCache: ${useCache}, useBulkOperations: ${useBulkOperations}, discoverNewCategories: ${discoverNewCategories}`);

  // Check if we have a Bearer token - if so, we can skip cookie validation
  const bearerToken = await getBearerToken();

  if (bearerToken) {
    console.log('✅ Bearer token available - skipping cookie validation');
    console.log(`🔑 Using Bearer token: ${bearerToken.substring(0, 20)}...`);
  } else {
    // Validate browser cookies only if no Bearer token
    console.log('\n🍪 No Bearer token found - validating browser session cookies...');
    const cookieValidation = await validateCookieSession();

    if (!cookieValidation) {
      console.log('❌ Cookie validation failed. Please check your cookies.json file.');
      console.log('📝 Instructions:');
      console.log('1. Open BlackBox website in your browser');
      console.log('2. Export cookies using the provided script');
      console.log('3. Save cookies as cookies.json in the project directory');
      console.log('4. Run the scraper again');
      console.log('');
      console.log('💡 Alternatively, configure a Bearer token:');
      console.log('   Set BLACKBOX_BEARER_TOKEN environment variable');
      console.log('   Or use configureAuthentication() function');

      return {
        success: false,
        count: 0,
        products: [],
        errors: ['Authentication failed - no valid Bearer token or cookies'],
        executionTime: 0,
        discoveredCategories: []
      };
    }

    console.log('✅ Cookie session validated successfully!');
  }

  const startTime = Date.now();

  const result = {
    products: [],
    count: 0,
    errors: [],
    discoveredCategories: []
  };

  try {
    // Ensure BlackBox store exists in database
    let dbStore = await storage.getStoreBySlug(BLACKBOX_CONFIG.slug);
    if (!dbStore) {
      console.log('🏪 Creating BlackBox store in database...');
      dbStore = await storage.createStore({
        name: BLACKBOX_CONFIG.name,
        slug: BLACKBOX_CONFIG.slug,
        url: BLACKBOX_CONFIG.url,
        active: true
      });
      console.log(`✅ Created BlackBox store with ID: ${dbStore.id}`);
    } else {
      console.log(`✅ Found existing BlackBox store with ID: ${dbStore.id}`);
    }

    // Discover categories if requested
    if (discoverNewCategories) {
      console.log('\n🔍 Starting category discovery process...');
      try {
        const discoveredCategories = await discoverCategories();
        result.discoveredCategories = discoveredCategories;

        if (discoveredCategories.length > 0) {
          updateCategoriesConfig(discoveredCategories);
          console.log(`✅ Updated configuration with ${discoveredCategories.length} discovered categories`);
        } else {
          console.log('⚠️ No valid categories discovered, using default configuration');
        }
      } catch (error) {
        console.error('❌ Error during category discovery:', error.message);
        console.log('⚠️ Continuing with default category configuration');
        result.errors.push({
          type: 'category_discovery',
          error: error.message
        });
      }
    }

    // Process all categories using category-based API
    const categoriesToProcess = BLACKBOX_CONFIG.categories;

    console.log(`\n📂 Processing ${categoriesToProcess.length} categories using category-based API...`);

    const categoryPromises = categoriesToProcess.map(async (category) => {
      try {
        return await scrapeCategoryProducts(category);
      } catch (error) {
        console.error(`❌ Error processing category ${category.name}:`, error.message);
        result.errors.push({
          category: category.name,
          error: error.message
        });
        return [];
      }
    });

    // Wait for all categories to complete
    const categoryResults = await Promise.all(categoryPromises);

    // Flatten all products
    const allProducts = categoryResults.flat();
    console.log(`\n📦 Total products scraped: ${allProducts.length}`);

    // Save products to database
    if (allProducts.length > 0) {
      console.log('💾 Saving products to database...');

      for (const product of allProducts) {
        try {
          // Get or create category
          let dbCategory = await storage.getCategoryBySlug(product.categorySlug);
          if (!dbCategory) {
            dbCategory = await storage.createCategory({
              name: product.category,
              slug: product.categorySlug
            });
          }

          // Save product
          let savedProduct;
          if (useBulkOperations) {
            savedProduct = await storage.upsertProductBySku({
              name: product.name,
              model: product.model,
              description: product.description,
              brand: product.brand,
              size: product.size,
              uom: product.uom,
              sku: product.sku,
              price: product.price,
              regular_price: product.regular_price,
              stock: product.stock,
              storeId: dbStore.id,
              categoryId: dbCategory.id,
              externalId: product.externalId,
              url: product.url,
              imageUrl: product.imageUrl
            });
          } else {
            savedProduct = await storage.upsertProduct({
              name: product.name,
              model: product.model,
              description: product.description,
              brand: product.brand,
              size: product.size,
              uom: product.uom,
              sku: product.sku,
              price: product.price,
              regular_price: product.regular_price,
              stock: product.stock,
              storeId: dbStore.id,
              categoryId: dbCategory.id,
              externalId: product.externalId,
              url: product.url,
              imageUrl: product.imageUrl
            });
          }

          // Create product price record for frontend compatibility
          // This ensures latestPrice.stock is available in the frontend
          if (savedProduct && savedProduct.id) {
            await storage.createProductPrice({
              productId: savedProduct.id,
              price: product.current_price || product.price || '0',
              currency: 'SAR',
              stock: product.stock || 0,
              status: product.stock > 0 ? 'in_stock' : 'out_of_stock'
            });

            console.log(`💾 Created price record for ${product.name} - Stock: ${product.stock}, Status: ${product.stock > 0 ? 'in_stock' : 'out_of_stock'}`);
          }

          result.products.push(product);
        } catch (error) {
          console.error(`❌ Error saving product ${product.name}:`, error.message);
          result.errors.push({
            product: product.name,
            error: error.message
          });
        }
      }
    }

    result.count = result.products.length;

    const totalTime = ((Date.now() - startTime) / 1000).toFixed(2);
    console.log(`\n🎉 BlackBox API scraping completed!`);
    console.log(`📊 Results: ${result.count} products, ${result.errors.length} errors in ${totalTime}s`);

    return result;

  } catch (error) {
    console.error('❌ Fatal error in BlackBox API scraper:', error);
    result.errors.push({
      type: 'fatal',
      error: error.message
    });
    return result;
  }
}

/**
 * Standalone category discovery function
 */
async function discoverBlackBoxCategories() {
  console.log('🔍 BlackBox Category Discovery Tool');
  console.log('='.repeat(50));

  try {
    const discoveredCategories = await discoverCategories();

    if (discoveredCategories.length > 0) {
      console.log('\n📋 DISCOVERED CATEGORIES SUMMARY:');
      console.log('='.repeat(50));

      discoveredCategories.forEach((cat, index) => {
        console.log(`\n${index + 1}. Category ID: ${cat.categoryId}`);
        console.log(`   Type: ${cat.detectedType}`);
        console.log(`   Products: ${cat.totalProducts}`);
        console.log(`   Sample: ${cat.sampleProducts.join(', ')}`);
      });

      console.log('\n🔧 CONFIGURATION UPDATE:');
      console.log('='.repeat(50));
      console.log('Add these categories to your BlackBox configuration:');
      console.log('\ncategories: [');
      discoveredCategories.forEach((cat, index) => {
        const comma = index < discoveredCategories.length - 1 ? ',' : '';
        console.log(`  {`);
        console.log(`    name: '${cat.detectedType}',`);
        console.log(`    slug: '${createCategorySlug(cat.detectedType)}',`);
        console.log(`    categoryId: ${cat.categoryId},`);
        console.log(`    description: '${cat.detectedType} - ${cat.totalProducts} products'`);
        console.log(`  }${comma}`);
      });
      console.log(']');

      return discoveredCategories;
    } else {
      console.log('❌ No valid categories discovered');
      return [];
    }

  } catch (error) {
    console.error('❌ Category discovery failed:', error.message);
    return [];
  }
}

// Export the main scraping function, category discovery, and authentication configuration
export { scrapeBlackBox, discoverBlackBoxCategories, configureAuthentication, testBlackboxAuthentication };

// Run scraper if this file is executed directly
// Fix for Windows path handling
const isMainModule = () => {
  try {
    const currentFileUrl = import.meta.url;
    const mainModulePath = process.argv[1];

    // Convert file URL to path for comparison
    const currentFilePath = new URL(currentFileUrl).pathname;

    // Normalize paths for Windows
    const normalizedCurrentPath = currentFilePath.replace(/^\/([A-Z]:)/, '$1').replace(/\//g, '\\');
    const normalizedMainPath = mainModulePath.replace(/\//g, '\\');

    console.log(`🔍 DEBUG: Current file: ${normalizedCurrentPath}`);
    console.log(`🔍 DEBUG: Main module: ${normalizedMainPath}`);

    return normalizedCurrentPath === normalizedMainPath ||
           currentFilePath.endsWith(mainModulePath) ||
           mainModulePath.endsWith('blackbox-scraper.js');
  } catch (error) {
    console.log(`🔍 DEBUG: Module detection error: ${error.message}`);
    // Fallback: check if the main module ends with this file name
    return process.argv[1] && process.argv[1].endsWith('blackbox-scraper.js');
  }
};

if (isMainModule()) {
  const args = process.argv.slice(2);
  const command = args[0] || 'scrape';

  // Configure authentication - try database first, then fallback to hardcoded token
  console.log('🔧 Configuring BlackBox authentication...');

  // Try to get token from database (manual configuration)
  const databaseToken = await getBearerTokenFromDatabase();
  if (databaseToken) {
    console.log('🔑 Using bearer token from database (manual configuration)');
    configureAuthentication({
      bearerToken: databaseToken
    });
  } else {
    console.log('🔑 Using fallback bearer token');
    configureAuthentication({
      bearerToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTYwMjU1OTQsInN0b3JlTmFtZSI6ImVuIn0.POQ0-0E9J62ZFNpoWQf5CkXjDLDGxVPc4nEbYR9WLU4'
    });
  }

  console.log('✅ Authentication configured successfully');

  if (command === 'discover' || command === 'categories') {
    console.log('🎯 Running BlackBox category discovery...');
    discoverBlackBoxCategories()
      .then(categories => {
        console.log(`\n✅ Category discovery completed: ${categories.length} categories found`);
        process.exit(0);
      })
      .catch(error => {
        console.error('❌ Category discovery failed:', error);
        process.exit(1);
      });
  } else {
    console.log('🎯 Running BlackBox category-based API scraper...');
    const discoverNew = args.includes('--discover') || args.includes('-d');

    scrapeBlackBox(false, true, discoverNew)
      .then(result => {
        console.log('✅ BlackBox category-based API scraper completed successfully');
        console.log(`📊 Final results: ${result.count || 0} products, ${result.errors?.length || 0} errors`);
        if (result.discoveredCategories && result.discoveredCategories.length > 0) {
          console.log(`🔍 Discovered ${result.discoveredCategories.length} new categories`);
        }
        process.exit(result.count > 0 ? 0 : 1);
      })
      .catch(error => {
        console.error('❌ BlackBox category-based API scraper failed:', error);
        process.exit(1);
      });
  }
}
