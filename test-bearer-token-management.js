/**
 * Test Bearer Token Management System
 * Tests the complete bearer token management workflow
 */

import { storage } from './server/storage.js';

const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTYwMjU1OTQsInN0b3JlTmFtZSI6ImVuIn0.POQ0-0E9J62ZFNpoWQf5CkXjDLDGxVPc4nEbYR9WLU4';

async function testBearerTokenManagement() {
  console.log('🧪 TESTING BEARER TOKEN MANAGEMENT SYSTEM');
  console.log('='.repeat(50));
  console.log('');

  try {
    // Test 1: Save bearer token
    console.log('📋 TEST 1: Save Bearer Token');
    console.log('-'.repeat(30));
    
    const saveResult = await storage.saveBearerToken('blackbox', TEST_TOKEN);
    console.log('✅ Save result:', saveResult ? 'SUCCESS' : 'FAILED');
    
    if (saveResult) {
      console.log(`   Store: ${saveResult.store}`);
      console.log(`   Token preview: ${saveResult.token.substring(0, 30)}...`);
      console.log(`   Created: ${saveResult.created_at}`);
      console.log(`   Updated: ${saveResult.updated_at}`);
    }

    // Test 2: Retrieve bearer token
    console.log('\n📋 TEST 2: Retrieve Bearer Token');
    console.log('-'.repeat(35));
    
    const retrieveResult = await storage.getBearerToken('blackbox');
    console.log('✅ Retrieve result:', retrieveResult ? 'SUCCESS' : 'FAILED');
    
    if (retrieveResult) {
      console.log(`   Store: ${retrieveResult.store}`);
      console.log(`   Token preview: ${retrieveResult.token.substring(0, 30)}...`);
      console.log(`   Token matches: ${retrieveResult.token === TEST_TOKEN ? 'YES' : 'NO'}`);
      console.log(`   Last updated: ${retrieveResult.updated_at}`);
    }

    // Test 3: Get all bearer tokens
    console.log('\n📋 TEST 3: Get All Bearer Tokens');
    console.log('-'.repeat(35));
    
    const allTokens = await storage.getAllBearerTokens();
    console.log(`✅ Found ${allTokens.length} bearer tokens`);
    
    allTokens.forEach((token, index) => {
      console.log(`   ${index + 1}. Store: ${token.store}`);
      console.log(`      Token: ${token.token.substring(0, 30)}...`);
      console.log(`      Updated: ${token.updated_at}`);
    });

    // Test 4: Update bearer token
    console.log('\n📋 TEST 4: Update Bearer Token');
    console.log('-'.repeat(30));
    
    const updatedToken = TEST_TOKEN.replace('POQ0', 'TEST');
    const updateResult = await storage.saveBearerToken('blackbox', updatedToken);
    console.log('✅ Update result:', updateResult ? 'SUCCESS' : 'FAILED');
    
    if (updateResult) {
      console.log(`   Token updated: ${updateResult.token.includes('TEST') ? 'YES' : 'NO'}`);
      console.log(`   Updated time changed: ${updateResult.updated_at !== updateResult.created_at ? 'YES' : 'NO'}`);
    }

    // Test 5: Test with different store
    console.log('\n📋 TEST 5: Test Different Store');
    console.log('-'.repeat(35));
    
    const alkhToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTYwMjU1OTQsInN0b3JlTmFtZSI6ImVuIn0.ALKHUNAIZAN_TEST_TOKEN';
    const alkhResult = await storage.saveBearerToken('alkhunaizan', alkhToken);
    console.log('✅ Alkhunaizan token save:', alkhResult ? 'SUCCESS' : 'FAILED');

    // Test 6: Verify multiple stores
    console.log('\n📋 TEST 6: Verify Multiple Stores');
    console.log('-'.repeat(40));
    
    const blackboxToken = await storage.getBearerToken('blackbox');
    const alkhunaizan = await storage.getBearerToken('alkhunaizan');
    
    console.log(`✅ Blackbox token: ${blackboxToken ? 'FOUND' : 'NOT FOUND'}`);
    console.log(`✅ Alkhunaizan token: ${alkhunaizan ? 'FOUND' : 'NOT FOUND'}`);
    
    if (blackboxToken && alkhunaizan) {
      console.log('   Both tokens are different:', blackboxToken.token !== alkhunaizan.token ? 'YES' : 'NO');
    }

    // Test 7: Delete bearer token
    console.log('\n📋 TEST 7: Delete Bearer Token');
    console.log('-'.repeat(30));
    
    const deleteResult = await storage.deleteBearerToken('alkhunaizan');
    console.log('✅ Delete result:', deleteResult ? 'SUCCESS' : 'FAILED');
    
    if (deleteResult) {
      console.log(`   Deleted store: ${deleteResult.store}`);
    }
    
    // Verify deletion
    const deletedCheck = await storage.getBearerToken('alkhunaizan');
    console.log(`✅ Token deleted: ${!deletedCheck ? 'YES' : 'NO'}`);

    // Final summary
    console.log('\n📊 BEARER TOKEN MANAGEMENT TEST SUMMARY');
    console.log('='.repeat(45));
    
    const finalTokens = await storage.getAllBearerTokens();
    console.log(`✅ Total tokens in database: ${finalTokens.length}`);
    console.log(`✅ Expected tokens: 1 (blackbox only)`);
    console.log(`✅ Test status: ${finalTokens.length === 1 ? 'PASSED' : 'FAILED'}`);
    
    if (finalTokens.length > 0) {
      console.log('\n📦 Final tokens:');
      finalTokens.forEach((token, index) => {
        console.log(`   ${index + 1}. ${token.store}: ${token.token.substring(0, 30)}...`);
      });
    }

    console.log('\n🎉 BEARER TOKEN MANAGEMENT SYSTEM TEST COMPLETED!');
    console.log('✅ All database operations working correctly');
    console.log('✅ Ready for frontend integration');
    console.log('✅ Manual token management is functional');

    return true;

  } catch (error) {
    console.error('\n❌ BEARER TOKEN MANAGEMENT TEST FAILED!');
    console.error('💥 Error:', error.message);
    console.error('🔧 Check database connection and table creation');
    return false;
  }
}

// Run the test
testBearerTokenManagement()
  .then(success => {
    if (success) {
      console.log('\n✅ Bearer token management test PASSED!');
      process.exit(0);
    } else {
      console.log('\n❌ Bearer token management test FAILED!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\n💥 Test crashed:', error);
    process.exit(1);
  });
