/**
 * Complete Blackbox Fix and Test
 * Tests token, runs scraper, and sends email report
 */

import { testCurrentToken } from './test-current-token.js';
import { scrapeBlackBox } from './blackbox-scraper.js';
import { sendFetchSummaryEmail } from './server/simple-fetch-emailer.js';

/**
 * Complete Blackbox fix workflow
 */
async function fixBlackboxComplete() {
  console.log('🚀 COMPLETE BLACKBOX FIX AND TEST');
  console.log('='.repeat(45));
  console.log('Current token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTYwMjU1OTQsInN0b3JlTmFtZSI6ImVuIn0.POQ0-0E9J62ZFNpoWQf5CkXjDLDGxVPc4nEbYR9WLU4');
  console.log('');
  console.log('This will:');
  console.log('✅ Test the current token');
  console.log('✅ Run complete Blackbox scraper');
  console.log('✅ Send comprehensive email report');
  console.log('✅ Resolve "Fetch Results all 0" issue');
  console.log('');

  const startTime = Date.now();
  let tokenTestPassed = false;
  let scraperResults = null;
  let emailSent = false;

  try {
    // Step 1: Test current token
    console.log('📋 STEP 1: Test Current Token');
    console.log('-'.repeat(30));
    
    tokenTestPassed = await testCurrentToken();
    
    if (tokenTestPassed) {
      console.log('✅ Token test passed - proceeding with scraper');
    } else {
      console.log('❌ Token test failed - will try scraper anyway');
    }

    // Step 2: Run Blackbox scraper
    console.log('\n📋 STEP 2: Run Blackbox Scraper');
    console.log('-'.repeat(35));
    
    console.log('🕷️ Starting Blackbox scraper...');
    scraperResults = await scrapeBlackBox(false, true, false);
    
    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    
    console.log('\n📊 SCRAPER RESULTS:');
    console.log('='.repeat(25));
    console.log(`✅ Success: ${scraperResults.success}`);
    console.log(`📦 Products: ${scraperResults.count || 0}`);
    console.log(`❌ Errors: ${scraperResults.errors?.length || 0}`);
    console.log(`⏱️ Duration: ${duration}s`);
    
    if (scraperResults.errors && scraperResults.errors.length > 0) {
      console.log('\n⚠️ Errors encountered:');
      scraperResults.errors.slice(0, 5).forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }

  } catch (error) {
    console.error('❌ Scraper execution failed:', error.message);
    scraperResults = {
      success: false,
      count: 0,
      errors: [`Scraper execution failed: ${error.message}`],
      duration: ((Date.now() - startTime) / 1000).toFixed(2)
    };
  }

  // Step 3: Send comprehensive email report
  console.log('\n📋 STEP 3: Send Email Report');
  console.log('-'.repeat(30));

  try {
    const emailData = {
      scraperName: 'Blackbox Complete Fix Test',
      newProducts: scraperResults?.count || 0,
      updatedProducts: scraperResults?.updated || 0,
      totalProcessed: scraperResults?.count || 0,
      errors: scraperResults?.errors || [],
      duration: scraperResults?.duration || `${((Date.now() - startTime) / 1000).toFixed(2)}s`,
      timestamp: new Date().toISOString(),
      processedSuccessfully: scraperResults?.success && (scraperResults?.count > 0),
      
      // Token and fix information
      fixAttempt: {
        tokenUsed: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTYwMjU1OTQsInN0b3JlTmFtZSI6ImVuIn0.POQ0-0E9J62ZFNpoWQf5CkXjDLDGxVPc4nEbYR9WLU4',
        tokenTestPassed: tokenTestPassed,
        scraperSuccess: scraperResults?.success || false,
        productsFound: scraperResults?.count || 0,
        authenticationIssues: (scraperResults?.errors || []).filter(e => 
          e.toLowerCase().includes('401') || 
          e.toLowerCase().includes('auth') || 
          e.toLowerCase().includes('unauthorized')
        ),
        fixStatus: scraperResults?.success && (scraperResults?.count > 0) ? 'SUCCESS' : 'NEEDS_ATTENTION'
      },
      
      // Issue resolution tracking
      issueResolution: {
        originalProblem: 'Fetch Results all 0 due to 401 authentication errors',
        solutionAttempted: 'Updated bearer token to fresh working token',
        currentStatus: scraperResults?.success && (scraperResults?.count > 0) ? 'RESOLVED' : 'ONGOING',
        nextActions: scraperResults?.success && (scraperResults?.count > 0) ? 
          ['Monitor for future token expiry', 'Use for regular scraping'] :
          ['Investigate remaining authentication issues', 'Consider alternative token extraction', 'Check API endpoint changes']
      }
    };

    const emailResult = await sendFetchSummaryEmail(emailData);
    
    if (emailResult.success) {
      console.log('✅ Email report sent <NAME_EMAIL>');
      emailSent = true;
    } else {
      console.log('⚠️ Email report failed:', emailResult.error);
    }

  } catch (error) {
    console.error('❌ Email sending failed:', error.message);
  }

  // Final assessment
  const totalDuration = ((Date.now() - startTime) / 1000).toFixed(2);
  const overallSuccess = scraperResults?.success && (scraperResults?.count > 0);
  
  console.log('\n📊 COMPLETE FIX ASSESSMENT:');
  console.log('='.repeat(35));
  console.log(`🔑 Token Test: ${tokenTestPassed ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`🕷️ Scraper: ${scraperResults?.success ? '✅ SUCCESS' : '❌ FAILED'}`);
  console.log(`📦 Products: ${scraperResults?.count || 0}`);
  console.log(`📧 Email: ${emailSent ? '✅ SENT' : '❌ FAILED'}`);
  console.log(`⏱️ Total Time: ${totalDuration}s`);
  
  if (overallSuccess) {
    console.log('\n🎉 BLACKBOX FIX SUCCESSFUL!');
    console.log('✅ Authentication working');
    console.log('✅ Products successfully fetched');
    console.log('✅ "Fetch Results all 0" issue RESOLVED');
    console.log('✅ Email report sent with details');
    console.log('');
    console.log('📧 Check your <NAME_EMAIL> for full report');
    console.log('🚀 Blackbox scraper is now ready for production use!');
  } else {
    console.log('\n⚠️ BLACKBOX FIX NEEDS ATTENTION');
    
    if (!tokenTestPassed) {
      console.log('🔑 Token test failed - may need fresh token');
    }
    
    if (!scraperResults?.success || scraperResults?.count === 0) {
      console.log('🕷️ Scraper issues detected:');
      if (scraperResults?.errors) {
        const authErrors = scraperResults.errors.filter(e => 
          e.toLowerCase().includes('401') || 
          e.toLowerCase().includes('auth')
        );
        if (authErrors.length > 0) {
          console.log('   - Authentication errors still occurring');
          console.log('   - Token may need to be refreshed again');
        }
      }
    }
    
    console.log('\n💡 NEXT STEPS:');
    console.log('1. 📧 Check email report for detailed error analysis');
    console.log('2. 🔑 If 401 errors persist, get a newer token');
    console.log('3. 🔧 Consider using session-based bypass approach');
    console.log('4. 🌐 Verify Blackbox website accessibility');
  }

  return {
    success: overallSuccess,
    tokenTestPassed: tokenTestPassed,
    scraperResults: scraperResults,
    emailSent: emailSent,
    totalDuration: totalDuration
  };
}

// Export the main function
export { fixBlackboxComplete };

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  fixBlackboxComplete()
    .then(results => {
      if (results.success) {
        console.log('\n🎉 BLACKBOX COMPLETE FIX SUCCESSFUL!');
        console.log(`📦 ${results.scraperResults?.count || 0} products fetched`);
        console.log('✅ Ready for production use!');
        process.exit(0);
      } else {
        console.log('\n❌ BLACKBOX COMPLETE FIX NEEDS ATTENTION!');
        console.log('🔧 Check email and logs for next steps');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('\n💥 BLACKBOX COMPLETE FIX CRASHED:', error);
      process.exit(1);
    });
}
