# Manual JWT Token Extraction Guide for Alkhunaizan

## Method 1: Browser Developer Tools (Recommended)

1. **Open Alkhunaizan website:**
   - Go to https://www.alkhunaizan.sa/
   - Open Developer Tools (F12)
   - Go to the "Network" tab
   - Clear existing requests (trash icon)

2. **Navigate to trigger API calls:**
   - Click on any category (e.g., "All Categories")
   - Or go directly to: https://www.alkhunaizan.sa/en/categories/2

3. **Find the API request:**
   - Look for requests to `api.alkhn.com`
   - Find requests like: `https://api.alkhn.com/api/v1/search/products/facets/category/2`

4. **Extract the token:**
   - Click on the API request
   - Go to "Request Headers" section
   - Look for "authorization" header
   - Copy the full token (starts with "Bearer eyJ...")

## Method 2: Browser Console

1. **Open Alkhunaizan website:**
   - Go to https://www.alkhunaizan.sa/
   - Open Developer Tools (F12)
   - Go to the "Console" tab

2. **Run this JavaScript code:**
```javascript
// Check localStorage
for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    const value = localStorage.getItem(key);
    if (value && (value.includes('eyJ') || key.toLowerCase().includes('token'))) {
        console.log(`localStorage[${key}]: ${value}`);
    }
}

// Check sessionStorage
for (let i = 0; i < sessionStorage.length; i++) {
    const key = sessionStorage.key(i);
    const value = sessionStorage.getItem(key);
    if (value && (value.includes('eyJ') || key.toLowerCase().includes('token'))) {
        console.log(`sessionStorage[${key}]: ${value}`);
    }
}

// Intercept fetch requests
const originalFetch = window.fetch;
window.fetch = function(...args) {
    console.log('Fetch request:', args);
    return originalFetch.apply(this, args).then(response => {
        console.log('Fetch response headers:', response.headers);
        return response;
    });
};
```

3. **Navigate to a category page to trigger API calls**

## Method 3: Manual API Test

Try making a request to see what authentication is required:

```bash
curl -X GET "https://api.alkhn.com/api/v1/search/products/facets/category/2?pageNo=1&pageSize=10&sortBy=position&sortDir=ASC&region=all" \
  -H "accept: application/json, text/plain, */*" \
  -H "origin: https://www.alkhunaizan.sa" \
  -H "referer: https://www.alkhunaizan.sa/" \
  -H "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
```

## Expected Token Format

The token should look like:
```
Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NDkyNTQyNzMsInN0b3JlTmFtZSI6ImFyIn0.lUQz8pA1Iz1fpI0Z-W0OWwHWHqCtPW5CrWtyGKLJB4k
```

## Once You Have the Token

1. **Update storeScraper.js:**
   - Find line 2174 in storeScraper.js
   - Replace the old token with the new one
   - Make sure to keep the "Bearer " prefix

2. **Test the token:**
   - Run: `node test-alkhunaizan-api.js`
   - Should show "Token valid: YES"

## Troubleshooting

- **No API requests visible:** Try refreshing the page and navigating to different categories
- **401 Unauthorized:** The token has expired, get a fresh one
- **CORS errors:** This is normal when testing from command line, but the token should still work in the scraper

## Alternative: Use Working Stores

If token extraction is difficult, you can:
1. Continue using the 4 working stores (BH Store, Zagzoog, Tamkeen, Bin Momen)
2. Skip Alkhunaizan for now
3. Revisit Alkhunaizan token extraction later

The system is already working excellently with 3,639 products from 4 stores!
