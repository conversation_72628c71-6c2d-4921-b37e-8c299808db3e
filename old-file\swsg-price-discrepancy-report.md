# SWSG Price Discrepancy Investigation Report

## Executive Summary

**CRITICAL ISSUE IDENTIFIED**: The SWSG scraper is extracting internal promotional prices from JSON data instead of customer-visible catalog prices, resulting in significant price discrepancies.

## Detailed Findings

### 1. Gaming Chair, White
- **Customer sees**: 999 SAR current, 1,098.9 SAR crossed out
- **Scraper extracts**: 299 SAR current, 999 SAR regular (from JSON data)
- **JSON data**: `"shelf_price":999,"price":299`
- **Discrepancy**: Current price off by 700 SAR (299 vs 999)

### 2. Gaming Chair, Black  
- **Customer sees**: 599 SAR current, 659.9 SAR crossed out
- **Scraper extracts**: 199 SAR current, 599 SAR regular (from JSON data)
- **JSON data**: `"shelf_price":599,"price":199`
- **Discrepancy**: Current price off by 400 SAR (199 vs 599)

### 3. Rollercoaster Tycoon Adv Deluxe
- **Customer sees**: 169 SAR current, 105.9 SAR crossed out
- **Scraper extracts**: 20 SAR current, 169 SAR regular (from JSON data)
- **JSON data**: `"shelf_price":169,"price":20`
- **Discrepancy**: Current price off by 149 SAR (20 vs 169)

## Root Cause Analysis

### Primary Issue: JSON Data vs Customer Display
The scraper prioritizes JSON data extraction (`data-item` attribute) which contains:
- Internal promotional prices
- Backend pricing logic
- Discount calculations

However, customers see different prices that are:
- Displayed via CSS/JavaScript rendering
- Updated in real-time
- Based on current promotions and availability

### Technical Analysis
1. **No `data-price-amount` attributes**: SWSG doesn't use these attributes
2. **JSON data is internal**: Contains backend pricing, not customer-facing prices
3. **Missing visible price extraction**: Scraper doesn't properly extract rendered prices
4. **Price priority logic flawed**: Prioritizes JSON over visible prices

## Impact Assessment

### Accuracy Rate: 0% for Gaming Products
- All 3 tested products show significant price discrepancies
- Current prices are consistently lower than customer-visible prices
- Regular prices show mixed accuracy

### Business Impact
- **Customer confusion**: Scraped prices don't match website
- **Competitive analysis errors**: Incorrect price comparisons
- **Inventory management issues**: Wrong pricing data for decisions

## Recommended Technical Fixes

### 1. Immediate Fix: Prioritize Visible Prices for SWSG
```javascript
// PRIORITY 1: Extract visible prices from rendered HTML
// PRIORITY 2: Use JSON data only as fallback
// PRIORITY 3: Apply SWSG-specific price correction logic
```

### 2. Enhanced Price Extraction Strategy
- Extract prices from CSS-rendered elements
- Parse price text from `.price`, `.special-price`, `.old-price` classes
- Implement SWSG-specific price selectors
- Add price validation against known patterns

### 3. SWSG Store-Specific Override
Based on memory: "For SWSG store specifically, all products should be forced to show as 'In Stock' regardless of detected stock status, and price extraction should prioritize user-visible catalog prices over internal JSON data when there are discrepancies."

### 4. Price Validation Logic
- Compare JSON prices vs visible prices
- Flag discrepancies for manual review
- Implement price reasonableness checks
- Add category-based price validation

## Implementation Plan

### Phase 1: Emergency Fix (Immediate)
1. Modify SWSG scraper to prioritize visible price extraction
2. Add SWSG-specific price selectors
3. Implement price discrepancy detection
4. Deploy with monitoring

### Phase 2: Enhanced Extraction (Week 1)
1. Improve visible price parsing algorithms
2. Add price validation rules
3. Implement fallback strategies
4. Add comprehensive logging

### Phase 3: Monitoring & Optimization (Week 2)
1. Set up price accuracy monitoring
2. Implement automated discrepancy alerts
3. Fine-tune extraction algorithms
4. Add performance optimizations

## Testing Requirements

### 1. Price Accuracy Testing
- Test all Gaming category products
- Verify Small Appliances category
- Check Home Appliances category
- Validate across different product types

### 2. Regression Testing
- Ensure other stores not affected
- Verify stock status handling
- Check image and URL extraction
- Validate category assignment

### 3. Performance Testing
- Monitor scraping speed impact
- Check memory usage
- Validate error handling
- Test timeout scenarios

## Success Metrics

### Target Accuracy: 100%
- All scraped prices must match customer-visible prices
- Zero tolerance for price discrepancies
- Real-time validation against website

### Performance Targets
- Scraping speed: No more than 10% slower
- Error rate: Less than 1%
- Memory usage: Within current limits

## Conclusion

The SWSG price discrepancy issue is **critical** and requires **immediate attention**. The root cause is clear: prioritizing internal JSON data over customer-visible prices. The recommended fix involves restructuring the price extraction logic to prioritize visible prices for SWSG store specifically.

**Next Steps**: Implement the emergency fix immediately, followed by comprehensive testing across all SWSG product categories.
