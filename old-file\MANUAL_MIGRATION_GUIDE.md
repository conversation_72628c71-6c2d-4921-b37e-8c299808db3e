# Manual Category Migration Guide for Extra Store Products

## Problem Summary
Extra store products (store_id = 8) are missing `category_id` values in the database, which prevents category badges from displaying in the frontend.

## Solution Overview
We need to assign appropriate `category_id` values to Extra store products based on their product names.

## Step 1: Check Available Categories

First, connect to your database and run this query to see available categories:

```sql
SELECT id, name, slug FROM categories ORDER BY id;
```

## Step 2: Check Current Status

Check how many Extra store products need category assignment:

```sql
SELECT 
  COUNT(*) as total_products,
  COUNT(CASE WHEN category_id IS NOT NULL THEN 1 END) as with_category_id,
  COUNT(CASE WHEN category_id IS NULL THEN 1 END) as missing_category_id
FROM products 
WHERE store_id = 8;
```

## Step 3: Sample Products Analysis

See what products need categorization:

```sql
SELECT id, name, category_id 
FROM products 
WHERE store_id = 8 AND category_id IS NULL
LIMIT 20;
```

## Step 4: Apply Category Assignments

### 4.1 Refrigerator Products
Based on our browser testing, many Extra products are refrigerators:

```sql
UPDATE products 
SET category_id = (
  SELECT id FROM categories 
  WHERE LOWER(name) LIKE '%refrigerator%' 
     OR LOWER(slug) LIKE '%refrigerator%'
     OR LOWER(name) LIKE '%home%kitchen%'
     OR LOWER(name) LIKE '%appliance%'
  ORDER BY 
    CASE 
      WHEN LOWER(name) LIKE '%refrigerator%' THEN 1
      WHEN LOWER(slug) LIKE '%refrigerator%' THEN 2
      WHEN LOWER(name) LIKE '%home%kitchen%' THEN 3
      ELSE 4
    END
  LIMIT 1
)
WHERE store_id = 8 
  AND category_id IS NULL
  AND (
    LOWER(name) LIKE '%refrigerator%' 
    OR LOWER(name) LIKE '%fridge%'
    OR LOWER(name) LIKE '%freezer%'
  );
```

### 4.2 Small Kitchen Appliances

```sql
UPDATE products 
SET category_id = (
  SELECT id FROM categories 
  WHERE LOWER(name) LIKE '%small%appliance%' 
     OR LOWER(name) LIKE '%kitchen%appliance%'
     OR LOWER(name) LIKE '%home%kitchen%'
     OR LOWER(name) LIKE '%appliance%'
  ORDER BY 
    CASE 
      WHEN LOWER(name) LIKE '%small%appliance%' THEN 1
      WHEN LOWER(name) LIKE '%kitchen%appliance%' THEN 2
      WHEN LOWER(name) LIKE '%home%kitchen%' THEN 3
      ELSE 4
    END
  LIMIT 1
)
WHERE store_id = 8 
  AND category_id IS NULL
  AND (
    LOWER(name) LIKE '%blender%' 
    OR LOWER(name) LIKE '%mixer%'
    OR LOWER(name) LIKE '%juicer%'
    OR LOWER(name) LIKE '%toaster%'
    OR LOWER(name) LIKE '%kettle%'
    OR LOWER(name) LIKE '%grinder%'
    OR LOWER(name) LIKE '%coffee%'
  );
```

### 4.3 Large Kitchen Appliances

```sql
UPDATE products 
SET category_id = (
  SELECT id FROM categories 
  WHERE LOWER(name) LIKE '%home%kitchen%'
     OR LOWER(name) LIKE '%appliance%'
     OR LOWER(name) LIKE '%kitchen%'
  ORDER BY 
    CASE 
      WHEN LOWER(name) LIKE '%home%kitchen%' THEN 1
      WHEN LOWER(name) LIKE '%appliance%' THEN 2
      ELSE 3
    END
  LIMIT 1
)
WHERE store_id = 8 
  AND category_id IS NULL
  AND (
    LOWER(name) LIKE '%microwave%' 
    OR LOWER(name) LIKE '%oven%'
    OR LOWER(name) LIKE '%dishwasher%'
    OR LOWER(name) LIKE '%washing machine%'
    OR LOWER(name) LIKE '%washer%'
    OR LOWER(name) LIKE '%dryer%'
    OR LOWER(name) LIKE '%air condition%'
    OR LOWER(name) LIKE '%cooker%'
    OR LOWER(name) LIKE '%stove%'
    OR LOWER(name) LIKE '%hob%'
  );
```

### 4.4 Default Category for Remaining Products

```sql
UPDATE products 
SET category_id = (
  SELECT id FROM categories 
  WHERE LOWER(name) LIKE '%home%kitchen%'
     OR LOWER(name) LIKE '%appliance%'
     OR LOWER(name) LIKE '%other%'
  ORDER BY 
    CASE 
      WHEN LOWER(name) LIKE '%home%kitchen%' THEN 1
      WHEN LOWER(name) LIKE '%appliance%' THEN 2
      ELSE 3
    END
  LIMIT 1
)
WHERE store_id = 8 
  AND category_id IS NULL;
```

## Step 5: Verify Results

Check the migration results:

```sql
SELECT 
  p.category_id,
  c.name as category_name,
  COUNT(*) as product_count
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
WHERE p.store_id = 8 AND p.category_id IS NOT NULL
GROUP BY p.category_id, c.name
ORDER BY product_count DESC;
```

Final status check:

```sql
SELECT 
  COUNT(*) as total_products,
  COUNT(CASE WHEN category_id IS NOT NULL THEN 1 END) as with_category_id,
  COUNT(CASE WHEN category_id IS NULL THEN 1 END) as still_missing_category_id
FROM products 
WHERE store_id = 8;
```

## Step 6: Test Frontend

After running the migration:

1. Go to: http://127.0.0.1:3021/product-catalog?storeId=8&search=refrigerator
2. Check if category badges now appear on Extra store products
3. Verify badges show appropriate categories like "Refrigerators", "Home & Kitchen", etc.

## Expected Results

- Extra store products should display category badges
- Refrigerator products should show "Refrigerators" or similar category
- Small appliances should show appropriate category badges
- Category filtering should work correctly for Extra store

## Troubleshooting

If categories still don't appear:
1. Check if the category_id values were actually updated in the database
2. Verify the categories table has the expected category names
3. Clear browser cache and refresh the page
4. Check browser console for any JavaScript errors

## Files Created

- `migrate-extra-categories.sql` - Complete SQL migration script
- `simple-category-migration.js` - Node.js migration script (if database connectivity works)
- `MANUAL_MIGRATION_GUIDE.md` - This guide
