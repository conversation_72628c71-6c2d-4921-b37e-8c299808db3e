import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, XCircle, AlertTriangle, Key, TestTube, Save, Trash2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface TokenData {
  storeName: string;
  hasToken: boolean;
  tokenPreview: string | null;
  lastUpdated: string | null;
  isValid: boolean;
  isExpired: boolean;
  expiryDate: string | null;
  error: string | null;
}

interface TokenTestResult {
  success: boolean;
  status?: number;
  productCount?: number;
  responseSize?: number;
  error?: string;
  details?: any;
}

interface BearerTokensResponse {
  success: boolean;
  tokens: Record<string, TokenData>;
  storeConfigs: string[];
}

const BearerTokenManagement: React.FC = () => {
  const [tokens, setTokens] = useState<Record<string, TokenData>>({});
  const [storeConfigs, setStoreConfigs] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState<Record<string, boolean>>({});
  const [testing, setTesting] = useState<Record<string, boolean>>({});
  const [tokenInputs, setTokenInputs] = useState<Record<string, string>>({});
  const [testResults, setTestResults] = useState<Record<string, TokenTestResult>>({});
  const { toast } = useToast();

  // Load bearer tokens on component mount
  useEffect(() => {
    loadBearerTokens();
  }, []);

  const loadBearerTokens = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/bearer-tokens');
      const data: BearerTokensResponse = await response.json();
      
      if (data.success) {
        setTokens(data.tokens);
        setStoreConfigs(data.storeConfigs);
        
        // Initialize token inputs with empty strings
        const inputs: Record<string, string> = {};
        data.storeConfigs.forEach(store => {
          inputs[store] = '';
        });
        setTokenInputs(inputs);
      } else {
        toast({
          title: "Error",
          description: "Failed to load bearer tokens",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error loading bearer tokens:', error);
      toast({
        title: "Error",
        description: "Failed to load bearer tokens",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const validateTokenFormat = (token: string): { valid: boolean; error?: string } => {
    if (!token || token.trim().length === 0) {
      return { valid: false, error: 'Token is required' };
    }

    if (!token.startsWith('eyJ')) {
      return { valid: false, error: 'Invalid JWT format - must start with "eyJ"' };
    }

    const parts = token.split('.');
    if (parts.length !== 3) {
      return { valid: false, error: 'Invalid JWT format - must have 3 parts' };
    }

    return { valid: true };
  };

  const saveToken = async (store: string) => {
    const token = tokenInputs[store]?.trim();
    
    if (!token) {
      toast({
        title: "Error",
        description: "Please enter a token",
        variant: "destructive",
      });
      return;
    }

    const validation = validateTokenFormat(token);
    if (!validation.valid) {
      toast({
        title: "Invalid Token Format",
        description: validation.error,
        variant: "destructive",
      });
      return;
    }

    try {
      setSaving(prev => ({ ...prev, [store]: true }));
      
      const response = await fetch(`/api/bearer-tokens/${store}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: "Success",
          description: `Bearer token saved for ${tokens[store]?.storeName || store}`,
        });
        
        // Clear the input and reload tokens
        setTokenInputs(prev => ({ ...prev, [store]: '' }));
        await loadBearerTokens();
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to save token",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error saving token:', error);
      toast({
        title: "Error",
        description: "Failed to save token",
        variant: "destructive",
      });
    } finally {
      setSaving(prev => ({ ...prev, [store]: false }));
    }
  };

  const testToken = async (store: string) => {
    const token = tokenInputs[store]?.trim();
    
    if (!token) {
      toast({
        title: "Error",
        description: "Please enter a token to test",
        variant: "destructive",
      });
      return;
    }

    const validation = validateTokenFormat(token);
    if (!validation.valid) {
      toast({
        title: "Invalid Token Format",
        description: validation.error,
        variant: "destructive",
      });
      return;
    }

    try {
      setTesting(prev => ({ ...prev, [store]: true }));
      
      const response = await fetch(`/api/bearer-tokens/${store}/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      const data = await response.json();

      if (data.success && data.tokenTest) {
        setTestResults(prev => ({ ...prev, [store]: data.tokenTest }));
        
        if (data.tokenTest.success) {
          toast({
            title: "Token Test Successful",
            description: `Found ${data.tokenTest.productCount || 0} products accessible`,
          });
        } else {
          toast({
            title: "Token Test Failed",
            description: data.tokenTest.error || "Authentication failed",
            variant: "destructive",
          });
        }
      } else {
        toast({
          title: "Test Failed",
          description: data.error || "Failed to test token",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error testing token:', error);
      toast({
        title: "Error",
        description: "Failed to test token",
        variant: "destructive",
      });
    } finally {
      setTesting(prev => ({ ...prev, [store]: false }));
    }
  };

  const deleteToken = async (store: string) => {
    if (!confirm(`Are you sure you want to delete the bearer token for ${tokens[store]?.storeName || store}?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/bearer-tokens/${store}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: "Success",
          description: `Bearer token deleted for ${tokens[store]?.storeName || store}`,
        });
        await loadBearerTokens();
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to delete token",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error deleting token:', error);
      toast({
        title: "Error",
        description: "Failed to delete token",
        variant: "destructive",
      });
    }
  };

  const getTokenStatus = (tokenData: TokenData) => {
    if (!tokenData.hasToken) {
      return { status: 'none', color: 'secondary', text: 'No Token' };
    }
    
    if (!tokenData.isValid) {
      return { status: 'invalid', color: 'destructive', text: 'Invalid' };
    }
    
    if (tokenData.isExpired) {
      return { status: 'expired', color: 'destructive', text: 'Expired' };
    }
    
    return { status: 'valid', color: 'default', text: 'Valid' };
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Bearer Token Management
          </CardTitle>
          <CardDescription>
            Manage bearer tokens for store API authentication
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Key className="h-5 w-5" />
          Bearer Token Management
        </CardTitle>
        <CardDescription>
          Manually configure bearer tokens for stores that require API authentication.
          Use this when automatic token extraction fails.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {storeConfigs.map((store) => {
          const tokenData = tokens[store];
          const status = getTokenStatus(tokenData);
          const testResult = testResults[store];
          
          return (
            <div key={store} className="border rounded-lg p-4 space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">{tokenData?.storeName || store}</h3>
                  <p className="text-sm text-muted-foreground">
                    Store: {store}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={status.color as any}>
                    {status.text}
                  </Badge>
                  {tokenData?.hasToken && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => deleteToken(store)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>

              {tokenData?.hasToken && (
                <div className="space-y-2">
                  <div className="text-sm">
                    <strong>Current Token:</strong> {tokenData.tokenPreview}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    <strong>Last Updated:</strong> {formatDate(tokenData.lastUpdated)}
                  </div>
                  {tokenData.expiryDate && (
                    <div className="text-sm text-muted-foreground">
                      <strong>Expires:</strong> {formatDate(tokenData.expiryDate)}
                    </div>
                  )}
                  {tokenData.error && (
                    <Alert variant="destructive">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>{tokenData.error}</AlertDescription>
                    </Alert>
                  )}
                </div>
              )}

              <div className="space-y-3">
                <Label htmlFor={`token-${store}`}>
                  {tokenData?.hasToken ? 'Update Bearer Token' : 'Add Bearer Token'}
                </Label>
                <div className="flex gap-2">
                  <Input
                    id={`token-${store}`}
                    type="password"
                    placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                    value={tokenInputs[store] || ''}
                    onChange={(e) => setTokenInputs(prev => ({ ...prev, [store]: e.target.value }))}
                    className="font-mono text-sm"
                  />
                  <Button
                    onClick={() => testToken(store)}
                    disabled={testing[store] || !tokenInputs[store]?.trim()}
                    variant="outline"
                  >
                    {testing[store] ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <TestTube className="h-4 w-4" />
                    )}
                    Test
                  </Button>
                  <Button
                    onClick={() => saveToken(store)}
                    disabled={saving[store] || !tokenInputs[store]?.trim()}
                  >
                    {saving[store] ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Save className="h-4 w-4" />
                    )}
                    Save
                  </Button>
                </div>
              </div>

              {testResult && (
                <Alert variant={testResult.success ? "default" : "destructive"}>
                  {testResult.success ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <XCircle className="h-4 w-4" />
                  )}
                  <AlertDescription>
                    {testResult.success ? (
                      <div>
                        <strong>Test Successful!</strong>
                        <br />
                        Status: {testResult.status}
                        <br />
                        Products accessible: {testResult.productCount || 0}
                        <br />
                        Response size: {testResult.responseSize || 0} bytes
                      </div>
                    ) : (
                      <div>
                        <strong>Test Failed!</strong>
                        <br />
                        {testResult.error}
                        {testResult.status && (
                          <>
                            <br />
                            Status: {testResult.status}
                          </>
                        )}
                      </div>
                    )}
                  </AlertDescription>
                </Alert>
              )}
            </div>
          );
        })}

        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Important:</strong> Bearer tokens are sensitive credentials. Only paste tokens from trusted sources.
            Tokens are stored securely and will be used automatically by the scrapers when needed.
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
};

export default BearerTokenManagement;
