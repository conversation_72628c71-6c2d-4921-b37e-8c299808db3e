#!/usr/bin/env node

import 'dotenv/config';
import pg from 'pg';

const pool = new pg.Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false
});

async function checkPriceDates() {
  try {
    console.log('🔍 CHECKING PRICE HISTORY DATES FOR PRODUCT 9981');
    console.log('='.repeat(60));

    // Check the date range of price history for product 9981
    const dateRangeResult = await pool.query(`
      SELECT 
        MIN(recorded_at) as earliest_date,
        MAX(recorded_at) as latest_date,
        COUNT(*) as total_entries
      FROM price_history 
      WHERE product_id = 9981
    `);

    if (dateRangeResult.rows.length > 0) {
      const { earliest_date, latest_date, total_entries } = dateRangeResult.rows[0];
      console.log(`📊 Date Range Analysis:`);
      console.log(`   Total entries: ${total_entries}`);
      console.log(`   Earliest date: ${earliest_date}`);
      console.log(`   Latest date: ${latest_date}`);
      
      // Calculate days ago
      const now = new Date();
      const earliestDaysAgo = Math.floor((now - new Date(earliest_date)) / (1000 * 60 * 60 * 24));
      const latestDaysAgo = Math.floor((now - new Date(latest_date)) / (1000 * 60 * 60 * 24));
      
      console.log(`   Earliest is ${earliestDaysAgo} days ago`);
      console.log(`   Latest is ${latestDaysAgo} days ago`);
    }

    // Check recent entries (last 30 days)
    const recentResult = await pool.query(`
      SELECT COUNT(*) as recent_count
      FROM price_history 
      WHERE product_id = 9981 
        AND recorded_at >= NOW() - INTERVAL '30 days'
    `);

    console.log(`\n📅 Recent entries (last 30 days): ${recentResult.rows[0].recent_count}`);

    // Check entries in last 365 days
    const yearResult = await pool.query(`
      SELECT COUNT(*) as year_count
      FROM price_history 
      WHERE product_id = 9981 
        AND recorded_at >= NOW() - INTERVAL '365 days'
    `);

    console.log(`📅 Entries in last year: ${yearResult.rows[0].year_count}`);

    // Show some sample entries
    const sampleResult = await pool.query(`
      SELECT price, recorded_at, change_type
      FROM price_history 
      WHERE product_id = 9981 
      ORDER BY recorded_at DESC
      LIMIT 5
    `);

    console.log(`\n📋 Sample entries (most recent first):`);
    sampleResult.rows.forEach((row, index) => {
      console.log(`   ${index + 1}. ${row.price} SAR - ${row.change_type} - ${row.recorded_at}`);
    });

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await pool.end();
  }
}

checkPriceDates();
