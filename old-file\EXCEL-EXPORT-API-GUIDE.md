# ProductPricePro Excel Export API

## Overview

The Excel Export API provides comprehensive product data export functionality for ProductPricePro. It exports all products from Extra, BH Store, Alkhunaizan, and BlackBox stores in a professionally formatted Excel file with multiple worksheets and advanced features.

## API Endpoints

### 1. Export All Products
```
GET /api/export/products
```

**Description**: Exports all product data to Excel format (.xlsx)

**Response**: 
- Content-Type: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- Content-Disposition: `attachment; filename="ProductPricePro-Export-{timestamp}.xlsx"`

**Features**:
- Complete product data from all stores
- Normalized brand and category information
- Price analysis with discount calculations
- Professional Excel formatting with filters
- Multiple worksheets (Products + Summary)
- Arabic text support

### 2. Export Statistics
```
GET /api/export/products/stats
```

**Description**: Returns export statistics and metadata

**Response**: JSON object with:
```json
{
  "overview": {
    "total_products": 15420,
    "total_stores": 4,
    "total_brands": 856,
    "total_categories": 234,
    "oldest_product": "2024-01-15T10:30:00.000Z",
    "latest_update": "2024-01-17T14:22:15.000Z"
  },
  "storeDistribution": [
    {
      "store_name": "Extra",
      "product_count": 6850
    },
    {
      "store_name": "BH Store", 
      "product_count": 4320
    }
  ],
  "exportInfo": {
    "estimatedFileSize": "8 MB",
    "estimatedTime": "2 minutes"
  }
}
```

## Excel File Structure

### Products Worksheet
Contains all product data with the following columns:

| Column | Description | Format |
|--------|-------------|---------|
| Product ID | Unique product identifier | Number |
| Product Name | Full product name | Text |
| Brand | Normalized brand name | Text |
| Category | Normalized category name | Text |
| Current Price (SAR) | Current selling price | Currency (SAR) |
| Original Price (SAR) | Original/regular price | Currency (SAR) |
| Discount % | Calculated discount percentage | Percentage |
| Store | Store name | Text |
| SKU | Product SKU/model number | Text |
| Stock Status | Availability status | Text (color-coded) |
| Quantity Available | Available quantity | Number |
| Product URL | Direct link to product | Hyperlink |
| Image URL | Product image URL | Hyperlink |
| Description | Product description | Text |
| Model | Product model | Text |
| Created Date | First scraped date | Date/Time |
| Last Updated | Last update timestamp | Date/Time |

### Summary Worksheet
Contains aggregated statistics and insights:

- Overall product statistics
- Store distribution analysis
- Top categories by product count
- Export metadata and timestamps

## Frontend Integration

### React Components

#### 1. ExportButton Component
Full-featured export button with statistics preview:

```tsx
import ExportButton from "@/components/ExportButton";

// Basic usage
<ExportButton />

// With statistics preview
<ExportButton showStats={true} />

// Custom styling
<ExportButton 
  variant="outline" 
  size="lg" 
  className="custom-class"
/>
```

#### 2. CompactExportButton Component
Minimal export button for toolbars:

```tsx
import { CompactExportButton } from "@/components/ExportButton";

<CompactExportButton className="ml-2" />
```

### Integration Examples

#### TopNav Integration
```tsx
<TopNav
  title="Products"
  actions={<CompactExportButton />}
/>
```

#### Page Integration
```tsx
<div className="flex justify-between items-center">
  <h1>Product Management</h1>
  <ExportButton showStats={true} />
</div>
```

## Usage Examples

### JavaScript/Frontend
```javascript
// Direct API call
const exportProducts = async () => {
  try {
    const response = await fetch('/api/export/products');
    const blob = await response.blob();
    
    // Create download link
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'products-export.xlsx';
    link.click();
    
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Export failed:', error);
  }
};

// Get export statistics
const getExportStats = async () => {
  const response = await fetch('/api/export/products/stats');
  const stats = await response.json();
  console.log(`Total products: ${stats.overview.total_products}`);
};
```

### cURL Examples
```bash
# Export products
curl -X GET "http://localhost:3021/api/export/products" \
  -H "Accept: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" \
  -o "products-export.xlsx"

# Get export statistics
curl -X GET "http://localhost:3021/api/export/products/stats" \
  -H "Accept: application/json"
```

## Performance Characteristics

### Response Times
- **Statistics Endpoint**: 50-200ms (typical)
- **Export Generation**: 1-5 minutes (depending on dataset size)
- **File Download**: Immediate after generation

### File Sizes
- **Small Dataset** (1,000 products): ~500KB
- **Medium Dataset** (10,000 products): ~2-5MB  
- **Large Dataset** (50,000+ products): ~10-25MB

### Memory Usage
- Streaming implementation minimizes server memory usage
- Efficient Excel generation with ExcelJS library
- Automatic garbage collection after export completion

## Error Handling

### Common Error Responses

#### 500 - Export Failed
```json
{
  "error": "Export failed",
  "message": "Failed to fetch product data: Connection timeout",
  "timestamp": "2024-01-17T15:30:00.000Z"
}
```

#### 500 - Database Error
```json
{
  "error": "Database connection failed",
  "message": "Unable to connect to product database",
  "timestamp": "2024-01-17T15:30:00.000Z"
}
```

### Frontend Error Handling
```tsx
const [error, setError] = useState<string | null>(null);

const handleExport = async () => {
  try {
    setError(null);
    const response = await fetch('/api/export/products');
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Export failed');
    }
    
    // Handle successful export
  } catch (err) {
    setError(err.message);
  }
};
```

## Security Considerations

### Authentication
- Requires valid user session
- Admin routes require admin role
- API endpoints respect existing authentication middleware

### Rate Limiting
- Export endpoint has built-in timeout protection
- Large exports are processed asynchronously
- Prevents concurrent exports from same user

### Data Privacy
- Exports include only authorized product data
- No sensitive user information included
- Audit trail maintained for export activities

## Testing

### Automated Testing
```bash
# Run API test suite
node test-export-api.js

# Test with custom URL
node test-export-api.js --url=http://localhost:3021
```

### Manual Testing Checklist
- [ ] Export button appears in navigation
- [ ] Statistics preview loads correctly
- [ ] Excel file downloads successfully
- [ ] File opens in Excel/LibreOffice
- [ ] Data formatting is correct
- [ ] Arabic text displays properly
- [ ] Filters and sorting work
- [ ] Summary worksheet contains accurate data

## Deployment

### Production Considerations
1. **Server Resources**: Ensure adequate memory for large exports
2. **Timeout Settings**: Configure appropriate request timeouts
3. **File Storage**: Consider temporary file cleanup
4. **Monitoring**: Track export frequency and performance
5. **Backup**: Regular database backups before large exports

### Environment Variables
```bash
# Optional: Configure export settings
EXPORT_BATCH_SIZE=1000
EXPORT_TIMEOUT=300000
EXPORT_MAX_MEMORY=512MB
```

## Troubleshooting

### Common Issues

#### Export Takes Too Long
- **Cause**: Large dataset or slow database
- **Solution**: Increase timeout, optimize database queries, use pagination

#### File Corruption
- **Cause**: Memory issues or interrupted generation
- **Solution**: Check server memory, restart service, retry export

#### Missing Data
- **Cause**: Database connectivity or query issues
- **Solution**: Verify database connection, check query logs

#### Frontend Button Not Working
- **Cause**: API endpoint not accessible or CORS issues
- **Solution**: Check network tab, verify API routes, check authentication

### Debug Mode
Enable detailed logging by setting log level to 'debug' in the export options:

```javascript
const exporter = new ProductExporter({
  logLevel: 'debug'
});
```

## Support

### Logs Location
- Server logs: Check console output for export progress
- Error logs: Captured in application error handling
- Performance logs: Available in debug mode

### Monitoring
- Track export frequency through API logs
- Monitor file sizes and generation times
- Alert on export failures or timeouts

The Excel Export API provides a robust, scalable solution for ProductPricePro data export needs with comprehensive error handling, performance optimization, and user-friendly frontend integration.
