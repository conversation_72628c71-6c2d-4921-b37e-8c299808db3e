# 🚨 PRODUCTION SERVER FIX GUIDE

## **ISSUE IDENTIFIED**
Your production server at `https://ir-ksa.dream4soft.co.uk` is returning **502 Bad Gateway**, which means:
- ✅ <PERSON><PERSON><PERSON> is running (good)
- ❌ Node.js application is not responding (needs fixing)

## **IMMEDIATE STEPS TO FIX**

### **Step 1: SSH into Your Production Server**
```bash
ssh your-username@your-server-ip
# or however you normally access your production server
```

### **Step 2: Check Application Status**
```bash
# Check if PM2 is running
pm2 status

# Check if your app is in the list
pm2 list

# Check PM2 logs for errors
pm2 logs irksa --lines 50
```

### **Step 3: Check if Port 3021 is Running**
```bash
# Check what's running on port 3021
netstat -tulpn | grep :3021

# Or check all node processes
ps aux | grep node
```

### **Step 4: Restart the Application**
```bash
# If PM2 shows the app but it's errored/stopped
pm2 restart irksa

# If PM2 doesn't show the app, start it fresh
pm2 start ecosystem.config.js --env production

# Save PM2 configuration
pm2 save
```

### **Step 5: Check Application Logs**
```bash
# Check recent logs
pm2 logs irksa --lines 100

# Check error logs specifically
tail -f logs/err.log

# Check if there are database connection issues
grep -i "error\|timeout\|connection" logs/combined.log | tail -20
```

### **Step 6: Test Local Connection**
```bash
# Test if the app responds locally on the server
curl http://localhost:3021/health

# Test the Excel API endpoint
curl http://localhost:3021/api/excel/products?limit=5
```

## **COMMON ISSUES & SOLUTIONS**

### **Issue 1: Database Connection Problems**
If you see database errors in logs:
```bash
# Check your .env file exists and has correct database settings
cat .env | grep -E "DATABASE_URL|DB_"

# Test database connection
npm run test:db
```

### **Issue 2: Missing Dependencies**
If you see module not found errors:
```bash
# Reinstall dependencies
npm install

# Rebuild the application
npm run build
```

### **Issue 3: Port Already in Use**
If port 3021 is occupied by another process:
```bash
# Find what's using port 3021
lsof -i :3021

# Kill the process (replace PID with actual process ID)
kill -9 PID

# Then restart your app
pm2 start ecosystem.config.js --env production
```

### **Issue 4: Environment Variables Missing**
Make sure your `.env` file has:
```env
NODE_ENV=production
PORT=3021
DATABASE_URL=your-database-url
SESSION_SECRET=your-session-secret
```

## **QUICK FIX COMMANDS**

### **Nuclear Option - Complete Restart**
```bash
# Stop everything
pm2 stop all
pm2 delete all

# Kill any remaining node processes
pkill -f node

# Start fresh
pm2 start ecosystem.config.js --env production
pm2 save
```

### **Check Nginx Configuration**
```bash
# Check nginx status
sudo systemctl status nginx

# Check nginx configuration
sudo nginx -t

# Restart nginx if needed
sudo systemctl restart nginx
```

## **AFTER FIXING**

Once your server is running, test these URLs:

1. **Health Check**: `https://ir-ksa.dream4soft.co.uk/health`
2. **Excel API**: `https://ir-ksa.dream4soft.co.uk/api/excel/products?limit=100`
3. **Main App**: `https://ir-ksa.dream4soft.co.uk/login`

## **PREVENTION**

### **Set Up Monitoring**
```bash
# Install PM2 monitoring
pm2 install pm2-server-monit

# Set up automatic restarts
pm2 startup
pm2 save
```

### **Health Check Script**
Create a cron job to check your server every 5 minutes:
```bash
# Add to crontab
*/5 * * * * curl -f http://localhost:3021/health || pm2 restart irksa
```

## **NEED HELP?**

If you're still having issues:

1. **Share the PM2 logs**: `pm2 logs irksa --lines 50`
2. **Share the error logs**: `cat logs/err.log | tail -50`
3. **Share the system status**: `pm2 status && netstat -tulpn | grep :3021`

The Excel API route is already in your code and should work once the server is running properly!
