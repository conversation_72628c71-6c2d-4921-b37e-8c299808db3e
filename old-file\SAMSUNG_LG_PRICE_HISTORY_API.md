# Samsung/LG Price History API Documentation

## Overview

The Samsung/LG API now includes comprehensive price history functionality that allows you to track, analyze, and compare price changes for Samsung and LG appliances over time.

## New Endpoints Added

### 1. Single Product Price History
**GET** `/api/samsung/price-history/:productId`

Get detailed price history for a specific Samsung/LG product.

**Parameters:**
- `productId` (path): Product ID
- `days` (query, optional): Number of days to look back (default: 30)
- `limit` (query, optional): Maximum number of price entries (default: 100)

**Example:**
```bash
GET /api/samsung/price-history/123?days=30&limit=50
```

**Response:**
```json
{
  "success": true,
  "data": {
    "product": {
      "id": 123,
      "name": "Samsung Refrigerator RF28T5001SR",
      "brand": "Samsung",
      "storeName": "Extra"
    },
    "priceHistory": [
      {
        "id": 1,
        "price": 2999.00,
        "currency": "SAR",
        "stock": 5,
        "changeType": "price_change",
        "changeAmount": -100.00,
        "changePercentage": -3.23,
        "previousPrice": 3099.00,
        "recordedAt": "2024-01-15T10:30:00Z",
        "timestamp": "2024-01-15T10:30:00Z"
      }
    ],
    "statistics": {
      "currentPrice": 2999.00,
      "averagePrice": 3049.50,
      "minPrice": 2899.00,
      "maxPrice": 3199.00,
      "priceChanges": 5,
      "totalEntries": 25
    },
    "period": {
      "days": 30,
      "from": "2023-12-16T00:00:00Z",
      "to": "2024-01-15T00:00:00Z"
    }
  }
}
```

### 2. Bulk Price History
**POST** `/api/samsung/price-history/bulk`

Get price history for multiple products in a single request.

**Request Body:**
```json
{
  "productIds": [123, 124, 125],
  "days": 30
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "products": [
      {
        "product": { "id": 123, "name": "Samsung RF28T5001SR", "brand": "Samsung" },
        "priceHistory": [...],
        "statistics": { "currentPrice": 2999.00, "averagePrice": 3049.50 }
      }
    ],
    "summary": {
      "requestedProducts": 3,
      "validProducts": 3,
      "invalidProducts": 0,
      "totalPriceEntries": 75,
      "period": { "days": 30, "from": "...", "to": "..." }
    }
  }
}
```

### 3. Price Trends by Category
**GET** `/api/samsung/price-trends/:category`

Get price trends for a specific category of appliances.

**Parameters:**
- `category` (path): Category name (`refrigerator`, `washing machine`, `dishwasher`, `dryer`, `all`)
- `days` (query, optional): Number of days to analyze (default: 30)
- `brand` (query, optional): Brand filter (`samsung`, `lg`, `all`) (default: `all`)

**Example:**
```bash
GET /api/samsung/price-trends/refrigerator?days=30&brand=samsung
```

**Response:**
```json
{
  "success": true,
  "data": {
    "category": "refrigerator",
    "brand": "samsung",
    "products": [
      { "id": 123, "name": "Samsung RF28T5001SR", "brand": "Samsung", "category": "refrigerator" }
    ],
    "trends": [
      {
        "date": "2024-01-01",
        "averagePrice": 3050.00,
        "minPrice": 2899.00,
        "maxPrice": 3199.00,
        "productCount": 15
      }
    ],
    "summary": {
      "totalProducts": 15,
      "averagePrice": 3025.50,
      "priceRange": { "min": 2799.00, "max": 3299.00 },
      "period": { "days": 30, "from": "...", "to": "..." }
    }
  }
}
```

### 4. Samsung vs LG Price Comparison
**GET** `/api/samsung/price-comparison/:category`

Compare average prices between Samsung and LG for a specific category.

**Parameters:**
- `category` (path): Category name (`refrigerator`, `washing machine`, `dishwasher`, `dryer`)
- `days` (query, optional): Number of days to analyze (default: 30)

**Example:**
```bash
GET /api/samsung/price-comparison/washing machine?days=30
```

**Response:**
```json
{
  "success": true,
  "data": {
    "category": "washing machine",
    "samsung": {
      "products": [...],
      "averagePrice": 1899.50,
      "priceRange": { "min": 1299.00, "max": 2499.00 },
      "totalPriceEntries": 45
    },
    "lg": {
      "products": [...],
      "averagePrice": 1799.25,
      "priceRange": { "min": 1199.00, "max": 2299.00 },
      "totalPriceEntries": 38
    },
    "comparison": {
      "priceDifference": 100.25,
      "percentageDifference": 5.57,
      "samsungCheaper": false,
      "lgCheaper": true
    },
    "period": { "days": 30, "from": "...", "to": "..." }
  }
}
```

### 5. Comprehensive Price Analytics
**GET** `/api/samsung/price-analytics`

Get comprehensive analytics including category, brand, and store breakdowns.

**Parameters:**
- `days` (query, optional): Number of days to analyze (default: 30)
- `brand` (query, optional): Brand filter (`samsung`, `lg`, `all`) (default: `all`)

**Example:**
```bash
GET /api/samsung/price-analytics?days=30&brand=all
```

**Response:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "totalProducts": 150,
      "categories": {
        "refrigerator": {
          "productCount": 45,
          "averagePrice": 3025.50,
          "priceRange": { "min": 2299.00, "max": 4999.00 },
          "priceEntries": 225
        },
        "washing machine": {
          "productCount": 38,
          "averagePrice": 1849.25,
          "priceRange": { "min": 1199.00, "max": 2499.00 },
          "priceEntries": 190
        }
      },
      "brands": {
        "Samsung": {
          "productCount": 78,
          "averagePrice": 2450.75,
          "priceRange": { "min": 1199.00, "max": 4999.00 },
          "priceEntries": 390
        },
        "LG": {
          "productCount": 72,
          "averagePrice": 2325.50,
          "priceRange": { "min": 1099.00, "max": 4599.00 },
          "priceEntries": 360
        }
      },
      "stores": {
        "Extra": { "productCount": 45, "averagePrice": 2399.00 },
        "Alkhunaizan": { "productCount": 32, "averagePrice": 2299.50 }
      },
      "priceAnalytics": {
        "averagePrice": 2388.12,
        "priceRange": { "min": 1099.00, "max": 4999.00 },
        "totalPriceEntries": 750
      }
    },
    "period": { "days": 30, "from": "...", "to": "..." }
  }
}
```

## Features

### ✅ **Comprehensive Price Tracking**
- Individual product price history
- Bulk price history for multiple products
- Price change detection and analysis
- Statistical summaries (min, max, average prices)

### ✅ **Trend Analysis**
- Daily price trends by category
- Brand-specific trend analysis
- Price movement patterns over time
- Category performance comparison

### ✅ **Competitive Analysis**
- Samsung vs LG price comparison
- Brand performance metrics
- Market positioning insights
- Price difference calculations

### ✅ **Advanced Analytics**
- Multi-dimensional analytics (category, brand, store)
- Comprehensive market overview
- Price distribution analysis
- Performance benchmarking

### ✅ **Flexible Filtering**
- Date range filtering (days parameter)
- Brand-specific filtering
- Category-specific analysis
- Store-level breakdowns

## Authentication

All endpoints require authentication. Include the authorization header:

```bash
Authorization: Bearer your-auth-token
```

## Error Handling

All endpoints return standardized error responses:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message"
}
```

Common error codes:
- `400`: Invalid parameters
- `401`: Authentication required
- `404`: Product/data not found
- `500`: Internal server error

## Usage Examples

### Get Price History for Samsung Refrigerator
```bash
curl -H "Authorization: Bearer token" \
  "http://localhost:3001/api/samsung/price-history/123?days=30"
```

### Compare Samsung vs LG Washing Machines
```bash
curl -H "Authorization: Bearer token" \
  "http://localhost:3001/api/samsung/price-comparison/washing machine"
```

### Get Comprehensive Analytics
```bash
curl -H "Authorization: Bearer token" \
  "http://localhost:3001/api/samsung/price-analytics?days=30&brand=all"
```

## Testing

Run the test script to verify all endpoints:

```bash
node test-samsung-lg-price-history.js
```

For manual testing of specific endpoints:

```bash
node test-samsung-lg-price-history.js --manual
```

## Integration Notes

- Price history data is automatically populated by the existing scraper system
- All endpoints filter for Samsung/LG appliances only (refrigerators, washing machines, dishwashers, dryers)
- Price data includes currency (SAR), stock levels, and change tracking
- Endpoints are optimized for performance with appropriate limits and pagination
- Full backward compatibility with existing Samsung/LG API endpoints
