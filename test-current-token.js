/**
 * Test Current Blackbox Token
 * Quick test of the token you just updated
 */

import axios from 'axios';
import https from 'https';

const CURRENT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTYwMjU1OTQsInN0b3JlTmFtZSI6ImVuIn0.POQ0-0E9J62ZFNpoWQf5CkXjDLDGxVPc4nEbYR9WLU4';

/**
 * Test the current token
 */
async function testCurrentToken() {
  console.log('🧪 TESTING CURRENT BLACKBOX TOKEN');
  console.log('='.repeat(40));
  console.log(`Token: ${CURRENT_TOKEN.substring(0, 30)}...`);
  console.log('');

  // Decode token to check expiry
  try {
    const parts = CURRENT_TOKEN.split('.');
    const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString());
    
    if (payload.exp) {
      const expiryDate = new Date(payload.exp * 1000);
      const now = new Date();
      const timeUntilExpiry = expiryDate.getTime() - now.getTime();
      const hoursUntilExpiry = Math.floor(timeUntilExpiry / (1000 * 60 * 60));
      
      console.log(`⏰ Token expires: ${expiryDate.toLocaleString()}`);
      console.log(`⏳ Hours until expiry: ${hoursUntilExpiry}`);
      console.log(`✅ Token status: ${timeUntilExpiry > 0 ? 'VALID' : 'EXPIRED'}`);
      console.log('');
    }
  } catch (e) {
    console.log('⚠️ Could not decode token expiry');
  }

  // Test multiple endpoints
  const testEndpoints = [
    'https://api.ops.blackbox.com.sa/api/v1/search/products/facets/category/175', // Air conditioners
    'https://api.ops.blackbox.com.sa/api/v1/search/products/facets/category/176', // Refrigerators
    'https://api.ops.blackbox.com.sa/api/v1/search/products/facets/category/218'  // Gaming (from your error)
  ];

  let workingEndpoints = 0;
  let totalProducts = 0;

  for (let i = 0; i < testEndpoints.length; i++) {
    const url = testEndpoints[i];
    const categoryId = url.split('/').pop();
    
    console.log(`🔍 Testing category ${categoryId}...`);
    
    try {
      const response = await axios.get(url, {
        headers: {
          'Authorization': `Bearer ${CURRENT_TOKEN}`,
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'en-US,en;q=0.9',
          'Origin': 'https://www.blackbox.com.sa',
          'Referer': 'https://www.blackbox.com.sa/',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
        },
        params: {
          pageNo: 0,
          pageSize: 10
        },
        httpsAgent: new https.Agent({ rejectUnauthorized: false }),
        timeout: 15000
      });
      
      const productCount = response.data?.products?.length || 0;
      totalProducts += productCount;
      workingEndpoints++;
      
      console.log(`   ✅ SUCCESS - Status: ${response.status}, Products: ${productCount}`);
      
    } catch (error) {
      const status = error.response?.status;
      console.log(`   ❌ FAILED - Status: ${status}, Error: ${error.message}`);
      
      if (status === 401) {
        console.log(`   🔑 Authentication failed - token invalid or expired`);
      }
    }
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log('\n📊 TOKEN TEST SUMMARY:');
  console.log('='.repeat(25));
  console.log(`✅ Working endpoints: ${workingEndpoints}/${testEndpoints.length}`);
  console.log(`📦 Total products found: ${totalProducts}`);
  console.log(`🔑 Token status: ${workingEndpoints > 0 ? 'WORKING' : 'FAILED'}`);

  if (workingEndpoints > 0) {
    console.log('\n🎉 TOKEN IS WORKING!');
    console.log('✅ Authentication successful');
    console.log('✅ Ready to run Blackbox scraper');
    return true;
  } else {
    console.log('\n❌ TOKEN FAILED ALL TESTS');
    console.log('💡 Possible issues:');
    console.log('   - Token has expired');
    console.log('   - Token format is incorrect');
    console.log('   - API endpoints have changed');
    console.log('   - Additional authentication required');
    return false;
  }
}

// Run the test
testCurrentToken()
  .then(success => {
    if (success) {
      console.log('\n✅ Current token test PASSED!');
      console.log('🚀 Run: node blackbox-scraper.js');
      process.exit(0);
    } else {
      console.log('\n❌ Current token test FAILED!');
      console.log('🔧 Need to get a fresh token');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\n💥 Token test crashed:', error);
    process.exit(1);
  });
