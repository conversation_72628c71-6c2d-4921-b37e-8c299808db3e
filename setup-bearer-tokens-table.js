/**
 * Setup Bearer Tokens Table
 * Creates the database table for bearer token management
 */

import { storage } from './server/storage.js';
import fs from 'fs';
import path from 'path';

async function setupBearerTokensTable() {
  console.log('🚀 SETTING UP BEARER TOKENS TABLE');
  console.log('='.repeat(40));
  console.log('');

  try {
    // Read the SQL migration file
    const migrationPath = path.join(process.cwd(), 'server', 'migrations', 'create_bearer_tokens_table.sql');
    
    if (!fs.existsSync(migrationPath)) {
      console.log('📝 Creating migration file...');
      
      const migrationSQL = `-- Create bearer_tokens table for manual token management
-- This table stores bearer tokens for different stores that require authentication

CREATE TABLE IF NOT EXISTS bearer_tokens (
    id SERIAL PRIMARY KEY,
    store VARCHAR(50) UNIQUE NOT NULL,
    token TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster lookups by store
CREATE INDEX IF NOT EXISTS idx_bearer_tokens_store ON bearer_tokens(store);

-- Add comments for documentation
COMMENT ON TABLE bearer_tokens IS 'Stores bearer tokens for API authentication with different stores';
COMMENT ON COLUMN bearer_tokens.store IS 'Store identifier (e.g., blackbox, alkhunaizan)';
COMMENT ON COLUMN bearer_tokens.token IS 'JWT bearer token for API authentication';
COMMENT ON COLUMN bearer_tokens.created_at IS 'When the token was first added';
COMMENT ON COLUMN bearer_tokens.updated_at IS 'When the token was last updated';`;

      // Ensure migrations directory exists
      const migrationsDir = path.dirname(migrationPath);
      if (!fs.existsSync(migrationsDir)) {
        fs.mkdirSync(migrationsDir, { recursive: true });
      }
      
      fs.writeFileSync(migrationPath, migrationSQL);
      console.log('✅ Migration file created');
    }

    console.log('📋 Reading migration SQL...');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('🔧 Executing migration...');
    
    // Execute the migration using the storage pool directly
    const pool = storage.pool || storage.getPool();
    
    if (!pool) {
      throw new Error('Database pool not available');
    }
    
    await pool.query(migrationSQL);
    
    console.log('✅ Bearer tokens table created successfully');
    
    // Test the table by inserting a sample token
    console.log('\n📋 Testing table with sample data...');
    
    const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTYwMjU1OTQsInN0b3JlTmFtZSI6ImVuIn0.POQ0-0E9J62ZFNpoWQf5CkXjDLDGxVPc4nEbYR9WLU4';
    
    const insertResult = await pool.query(`
      INSERT INTO bearer_tokens (store, token) 
      VALUES ($1, $2) 
      ON CONFLICT (store) DO UPDATE SET 
        token = EXCLUDED.token,
        updated_at = NOW()
      RETURNING *
    `, ['blackbox', testToken]);
    
    if (insertResult.rows.length > 0) {
      console.log('✅ Sample token inserted successfully');
      console.log(`   Store: ${insertResult.rows[0].store}`);
      console.log(`   Token: ${insertResult.rows[0].token.substring(0, 30)}...`);
      console.log(`   Created: ${insertResult.rows[0].created_at}`);
    }
    
    // Test retrieval
    const selectResult = await pool.query('SELECT * FROM bearer_tokens WHERE store = $1', ['blackbox']);
    
    if (selectResult.rows.length > 0) {
      console.log('✅ Token retrieval test successful');
      console.log(`   Retrieved token matches: ${selectResult.rows[0].token === testToken ? 'YES' : 'NO'}`);
    }
    
    // Show table structure
    console.log('\n📋 Table structure verification...');
    const tableInfo = await pool.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'bearer_tokens' 
      ORDER BY ordinal_position
    `);
    
    console.log('✅ Table columns:');
    tableInfo.rows.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? '(NOT NULL)' : ''}`);
    });
    
    // Show indexes
    const indexInfo = await pool.query(`
      SELECT indexname, indexdef 
      FROM pg_indexes 
      WHERE tablename = 'bearer_tokens'
    `);
    
    console.log('\n✅ Table indexes:');
    indexInfo.rows.forEach(idx => {
      console.log(`   - ${idx.indexname}`);
    });
    
    console.log('\n🎉 BEARER TOKENS TABLE SETUP COMPLETED!');
    console.log('✅ Table created and tested successfully');
    console.log('✅ Ready for bearer token management');
    console.log('✅ Frontend can now use the API endpoints');
    
    return true;

  } catch (error) {
    console.error('\n❌ BEARER TOKENS TABLE SETUP FAILED!');
    console.error('💥 Error:', error.message);
    console.error('🔧 Check database connection and permissions');
    
    if (error.code) {
      console.error(`   Error code: ${error.code}`);
    }
    
    if (error.detail) {
      console.error(`   Error detail: ${error.detail}`);
    }
    
    return false;
  }
}

// Run the setup
setupBearerTokensTable()
  .then(success => {
    if (success) {
      console.log('\n✅ Bearer tokens table setup COMPLETED!');
      process.exit(0);
    } else {
      console.log('\n❌ Bearer tokens table setup FAILED!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\n💥 Setup crashed:', error);
    process.exit(1);
  });
