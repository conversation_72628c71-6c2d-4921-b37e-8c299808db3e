# SWSG Store Duplicate Item Fix - Implementation Summary

## 🔍 Root Cause Analysis

The duplicate item issue in the SWSG store scraper was caused by several interconnected problems:

### 1. **Index-Based SKU Generation**
- **Problem**: SKUs were generated using `SWSG-${model || brand}-${index}` where `index` was the position in the current scraping session
- **Impact**: Same products from different scraping runs got different SKUs, causing duplicates
- **Example**: Same product could get `SWSG-Samsung-0` in one run and `SWSG-Samsung-5` in another

### 2. **Multiple Category/Subcategory Scraping**
- **Problem**: Products appearing in both main categories and subcategories were scraped multiple times
- **Impact**: Same product extracted multiple times within a single scraping session
- **Example**: A TV appearing in both "TVs and Audios" and "TVs" subcategory

### 3. **Non-Unique External IDs**
- **Problem**: External IDs were generated as `swsg-${sku}`, inheriting the SKU uniqueness problem
- **Impact**: Database deduplication failed because external IDs weren't truly unique

### 4. **Lack of Session-Level Deduplication**
- **Problem**: No mechanism to prevent processing the same product multiple times within one scraping session
- **Impact**: Immediate duplicates created during scraping

## 🔧 Implemented Solutions

### 1. **Unique Product ID Extraction**
```javascript
function extractUniqueProductId(element, productName, productUrl) {
  // Priority 1: Extract from URL (most reliable)
  // Priority 2: Extract from data-item JSON
  // Priority 3: Generate from product name hash
  // Priority 4: Timestamp-based fallback
}
```

**Benefits**:
- Consistent IDs across scraping sessions
- URL-based IDs are stable and unique
- Fallback mechanisms ensure all products get unique IDs

### 2. **Improved SKU Generation**
```javascript
const uniqueProductId = extractUniqueProductId(element, productName, productUrl);
const sku = `SWSG-${model || brand || 'PRODUCT'}-${uniqueProductId.replace('swsg-', '')}`;
const externalId = uniqueProductId;
```

**Benefits**:
- SKUs now based on stable product identifiers
- External IDs are truly unique
- Maintains SWSG branding in SKU format

### 3. **Session-Level Deduplication**
```javascript
async function extractProductsFromPage(html, categoryName, pageUrl, seenProductIds = new Set()) {
  // Check for duplicates using external ID
  if (seenProductIds.has(product.externalId)) {
    duplicatesSkipped++;
    continue;
  }
  seenProductIds.add(product.externalId);
}
```

**Benefits**:
- Prevents immediate duplicates within same scraping session
- Tracks duplicates across categories and subcategories
- Provides visibility into duplicate detection

### 4. **Sequential Category Processing**
```javascript
// Changed from parallel to sequential processing
const globalSeenProductIds = new Set();
for (const category of SWSG_CONFIG.categories) {
  const categoryProducts = await scrapeCategoryProducts(category, globalSeenProductIds);
}
```

**Benefits**:
- Maintains global deduplication state
- Prevents race conditions in duplicate detection
- Better error handling and logging

## 🗑️ Database Cleanup Solution

Created `fix-swsg-duplicates.js` script that:

1. **Identifies Duplicates by Name**: Finds products with identical names
2. **Removes Older Duplicates**: Keeps the most recent version (by updated_at)
3. **Fixes SKU Conflicts**: Updates duplicate SKUs with unique suffixes
4. **Fixes External ID Conflicts**: Updates duplicate external IDs
5. **Provides Detailed Reporting**: Shows what was cleaned up

## 🧪 Testing and Verification

Created `test-swsg-scraper-fixes.js` script that:

1. **Pre-scraping Analysis**: Checks existing duplicate state
2. **Limited Scraping Test**: Tests one category to verify fixes
3. **Post-scraping Verification**: Confirms no new duplicates created
4. **Pattern Analysis**: Verifies new SKU/external ID patterns
5. **Stock Status Check**: Ensures SWSG "In Stock" requirement maintained

## 📊 Expected Results

### Before Fix:
- Multiple products with same name but different SKUs
- SKUs like `SWSG-Samsung-0`, `SWSG-Samsung-1` for same product
- External IDs not truly unique
- Products appearing multiple times in database

### After Fix:
- Unique products only (no name duplicates)
- SKUs like `SWSG-Samsung-product-name-123` (stable across runs)
- External IDs like `swsg-product-name-123` (truly unique)
- Session-level deduplication prevents immediate duplicates

## 🚀 Implementation Steps

### Step 1: Clean Existing Duplicates
```bash
node fix-swsg-duplicates.js
```

### Step 2: Test the Fixes
```bash
node test-swsg-scraper-fixes.js
```

### Step 3: Run Full Scraping (if tests pass)
```bash
node swsg-scraper.js
```

### Step 4: Verify Results
- Check product catalog for duplicates
- Verify all products show "In Stock"
- Confirm SKU patterns are consistent

## 🔒 Safeguards Implemented

1. **Validation Errors Tracking**: Products with issues are flagged but not rejected
2. **Fallback Mechanisms**: Multiple strategies for ID generation
3. **Error Handling**: Graceful handling of parsing failures
4. **Logging**: Detailed logs for debugging and monitoring
5. **SWSG Store Overrides**: Maintains forced "In Stock" status

## 🎯 Key Benefits

1. **Eliminates Duplicates**: Root cause fixed, not just symptoms
2. **Maintains SWSG Requirements**: "In Stock" status and price logic preserved
3. **Stable Across Runs**: Same products get same IDs consistently
4. **Performance Improvement**: Fewer database operations due to deduplication
5. **Better Data Quality**: Unique, identifiable products

## 🔮 Future Considerations

1. **Monitor for Edge Cases**: Watch for products that might still cause issues
2. **URL Pattern Changes**: If SWSG changes URL structure, update extraction logic
3. **Performance Optimization**: Consider caching for large-scale scraping
4. **Database Constraints**: Consider adding unique constraints to prevent future duplicates

This comprehensive fix addresses the root causes of the duplicate issue while maintaining all existing SWSG store functionality and requirements.
