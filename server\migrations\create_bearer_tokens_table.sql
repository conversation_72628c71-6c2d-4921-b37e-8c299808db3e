-- Create bearer_tokens table for manual token management
-- This table stores bearer tokens for different stores that require authentication

CREATE TABLE IF NOT EXISTS bearer_tokens (
    id SERIAL PRIMARY KEY,
    store VARCHAR(50) UNIQUE NOT NULL,
    token TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster lookups by store
CREATE INDEX IF NOT EXISTS idx_bearer_tokens_store ON bearer_tokens(store);

-- Add comments for documentation
COMMENT ON TABLE bearer_tokens IS 'Stores bearer tokens for API authentication with different stores';
COMMENT ON COLUMN bearer_tokens.store IS 'Store identifier (e.g., blackbox, alkhunaizan)';
COMMENT ON COLUMN bearer_tokens.token IS 'JWT bearer token for API authentication';
COMMENT ON COLUMN bearer_tokens.created_at IS 'When the token was first added';
COMMENT ON COLUMN bearer_tokens.updated_at IS 'When the token was last updated';

-- Insert some example data (optional - remove in production)
-- INSERT INTO bearer_tokens (store, token) VALUES 
-- ('blackbox', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.example.token')
-- ON CONFLICT (store) DO NOTHING;
