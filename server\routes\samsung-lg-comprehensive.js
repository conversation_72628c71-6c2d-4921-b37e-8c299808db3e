// Comprehensive Samsung and LG API - All Products from All Stores with Full Details (No Quantity)
import express from 'express';
import { z } from 'zod';
import { storage } from '../storage.js';
import { isAuthenticated } from '../middleware/auth.cjs';

const router = express.Router();

// Test endpoint to verify route registration
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: 'Samsung/LG Comprehensive API is working!',
    timestamp: new Date().toISOString(),
    availableEndpoints: [
      '/api/samsung-lg/test',
      '/api/samsung-lg/comprehensive',
      '/api/samsung-lg/all-with-history',
      '/api/samsung-lg/samsung',
      '/api/samsung-lg/lg',
      '/api/samsung-lg/appliances',
      '/api/samsung-lg/docs'
    ]
  });
});

// Validation schemas
const querySchema = z.object({
  page: z.string().optional().default('1'),
  limit: z.string().optional().default('100'),
  brand: z.enum(['all', 'Samsung', 'LG']).optional().default('all'),
  category: z.string().optional().default('all'),
  store: z.string().optional().default('all'),
  minPrice: z.string().optional(),
  maxPrice: z.string().optional(),
  search: z.string().optional(),
  sortBy: z.enum(['name', 'price', 'brand', 'store', 'updated']).optional().default('name'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('asc'),
  includeOutOfStock: z.string().optional().default('true')
});

// Valid categories for Samsung/LG products
const validCategories = [
  'all', 'refrigerator', 'washing machine', 'dryer', 'dishwasher',
  'microwave', 'oven', 'air conditioner', 'tv', 'monitor', 'freezer',
  'kitchen appliances', 'home appliances', 'electronics'
];

/**
 * Get price history for a product (day by day)
 */
async function getPriceHistory(productId, days = 30) {
  try {
    const priceHistoryQuery = `
      SELECT
        price,
        regular_price,
        currency,
        change_type,
        change_amount,
        change_percentage,
        previous_price,
        recorded_at,
        DATE(recorded_at) as date
      FROM price_history
      WHERE product_id = $1
      AND recorded_at >= NOW() - INTERVAL '${days} days'
      ORDER BY recorded_at ASC
    `;

    const result = await storage.executeQuery(priceHistoryQuery, [productId]);

    // Group by date to get daily prices (in case multiple entries per day)
    const dailyPrices = {};

    result.rows.forEach(entry => {
      const date = entry.date.toISOString().split('T')[0]; // YYYY-MM-DD format

      if (!dailyPrices[date]) {
        dailyPrices[date] = {
          date: date,
          price: parseFloat(entry.price || 0),
          regularPrice: parseFloat(entry.regular_price || 0),
          currency: entry.currency || 'SAR',
          changeType: entry.change_type,
          changeAmount: parseFloat(entry.change_amount || 0),
          changePercentage: parseFloat(entry.change_percentage || 0),
          previousPrice: parseFloat(entry.previous_price || 0),
          timestamp: entry.recorded_at
        };
      } else {
        // If multiple entries for same day, keep the latest one
        if (new Date(entry.recorded_at) > new Date(dailyPrices[date].timestamp)) {
          dailyPrices[date] = {
            date: date,
            price: parseFloat(entry.price || 0),
            regularPrice: parseFloat(entry.regular_price || 0),
            currency: entry.currency || 'SAR',
            changeType: entry.change_type,
            changeAmount: parseFloat(entry.change_amount || 0),
            changePercentage: parseFloat(entry.change_percentage || 0),
            previousPrice: parseFloat(entry.previous_price || 0),
            timestamp: entry.recorded_at
          };
        }
      }
    });

    // Convert to array and sort by date
    return Object.values(dailyPrices).sort((a, b) => new Date(a.date) - new Date(b.date));

  } catch (error) {
    console.log(`⚠️ Error getting price history for product ${productId}:`, error.message);
    return [];
  }
}

/**
 * Extract model number from product name using regex patterns
 */
function extractModelNumber(productName, brand) {
  if (!productName) return null;
  
  const name = productName.toUpperCase();
  
  // Samsung model patterns
  if (brand.toLowerCase().includes('samsung')) {
    // Samsung refrigerator patterns: RT18M6213SG, RF23M8070SR, etc.
    const samsungPatterns = [
      /\b(RT\d{2}[A-Z]\d{4}[A-Z]{2})\b/,
      /\b(RF\d{2}[A-Z]\d{4}[A-Z]{2})\b/,
      /\b(WA\d{2}[A-Z]\d{4}[A-Z]{2})\b/,
      /\b(WF\d{2}[A-Z]\d{4}[A-Z]{2})\b/,
      /\b([A-Z]{2}\d{2}[A-Z]\d{4}[A-Z]{0,2})\b/,
      /\b(UN\d{2}[A-Z]\d{4}[A-Z]{2})\b/,
      /\b([A-Z]{1,3}\d{2,4}[A-Z]{1,4})\b/
    ];
    
    for (const pattern of samsungPatterns) {
      const match = name.match(pattern);
      if (match) return match[1];
    }
  }
  
  // LG model patterns
  if (brand.toLowerCase().includes('lg')) {
    // LG patterns: LT18NBBSIV, 55UT73006LA, etc.
    const lgPatterns = [
      /\b(LT\d{2}[A-Z]{5,6})\b/,
      /\b(\d{2}[A-Z]{2}\d{5}[A-Z]{2})\b/,
      /\b(F\d{2}[A-Z]\d{4}[A-Z]{2})\b/,
      /\b(WM\d{4}[A-Z]{2,4})\b/,
      /\b([A-Z]{1,3}\d{2,4}[A-Z]{2,6})\b/
    ];
    
    for (const pattern of lgPatterns) {
      const match = name.match(pattern);
      if (match) return match[1];
    }
  }
  
  // Generic model patterns
  const genericPatterns = [
    /\bMODEL[:\s]+([A-Z0-9\-\.]+)\b/,
    /\b([A-Z]{2,4}\d{2,6}[A-Z]{0,4})\b/,
    /\b(\d{2,4}[A-Z]{2,6}\d{0,4}[A-Z]{0,2})\b/
  ];
  
  for (const pattern of genericPatterns) {
    const match = name.match(pattern);
    if (match) return match[1];
  }
  
  return null;
}

/**
 * Comprehensive Samsung and LG Products API
 * Returns all Samsung and LG products from all stores with full details (excluding quantity)
 */
router.get('/comprehensive', isAuthenticated, async (req, res) => {
  try {
    console.log('🔍 [Samsung/LG Comprehensive] API called with query:', req.query);
    
    // Validate query parameters
    const validation = querySchema.safeParse(req.query);
    if (!validation.success) {
      console.error('❌ [Samsung/LG Comprehensive] Validation failed:', validation.error);
      return res.status(400).json({
        success: false,
        message: 'Invalid query parameters',
        errors: validation.error.errors
      });
    }
    
    const {
      page,
      limit,
      brand,
      category,
      store,
      minPrice,
      maxPrice,
      search,
      sortBy,
      sortOrder,
      includeOutOfStock
    } = validation.data;
    
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const offset = (pageNum - 1) * limitNum;
    
    // Build WHERE clause
    let whereConditions = [];
    let queryParams = [];
    let paramIndex = 1;
    
    // Brand filter (Samsung and/or LG)
    if (brand === 'Samsung') {
      whereConditions.push(`LOWER(p.brand) ILIKE $${paramIndex}`);
      queryParams.push('%samsung%');
      paramIndex++;
    } else if (brand === 'LG') {
      whereConditions.push(`LOWER(p.brand) ILIKE $${paramIndex}`);
      queryParams.push('%lg%');
      paramIndex++;
    } else {
      // Default: both Samsung and LG
      whereConditions.push(`(LOWER(p.brand) ILIKE $${paramIndex} OR LOWER(p.brand) ILIKE $${paramIndex + 1})`);
      queryParams.push('%samsung%', '%lg%');
      paramIndex += 2;
    }
    
    // Category filter
    if (category !== 'all') {
      whereConditions.push(`LOWER(p.category) ILIKE $${paramIndex}`);
      queryParams.push(`%${category.toLowerCase()}%`);
      paramIndex++;
    }
    
    // Store filter
    if (store !== 'all') {
      whereConditions.push(`LOWER(s.name) ILIKE $${paramIndex}`);
      queryParams.push(`%${store.toLowerCase()}%`);
      paramIndex++;
    }
    
    // Price range filter
    if (minPrice) {
      whereConditions.push(`p.price >= $${paramIndex}`);
      queryParams.push(parseFloat(minPrice));
      paramIndex++;
    }
    
    if (maxPrice) {
      whereConditions.push(`p.price <= $${paramIndex}`);
      queryParams.push(parseFloat(maxPrice));
      paramIndex++;
    }
    
    // Search filter
    if (search) {
      whereConditions.push(`(LOWER(p.name) ILIKE $${paramIndex} OR LOWER(p.brand) ILIKE $${paramIndex} OR LOWER(p.sku) ILIKE $${paramIndex})`);
      queryParams.push(`%${search.toLowerCase()}%`);
      paramIndex++;
    }
    
    // Stock filter
    if (includeOutOfStock === 'false') {
      whereConditions.push(`p.stock > 0`);
    }
    
    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
    
    // Build ORDER BY clause
    let orderByClause = '';
    switch (sortBy) {
      case 'price':
        orderByClause = `ORDER BY p.price ${sortOrder.toUpperCase()}`;
        break;
      case 'brand':
        orderByClause = `ORDER BY p.brand ${sortOrder.toUpperCase()}, p.name ASC`;
        break;
      case 'store':
        orderByClause = `ORDER BY s.name ${sortOrder.toUpperCase()}, p.name ASC`;
        break;
      case 'updated':
        orderByClause = `ORDER BY p.updated_at ${sortOrder.toUpperCase()}`;
        break;
      default:
        orderByClause = `ORDER BY p.name ${sortOrder.toUpperCase()}`;
    }
    
    // Get total count
    const countQuery = `
      SELECT COUNT(DISTINCT p.id) as total
      FROM products p
      LEFT JOIN stores s ON p.store_id = s.id
      ${whereClause}
    `;
    
    const countResult = await storage.executeQuery(countQuery, queryParams);
    const total = parseInt(countResult.rows[0]?.total || 0);
    
    // Get products with full details (excluding quantity/stock information)
    const productsQuery = `
      SELECT DISTINCT
        p.id,
        p.name,
        p.brand,
        p.category,
        p.sku,
        p.external_id,
        p.price,
        p.regular_price,
        p.currency,
        p.description,
        p.image_url,
        p.product_url,
        p.created_at,
        p.updated_at,
        s.name as store_name,
        s.slug as store_slug,
        s.logo_url as store_logo,
        c.name as category_name,
        c.slug as category_slug
      FROM products p
      LEFT JOIN stores s ON p.store_id = s.id
      LEFT JOIN categories c ON p.category_id = c.id
      ${whereClause}
      ${orderByClause}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    
    const finalParams = [...queryParams, limitNum, offset];
    const productsResult = await storage.executeQuery(productsQuery, finalParams);
    
    // Process products and extract model numbers with price history
    console.log('📊 Processing products and fetching price history...');
    const products = await Promise.all(productsResult.rows.map(async (product) => {
      const modelNumber = extractModelNumber(product.name, product.brand);

      // Get price history for this product (last 30 days by default)
      const priceHistoryDays = req.query.priceHistoryDays ? parseInt(req.query.priceHistoryDays) : 30;
      const priceHistory = await getPriceHistory(product.id, priceHistoryDays);

      // Calculate price analytics from history
      const prices = priceHistory.map(h => h.price).filter(p => p > 0);
      const priceAnalytics = prices.length > 0 ? {
        lowest: Math.min(...prices),
        highest: Math.max(...prices),
        average: parseFloat((prices.reduce((a, b) => a + b, 0) / prices.length).toFixed(2)),
        trend: priceHistory.length >= 2 ?
          (priceHistory[priceHistory.length - 1].price > priceHistory[0].price ? 'increasing' :
           priceHistory[priceHistory.length - 1].price < priceHistory[0].price ? 'decreasing' : 'stable') : 'stable',
        volatility: prices.length > 1 ?
          parseFloat((Math.max(...prices) - Math.min(...prices)).toFixed(2)) : 0
      } : null;

      return {
        id: product.id,
        name: product.name,
        brand: product.brand,
        category: product.category,
        categoryName: product.category_name,
        categorySlug: product.category_slug,
        sku: product.sku,
        externalId: product.external_id,
        modelNumber: modelNumber,
        price: {
          current: parseFloat(product.price || 0),
          regular: parseFloat(product.regular_price || 0),
          currency: product.currency || 'SAR',
          discount: product.regular_price && product.price ?
            Math.round(((product.regular_price - product.price) / product.regular_price) * 100) : 0
        },
        priceHistory: priceHistory,
        priceAnalytics: priceAnalytics,
        description: product.description,
        images: {
          main: product.image_url,
          thumbnail: product.image_url
        },
        urls: {
          product: product.product_url,
          store: product.product_url
        },
        store: {
          name: product.store_name,
          slug: product.store_slug,
          logo: product.store_logo
        },
        timestamps: {
          created: product.created_at,
          updated: product.updated_at
        }
        // Note: Quantity/stock information excluded as requested
      };
    }));
    
    // Calculate pagination
    const totalPages = Math.ceil(total / limitNum);
    const hasNextPage = pageNum < totalPages;
    const hasPrevPage = pageNum > 1;
    
    // Get summary statistics
    const statsQuery = `
      SELECT 
        COUNT(DISTINCT p.id) as total_products,
        COUNT(DISTINCT p.brand) as total_brands,
        COUNT(DISTINCT s.name) as total_stores,
        COUNT(DISTINCT p.category) as total_categories,
        AVG(p.price) as avg_price,
        MIN(p.price) as min_price,
        MAX(p.price) as max_price,
        COUNT(CASE WHEN p.brand ILIKE '%samsung%' THEN 1 END) as samsung_count,
        COUNT(CASE WHEN p.brand ILIKE '%lg%' THEN 1 END) as lg_count
      FROM products p
      LEFT JOIN stores s ON p.store_id = s.id
      ${whereClause}
    `;
    
    const statsResult = await storage.executeQuery(statsQuery, queryParams);
    const stats = statsResult.rows[0];
    
    // Get brand breakdown
    const brandBreakdownQuery = `
      SELECT 
        p.brand,
        COUNT(*) as product_count,
        AVG(p.price) as avg_price,
        COUNT(DISTINCT s.name) as store_count
      FROM products p
      LEFT JOIN stores s ON p.store_id = s.id
      ${whereClause}
      GROUP BY p.brand
      ORDER BY product_count DESC
    `;
    
    const brandBreakdownResult = await storage.executeQuery(brandBreakdownQuery, queryParams);
    
    // Get store breakdown
    const storeBreakdownQuery = `
      SELECT 
        s.name as store_name,
        s.slug as store_slug,
        COUNT(*) as product_count,
        AVG(p.price) as avg_price,
        COUNT(CASE WHEN p.brand ILIKE '%samsung%' THEN 1 END) as samsung_count,
        COUNT(CASE WHEN p.brand ILIKE '%lg%' THEN 1 END) as lg_count
      FROM products p
      LEFT JOIN stores s ON p.store_id = s.id
      ${whereClause}
      GROUP BY s.id, s.name, s.slug
      ORDER BY product_count DESC
    `;
    
    const storeBreakdownResult = await storage.executeQuery(storeBreakdownQuery, queryParams);
    
    console.log(`✅ [Samsung/LG Comprehensive] Retrieved ${products.length} products (page ${pageNum}/${totalPages})`);
    
    // Return comprehensive response
    res.json({
      success: true,
      data: {
        products: products,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: total,
          totalPages: totalPages,
          hasNextPage: hasNextPage,
          hasPrevPage: hasPrevPage
        },
        summary: {
          totalProducts: parseInt(stats.total_products || 0),
          totalBrands: parseInt(stats.total_brands || 0),
          totalStores: parseInt(stats.total_stores || 0),
          totalCategories: parseInt(stats.total_categories || 0),
          priceAnalytics: {
            average: parseFloat(stats.avg_price || 0),
            minimum: parseFloat(stats.min_price || 0),
            maximum: parseFloat(stats.max_price || 0),
            currency: 'SAR'
          },
          brandCounts: {
            samsung: parseInt(stats.samsung_count || 0),
            lg: parseInt(stats.lg_count || 0)
          }
        },
        breakdown: {
          byBrand: brandBreakdownResult.rows.map(row => ({
            brand: row.brand,
            productCount: parseInt(row.product_count),
            averagePrice: parseFloat(row.avg_price || 0),
            storeCount: parseInt(row.store_count)
          })),
          byStore: storeBreakdownResult.rows.map(row => ({
            storeName: row.store_name,
            storeSlug: row.store_slug,
            productCount: parseInt(row.product_count),
            averagePrice: parseFloat(row.avg_price || 0),
            samsungCount: parseInt(row.samsung_count || 0),
            lgCount: parseInt(row.lg_count || 0)
          }))
        },
        filters: {
          applied: {
            brand: brand,
            category: category,
            store: store,
            minPrice: minPrice,
            maxPrice: maxPrice,
            search: search,
            includeOutOfStock: includeOutOfStock
          },
          available: {
            brands: ['all', 'Samsung', 'LG'],
            categories: validCategories,
            sortOptions: ['name', 'price', 'brand', 'store', 'updated'],
            sortOrders: ['asc', 'desc']
          }
        }
      },
      meta: {
        timestamp: new Date().toISOString(),
        requestId: `samsung-lg-comprehensive-${Date.now()}`,
        apiVersion: '2.0',
        note: 'Quantity/stock information excluded as requested'
      }
    });
    
  } catch (error) {
    console.error('❌ [Samsung/LG Comprehensive] Error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

/**
 * Get all Samsung products from all stores
 */
router.get('/samsung', isAuthenticated, async (req, res) => {
  try {
    // Redirect to comprehensive endpoint with Samsung filter
    req.query.brand = 'Samsung';
    return router.handle(req, res, () => {
      req.url = '/comprehensive';
      router.handle(req, res);
    });
  } catch (error) {
    console.error('❌ [Samsung Only] Error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

/**
 * Get all LG products from all stores
 */
router.get('/lg', isAuthenticated, async (req, res) => {
  try {
    // Redirect to comprehensive endpoint with LG filter
    req.query.brand = 'LG';
    return router.handle(req, res, () => {
      req.url = '/comprehensive';
      router.handle(req, res);
    });
  } catch (error) {
    console.error('❌ [LG Only] Error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

/**
 * Get Samsung and LG appliances only (refrigerators and washing machines)
 */
router.get('/appliances', isAuthenticated, async (req, res) => {
  try {
    console.log('🔍 [Samsung/LG Appliances Only] API called');

    const appliancesQuery = `
      SELECT DISTINCT
        p.id,
        p.name,
        p.brand,
        p.category,
        p.sku,
        p.external_id,
        p.price,
        p.regular_price,
        p.currency,
        p.description,
        p.image_url,
        p.product_url,
        p.created_at,
        p.updated_at,
        s.name as store_name,
        s.slug as store_slug,
        s.logo_url as store_logo
      FROM products p
      LEFT JOIN stores s ON p.store_id = s.id
      WHERE (LOWER(p.brand) ILIKE '%samsung%' OR LOWER(p.brand) ILIKE '%lg%')
      AND (
        LOWER(p.name) ILIKE '%refrigerator%' OR
        LOWER(p.name) ILIKE '%fridge%' OR
        LOWER(p.name) ILIKE '%freezer%' OR
        LOWER(p.name) ILIKE '%washing machine%' OR
        LOWER(p.name) ILIKE '%washer%' OR
        LOWER(p.category) ILIKE '%refrigerator%' OR
        LOWER(p.category) ILIKE '%washing machine%'
      )
      ORDER BY p.brand, p.name
    `;

    const result = await storage.executeQuery(appliancesQuery);

    const appliances = result.rows.map(product => {
      const modelNumber = extractModelNumber(product.name, product.brand);

      return {
        id: product.id,
        name: product.name,
        brand: product.brand,
        category: product.category,
        sku: product.sku,
        externalId: product.external_id,
        modelNumber: modelNumber,
        price: {
          current: parseFloat(product.price || 0),
          regular: parseFloat(product.regular_price || 0),
          currency: product.currency || 'SAR',
          discount: product.regular_price && product.price ?
            Math.round(((product.regular_price - product.price) / product.price) * 100) : 0
        },
        description: product.description,
        images: {
          main: product.image_url,
          thumbnail: product.image_url
        },
        urls: {
          product: product.product_url,
          store: product.product_url
        },
        store: {
          name: product.store_name,
          slug: product.store_slug,
          logo: product.store_logo
        },
        timestamps: {
          created: product.created_at,
          updated: product.updated_at
        }
      };
    });

    res.json({
      success: true,
      data: {
        appliances: appliances,
        summary: {
          totalAppliances: appliances.length,
          samsungCount: appliances.filter(a => a.brand.toLowerCase().includes('samsung')).length,
          lgCount: appliances.filter(a => a.brand.toLowerCase().includes('lg')).length,
          storeCount: [...new Set(appliances.map(a => a.store.name))].length
        }
      },
      meta: {
        timestamp: new Date().toISOString(),
        requestId: `samsung-lg-appliances-${Date.now()}`,
        note: 'Refrigerators and washing machines only, no quantity information'
      }
    });

  } catch (error) {
    console.error('❌ [Samsung/LG Appliances Only] Error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

/**
 * Get ALL Samsung and LG products in one page (no pagination, no quantity)
 * This endpoint returns every Samsung and LG product from all stores
 */
router.get('/all', isAuthenticated, async (req, res) => {
  try {
    console.log('🔍 [Samsung/LG All Products] API called - fetching ALL products without pagination');

    const startTime = Date.now();

    // Get ALL Samsung and LG products with full details (no pagination, no quantity)
    const allProductsQuery = `
      SELECT DISTINCT
        p.id,
        p.name,
        p.brand,
        p.category,
        p.sku,
        p.external_id,
        p.price,
        p.regular_price,
        p.currency,
        p.description,
        p.image_url,
        p.product_url,
        p.created_at,
        p.updated_at,
        s.name as store_name,
        s.slug as store_slug,
        s.logo_url as store_logo,
        c.name as category_name,
        c.slug as category_slug
      FROM products p
      LEFT JOIN stores s ON p.store_id = s.id
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE (LOWER(p.brand) ILIKE '%samsung%' OR LOWER(p.brand) ILIKE '%lg%')
      ORDER BY p.brand ASC, p.name ASC
    `;

    console.log('📊 Executing query to fetch ALL Samsung and LG products...');
    const productsResult = await storage.executeQuery(allProductsQuery);

    const queryTime = Date.now() - startTime;
    console.log(`✅ Query completed in ${queryTime}ms, found ${productsResult.rows.length} products`);

    // Process all products and extract model numbers
    const allProducts = productsResult.rows.map(product => {
      const modelNumber = extractModelNumber(product.name, product.brand);

      return {
        id: product.id,
        name: product.name,
        brand: product.brand,
        category: product.category,
        categoryName: product.category_name,
        categorySlug: product.category_slug,
        sku: product.sku,
        externalId: product.external_id,
        modelNumber: modelNumber,
        price: {
          current: parseFloat(product.price || 0),
          regular: parseFloat(product.regular_price || 0),
          currency: product.currency || 'SAR',
          discount: product.regular_price && product.price ?
            Math.round(((product.regular_price - product.price) / product.regular_price) * 100) : 0
        },
        description: product.description,
        images: {
          main: product.image_url,
          thumbnail: product.image_url
        },
        urls: {
          product: product.product_url,
          store: product.product_url
        },
        store: {
          name: product.store_name,
          slug: product.store_slug,
          logo: product.store_logo
        },
        timestamps: {
          created: product.created_at,
          updated: product.updated_at
        }
        // Note: NO quantity/stock information included
      };
    });

    // Calculate comprehensive statistics
    const totalProducts = allProducts.length;
    const samsungProducts = allProducts.filter(p => p.brand.toLowerCase().includes('samsung'));
    const lgProducts = allProducts.filter(p => p.brand.toLowerCase().includes('lg'));

    // Store breakdown
    const storeBreakdown = {};
    allProducts.forEach(product => {
      const storeName = product.store.name;
      if (!storeBreakdown[storeName]) {
        storeBreakdown[storeName] = {
          storeName: storeName,
          storeSlug: product.store.slug,
          totalProducts: 0,
          samsungCount: 0,
          lgCount: 0,
          averagePrice: 0,
          totalPrice: 0
        };
      }

      storeBreakdown[storeName].totalProducts++;
      storeBreakdown[storeName].totalPrice += product.price.current;

      if (product.brand.toLowerCase().includes('samsung')) {
        storeBreakdown[storeName].samsungCount++;
      } else if (product.brand.toLowerCase().includes('lg')) {
        storeBreakdown[storeName].lgCount++;
      }
    });

    // Calculate average prices for stores
    Object.values(storeBreakdown).forEach(store => {
      store.averagePrice = store.totalProducts > 0 ?
        parseFloat((store.totalPrice / store.totalProducts).toFixed(2)) : 0;
      delete store.totalPrice; // Remove temporary field
    });

    // Category breakdown
    const categoryBreakdown = {};
    allProducts.forEach(product => {
      const category = product.category || 'Uncategorized';
      if (!categoryBreakdown[category]) {
        categoryBreakdown[category] = {
          categoryName: category,
          totalProducts: 0,
          samsungCount: 0,
          lgCount: 0,
          averagePrice: 0,
          totalPrice: 0
        };
      }

      categoryBreakdown[category].totalProducts++;
      categoryBreakdown[category].totalPrice += product.price.current;

      if (product.brand.toLowerCase().includes('samsung')) {
        categoryBreakdown[category].samsungCount++;
      } else if (product.brand.toLowerCase().includes('lg')) {
        categoryBreakdown[category].lgCount++;
      }
    });

    // Calculate average prices for categories
    Object.values(categoryBreakdown).forEach(category => {
      category.averagePrice = category.totalProducts > 0 ?
        parseFloat((category.totalPrice / category.totalProducts).toFixed(2)) : 0;
      delete category.totalPrice; // Remove temporary field
    });

    // Price analytics
    const prices = allProducts.map(p => p.price.current).filter(price => price > 0);
    const priceAnalytics = {
      average: prices.length > 0 ? parseFloat((prices.reduce((a, b) => a + b, 0) / prices.length).toFixed(2)) : 0,
      minimum: prices.length > 0 ? Math.min(...prices) : 0,
      maximum: prices.length > 0 ? Math.max(...prices) : 0,
      currency: 'SAR'
    };

    const processingTime = Date.now() - startTime;

    console.log(`✅ [Samsung/LG All Products] Processed ${totalProducts} products in ${processingTime}ms`);
    console.log(`📊 Samsung: ${samsungProducts.length}, LG: ${lgProducts.length}`);
    console.log(`🏪 Stores: ${Object.keys(storeBreakdown).length}`);
    console.log(`📂 Categories: ${Object.keys(categoryBreakdown).length}`);

    // Return ALL products in one response (no pagination)
    res.json({
      success: true,
      data: {
        products: allProducts,
        summary: {
          totalProducts: totalProducts,
          samsungCount: samsungProducts.length,
          lgCount: lgProducts.length,
          totalStores: Object.keys(storeBreakdown).length,
          totalCategories: Object.keys(categoryBreakdown).length,
          priceAnalytics: priceAnalytics
        },
        breakdown: {
          byStore: Object.values(storeBreakdown).sort((a, b) => b.totalProducts - a.totalProducts),
          byCategory: Object.values(categoryBreakdown).sort((a, b) => b.totalProducts - a.totalProducts)
        },
        performance: {
          queryTime: `${queryTime}ms`,
          processingTime: `${processingTime}ms`,
          totalTime: `${processingTime}ms`
        }
      },
      meta: {
        timestamp: new Date().toISOString(),
        requestId: `samsung-lg-all-${Date.now()}`,
        apiVersion: '2.0',
        note: 'ALL Samsung and LG products in one response - no pagination, no quantity information',
        disclaimer: 'This endpoint returns ALL products and may be large. Use with caution for performance.'
      }
    });

  } catch (error) {
    console.error('❌ [Samsung/LG All Products] Error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

/**
 * Get ALL Samsung and LG products in one page with price history (no pagination, no quantity)
 * This endpoint returns every Samsung and LG product from all stores with day-by-day price history
 */
router.get('/all-with-history', isAuthenticated, async (req, res) => {
  try {
    console.log('🔍 [Samsung/LG All Products with Price History] API called - fetching ALL products with price history');

    const startTime = Date.now();

    // Get price history days from query (default 30 days)
    const priceHistoryDays = req.query.priceHistoryDays ? parseInt(req.query.priceHistoryDays) : 30;
    console.log(`📊 Including price history for last ${priceHistoryDays} days`);

    // Get ALL Samsung and LG products with full details (no pagination, no quantity)
    const allProductsQuery = `
      SELECT DISTINCT
        p.id,
        p.name,
        p.brand,
        p.category,
        p.sku,
        p.external_id,
        p.price,
        p.regular_price,
        p.currency,
        p.description,
        p.image_url,
        p.product_url,
        p.created_at,
        p.updated_at,
        s.name as store_name,
        s.slug as store_slug,
        s.logo_url as store_logo,
        c.name as category_name,
        c.slug as category_slug
      FROM products p
      LEFT JOIN stores s ON p.store_id = s.id
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE (LOWER(p.brand) ILIKE '%samsung%' OR LOWER(p.brand) ILIKE '%lg%')
      ORDER BY p.brand ASC, p.name ASC
    `;

    console.log('📊 Executing query to fetch ALL Samsung and LG products...');
    const productsResult = await storage.executeQuery(allProductsQuery);

    const queryTime = Date.now() - startTime;
    console.log(`✅ Query completed in ${queryTime}ms, found ${productsResult.rows.length} products`);
    console.log('📊 Now fetching price history for each product...');

    // Process all products and get price history for each
    const allProducts = await Promise.all(productsResult.rows.map(async (product, index) => {
      if (index % 100 === 0) {
        console.log(`📊 Processing product ${index + 1}/${productsResult.rows.length}...`);
      }

      const modelNumber = extractModelNumber(product.name, product.brand);

      // Get price history for this product
      const priceHistory = await getPriceHistory(product.id, priceHistoryDays);

      // Calculate price analytics from history
      const prices = priceHistory.map(h => h.price).filter(p => p > 0);
      const priceAnalytics = prices.length > 0 ? {
        lowest: Math.min(...prices),
        highest: Math.max(...prices),
        average: parseFloat((prices.reduce((a, b) => a + b, 0) / prices.length).toFixed(2)),
        trend: priceHistory.length >= 2 ?
          (priceHistory[priceHistory.length - 1].price > priceHistory[0].price ? 'increasing' :
           priceHistory[priceHistory.length - 1].price < priceHistory[0].price ? 'decreasing' : 'stable') : 'stable',
        volatility: prices.length > 1 ?
          parseFloat((Math.max(...prices) - Math.min(...prices)).toFixed(2)) : 0,
        totalChanges: priceHistory.length,
        firstRecorded: priceHistory.length > 0 ? priceHistory[0].date : null,
        lastRecorded: priceHistory.length > 0 ? priceHistory[priceHistory.length - 1].date : null
      } : null;

      return {
        id: product.id,
        name: product.name,
        brand: product.brand,
        category: product.category,
        categoryName: product.category_name,
        categorySlug: product.category_slug,
        sku: product.sku,
        externalId: product.external_id,
        modelNumber: modelNumber,
        price: {
          current: parseFloat(product.price || 0),
          regular: parseFloat(product.regular_price || 0),
          currency: product.currency || 'SAR',
          discount: product.regular_price && product.price ?
            Math.round(((product.regular_price - product.price) / product.regular_price) * 100) : 0
        },
        priceHistory: priceHistory,
        priceAnalytics: priceAnalytics,
        description: product.description,
        images: {
          main: product.image_url,
          thumbnail: product.image_url
        },
        urls: {
          product: product.product_url,
          store: product.product_url
        },
        store: {
          name: product.store_name,
          slug: product.store_slug,
          logo: product.store_logo
        },
        timestamps: {
          created: product.created_at,
          updated: product.updated_at
        }
        // Note: NO quantity/stock information included
      };
    }));

    // Calculate comprehensive statistics
    const totalProducts = allProducts.length;
    const samsungProducts = allProducts.filter(p => p.brand.toLowerCase().includes('samsung'));
    const lgProducts = allProducts.filter(p => p.brand.toLowerCase().includes('lg'));

    // Store breakdown
    const storeBreakdown = {};
    allProducts.forEach(product => {
      const storeName = product.store.name;
      if (!storeBreakdown[storeName]) {
        storeBreakdown[storeName] = {
          storeName: storeName,
          storeSlug: product.store.slug,
          totalProducts: 0,
          samsungCount: 0,
          lgCount: 0,
          averagePrice: 0,
          totalPrice: 0,
          productsWithHistory: 0
        };
      }

      storeBreakdown[storeName].totalProducts++;
      storeBreakdown[storeName].totalPrice += product.price.current;

      if (product.priceHistory && product.priceHistory.length > 0) {
        storeBreakdown[storeName].productsWithHistory++;
      }

      if (product.brand.toLowerCase().includes('samsung')) {
        storeBreakdown[storeName].samsungCount++;
      } else if (product.brand.toLowerCase().includes('lg')) {
        storeBreakdown[storeName].lgCount++;
      }
    });

    // Calculate average prices for stores
    Object.values(storeBreakdown).forEach(store => {
      store.averagePrice = store.totalProducts > 0 ?
        parseFloat((store.totalPrice / store.totalProducts).toFixed(2)) : 0;
      delete store.totalPrice; // Remove temporary field
    });

    // Price analytics across all products
    const allPrices = allProducts.map(p => p.price.current).filter(price => price > 0);
    const priceAnalytics = {
      average: allPrices.length > 0 ? parseFloat((allPrices.reduce((a, b) => a + b, 0) / allPrices.length).toFixed(2)) : 0,
      minimum: allPrices.length > 0 ? Math.min(...allPrices) : 0,
      maximum: allPrices.length > 0 ? Math.max(...allPrices) : 0,
      currency: 'SAR'
    };

    // Price history statistics
    const productsWithHistory = allProducts.filter(p => p.priceHistory && p.priceHistory.length > 0);
    const totalPriceRecords = allProducts.reduce((sum, p) => sum + (p.priceHistory ? p.priceHistory.length : 0), 0);

    const processingTime = Date.now() - startTime;

    console.log(`✅ [Samsung/LG All Products with History] Processed ${totalProducts} products in ${processingTime}ms`);
    console.log(`📊 Samsung: ${samsungProducts.length}, LG: ${lgProducts.length}`);
    console.log(`🏪 Stores: ${Object.keys(storeBreakdown).length}`);
    console.log(`📈 Products with price history: ${productsWithHistory.length}/${totalProducts}`);
    console.log(`📊 Total price records: ${totalPriceRecords}`);

    // Return ALL products with price history in one response (no pagination)
    res.json({
      success: true,
      data: {
        products: allProducts,
        summary: {
          totalProducts: totalProducts,
          samsungCount: samsungProducts.length,
          lgCount: lgProducts.length,
          totalStores: Object.keys(storeBreakdown).length,
          priceAnalytics: priceAnalytics,
          priceHistoryStats: {
            productsWithHistory: productsWithHistory.length,
            productsWithoutHistory: totalProducts - productsWithHistory.length,
            totalPriceRecords: totalPriceRecords,
            averageRecordsPerProduct: totalProducts > 0 ?
              parseFloat((totalPriceRecords / totalProducts).toFixed(2)) : 0,
            historyPeriodDays: priceHistoryDays
          }
        },
        breakdown: {
          byStore: Object.values(storeBreakdown).sort((a, b) => b.totalProducts - a.totalProducts)
        },
        performance: {
          queryTime: `${queryTime}ms`,
          processingTime: `${processingTime}ms`,
          totalTime: `${processingTime}ms`
        }
      },
      meta: {
        timestamp: new Date().toISOString(),
        requestId: `samsung-lg-all-history-${Date.now()}`,
        apiVersion: '2.0',
        note: 'ALL Samsung and LG products with day-by-day price history - no pagination, no quantity information',
        disclaimer: 'This endpoint returns ALL products with price history and may be large. Use with caution for performance.',
        priceHistoryPeriod: `${priceHistoryDays} days`
      }
    });

  } catch (error) {
    console.error('❌ [Samsung/LG All Products with History] Error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

/**
 * Get API documentation and available endpoints
 */
router.get('/docs', (req, res) => {
  res.json({
    success: true,
    documentation: {
      title: 'Samsung/LG Comprehensive API',
      version: '2.0',
      description: 'Complete API for Samsung and LG products from all stores with full details (excluding quantity)',
      baseUrl: '/api/samsung-lg',
      endpoints: [
        {
          path: '/comprehensive',
          method: 'GET',
          description: 'Get all Samsung and LG products with comprehensive filtering and sorting',
          parameters: {
            page: 'Page number (default: 1)',
            limit: 'Products per page (default: 100)',
            brand: 'Filter by brand: all, Samsung, LG (default: all)',
            category: 'Filter by category (default: all)',
            store: 'Filter by store (default: all)',
            minPrice: 'Minimum price filter',
            maxPrice: 'Maximum price filter',
            search: 'Search in name, brand, or SKU',
            sortBy: 'Sort by: name, price, brand, store, updated (default: name)',
            sortOrder: 'Sort order: asc, desc (default: asc)',
            includeOutOfStock: 'Include out of stock products: true, false (default: true)',
            priceHistoryDays: 'Number of days for price history (default: 30)'
          }
        },
        {
          path: '/samsung',
          method: 'GET',
          description: 'Get all Samsung products from all stores',
          note: 'Same parameters as /comprehensive but filtered to Samsung only'
        },
        {
          path: '/lg',
          method: 'GET',
          description: 'Get all LG products from all stores',
          note: 'Same parameters as /comprehensive but filtered to LG only'
        },
        {
          path: '/appliances',
          method: 'GET',
          description: 'Get Samsung and LG appliances only (refrigerators and washing machines)',
          note: 'No additional parameters, returns all appliances'
        },
        {
          path: '/all-with-history',
          method: 'GET',
          description: 'Get ALL Samsung and LG products with day-by-day price history (no pagination)',
          parameters: {
            priceHistoryDays: 'Number of days for price history (default: 30)'
          },
          note: 'Returns ALL products in one response with complete price history'
        },
        {
          path: '/docs',
          method: 'GET',
          description: 'Get this API documentation',
          note: 'No authentication required'
        }
      ],
      features: [
        'All Samsung and LG products from all stores',
        'Full product details (name, brand, category, price, images, etc.)',
        'Day-by-day price history for each product',
        'Price analytics and trend analysis',
        'Model number extraction using advanced regex patterns',
        'Price volatility and change tracking',
        'Store and brand breakdown statistics',
        'Advanced filtering and sorting options',
        'Pagination support (except /all-with-history endpoint)',
        'No quantity/stock information (as requested)',
        'Comprehensive error handling',
        'Real-time data from database'
      ],
      responseFormat: {
        success: 'Boolean indicating request success',
        data: {
          products: 'Array of product objects with full details',
          pagination: 'Pagination information',
          summary: 'Summary statistics',
          breakdown: 'Breakdown by brand and store',
          filters: 'Applied and available filters'
        },
        meta: 'Request metadata including timestamp and request ID'
      },
      authentication: 'Required for all endpoints except /docs',
      rateLimit: 'Standard API rate limits apply'
    }
  });
});

export default router;
