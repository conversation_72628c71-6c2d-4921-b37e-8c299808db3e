const { Pool } = require('pg');

async function addMissingColumns() {
  console.log('🔧 Adding missing columns to price_history table...');
  
  const pool = new Pool({
    connectionString: 'postgres://postgres:<EMAIL>:5432/ir2-ksa?sslmode=no-verify',
    ssl: { rejectUnauthorized: false },
    connectionTimeoutMillis: 10000,
    idleTimeoutMillis: 10000,
    max: 1
  });

  try {
    console.log('✅ Connected to database');

    // Add missing columns one by one
    const columns = [
      { name: 'member_price', type: 'DECIMAL(10, 2)', comment: 'Historical membership/loyalty price for trend analysis' },
      { name: 'stock_status', type: 'VARCHAR(50)', comment: 'Stock availability status (in_stock, out_of_stock, etc.)' },
      { name: 'quantity_available', type: 'INTEGER', comment: 'Available quantity at time of recording' },
      { name: 'store_id', type: 'INTEGER', comment: 'Reference to the store for multi-store tracking' }
    ];

    for (const column of columns) {
      console.log(`\n🔧 Adding ${column.name} column...`);
      
      try {
        await pool.query(`
          ALTER TABLE price_history 
          ADD COLUMN IF NOT EXISTS ${column.name} ${column.type}
        `);
        console.log(`   ✅ Added ${column.name} column`);
        
        // Add comment
        await pool.query(`
          COMMENT ON COLUMN price_history.${column.name} IS '${column.comment}'
        `);
        console.log(`   ✅ Added comment for ${column.name}`);
        
      } catch (error) {
        console.log(`   ⚠️ Column ${column.name} might already exist: ${error.message}`);
      }
    }

    // Verify the columns exist
    console.log('\n🔍 Verifying columns...');
    const result = await pool.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'price_history' 
      AND column_name IN ('member_price', 'stock_status', 'quantity_available', 'store_id')
      ORDER BY column_name
    `);

    console.log('\n📊 Columns in price_history table:');
    result.rows.forEach(row => {
      console.log(`   ✅ ${row.column_name} (${row.data_type})`);
    });

    console.log('\n🎉 Migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await pool.end();
  }
}

addMissingColumns().catch(console.error);
