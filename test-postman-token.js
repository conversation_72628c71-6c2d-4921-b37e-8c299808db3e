/**
 * Test Postman Token
 * Quick test of the working token from Postman
 */

import axios from 'axios';
import https from 'https';

// Complete token from your Postman
const POSTMAN_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTYwMjU1OTQsInN0b3JlTmFtZSI6ImVuIn0.POQ0-0E9J62ZFNpoWQf5CkXjDLDGxVPc4nEbYR9WLU4';

async function testPostmanToken() {
  console.log('🧪 TESTING POSTMAN TOKEN');
  console.log('='.repeat(30));
  console.log(`Token: ${POSTMAN_TOKEN.substring(0, 50)}...`);
  console.log('Source: Postman screenshot (working request)');
  console.log('');

  // Test the exact same endpoint from Postman
  const testUrl = 'https://api.ops.blackbox.com.sa/api/v1/search/products/facets/category/175';
  
  console.log('🔍 Testing exact Postman endpoint...');
  console.log(`URL: ${testUrl}`);
  console.log('');

  try {
    const response = await axios.get(testUrl, {
      headers: {
        'Authorization': `Bearer ${POSTMAN_TOKEN}`,
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Origin': 'https://www.blackbox.com.sa',
        'Referer': 'https://www.blackbox.com.sa/',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site'
      },
      params: {
        pageNo: 0,
        pageSize: 10
      },
      httpsAgent: new https.Agent({ rejectUnauthorized: false }),
      timeout: 15000
    });
    
    const products = response.data?.products?.length || 0;
    const totalProducts = response.data?.total || 0;
    
    console.log('✅ SUCCESS! POSTMAN TOKEN WORKS!');
    console.log(`📊 Status: ${response.status}`);
    console.log(`📦 Products in response: ${products}`);
    console.log(`📈 Total available: ${totalProducts}`);
    console.log(`📄 Response size: ${JSON.stringify(response.data).length} bytes`);
    
    if (products > 0) {
      console.log('\n📦 Sample products:');
      response.data.products.slice(0, 3).forEach((product, index) => {
        console.log(`${index + 1}. ${product.name || product.title || 'Unknown'}`);
      });
    }
    
    console.log('\n🎉 POSTMAN TOKEN IS WORKING!');
    console.log('✅ Authentication successful');
    console.log('✅ API access confirmed');
    console.log('✅ Products accessible');
    console.log('✅ Ready to run Blackbox scraper');
    
    return true;
    
  } catch (error) {
    const status = error.response?.status;
    const statusText = error.response?.statusText;
    
    console.log(`❌ FAILED! Status: ${status} ${statusText}`);
    console.log(`💥 Error: ${error.message}`);
    
    if (status === 401) {
      console.log('\n🔑 AUTHENTICATION FAILED');
      console.log('❌ Postman token is invalid or expired');
      console.log('💡 Need to get a fresh token from Postman');
    } else if (status === 403) {
      console.log('\n🚫 ACCESS FORBIDDEN');
      console.log('❌ Token valid but access denied');
    } else if (status === 429) {
      console.log('\n⏱️ RATE LIMITED');
      console.log('❌ Too many requests');
    } else {
      console.log('\n💥 UNEXPECTED ERROR');
      console.log('❌ Check network connectivity');
    }
    
    return false;
  }
}

// Run the test
testPostmanToken()
  .then(success => {
    if (success) {
      console.log('\n✅ Postman token test PASSED!');
      console.log('🚀 Run: node blackbox-scraper.js');
      console.log('📧 Or run: node fix-blackbox-complete.js');
      process.exit(0);
    } else {
      console.log('\n❌ Postman token test FAILED!');
      console.log('🔧 Need to get a fresh token from Postman');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\n💥 Test crashed:', error.message);
    process.exit(1);
  });
