// Extra.com store scraper using Unbxd Search API
import axios from 'axios';
import https from 'https';
import http from 'http';
import { JSDOM } from 'jsdom';

// Auto-normalization function
async function runPostFetchNormalization() {
  try {
    console.log('\n🔄 Running post-fetch normalization...');

    // 1. Run brand normalization
    console.log('📊 Running brand normalization...');
    try {
      const { BrandNormalizationMaster } = await import('./scripts/brand-normalization-master.js');
      const brandMaster = new BrandNormalizationMaster({
        interactive: false,
        dryRun: false,
        skipValidation: true,
        logLevel: 'info'
      });

      const brandResult = await brandMaster.run();
      if (brandResult.success) {
        console.log(`✅ Brand normalization completed: ${brandResult.results.migration?.stats?.brandsConsolidated || 0} brands consolidated`);
      } else {
        console.log(`⚠️ Brand normalization failed: ${brandResult.error}`);
      }
    } catch (error) {
      console.log(`⚠️ Brand normalization error: ${error.message}`);
    }

    // 2. Run category normalization
    console.log('📂 Running category normalization...');
    try {
      const { CategoryNormalizationMaster } = await import('./scripts/category-normalization-master.js');
      const categoryMaster = new CategoryNormalizationMaster({
        interactive: false,
        dryRun: false,
        skipValidation: true,
        logLevel: 'info',
        updateCategoriesTable: true
      });

      const categoryResult = await categoryMaster.run();
      if (categoryResult.success) {
        console.log(`✅ Category normalization completed: ${categoryResult.results.migration?.stats?.categoriesConsolidated || 0} categories consolidated`);
      } else {
        console.log(`⚠️ Category normalization failed: ${categoryResult.error}`);
      }
    } catch (error) {
      console.log(`⚠️ Category normalization error: ${error.message}`);
    }

    console.log('✅ Post-fetch normalization completed');

  } catch (error) {
    console.log(`⚠️ Post-fetch normalization failed: ${error.message}`);
  }
}

// Get comprehensive database statistics
async function getComprehensiveStats() {
  try {
    const pg = await import('pg');
    const pool = new pg.default.Pool({
      user: process.env.DB_USER || 'postgres',
      host: process.env.DB_HOST || 'postgres.dream4soft.co.uk',
      database: process.env.DB_NAME || 'ir2-ksa',
      password: process.env.DB_PASSWORD || 'Spider2kemo@412',
      port: process.env.DB_PORT || 5432,
      ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
      connectionTimeoutMillis: 10000,
      idleTimeoutMillis: 5000,
      query_timeout: 15000,
    });

    // Get all statistics in parallel
    const [
      totalStats,
      storeStats,
      brandStats,
      categoryStats,
      recentActivity,
      errorLogs,
      priceRanges
    ] = await Promise.all([
      // Total statistics
      pool.query(`
        SELECT
          (SELECT COUNT(*) FROM products) as total_products,
          (SELECT COUNT(DISTINCT brand) FROM products WHERE brand IS NOT NULL AND brand != '') as total_brands,
          (SELECT COUNT(*) FROM categories) as total_categories,
          (SELECT COUNT(*) FROM stores WHERE active = true) as total_stores,
          (SELECT AVG(price) FROM products WHERE price > 0) as avg_price,
          (SELECT MAX(price) FROM products WHERE price > 0) as max_price,
          (SELECT MIN(price) FROM products WHERE price > 0) as min_price
      `),

      // Store statistics
      pool.query(`
        SELECT
          s.name as store_name,
          s.slug as store_slug,
          COUNT(p.id) as product_count,
          AVG(p.price) as avg_price,
          COUNT(CASE WHEN p.created_at >= NOW() - INTERVAL '24 hours' THEN 1 END) as new_24h,
          COUNT(CASE WHEN p.updated_at >= NOW() - INTERVAL '24 hours' AND p.created_at < NOW() - INTERVAL '24 hours' THEN 1 END) as updated_24h,
          COUNT(CASE WHEN p.stock > 0 THEN 1 END) as in_stock_count,
          COUNT(CASE WHEN p.stock = 0 OR p.stock IS NULL THEN 1 END) as out_of_stock_count
        FROM stores s
        LEFT JOIN products p ON s.id = p.store_id
        WHERE s.active = true
        GROUP BY s.id, s.name, s.slug
        ORDER BY product_count DESC
      `),

      // Brand statistics
      pool.query(`
        SELECT
          brand,
          COUNT(*) as product_count,
          AVG(price) as avg_price,
          COUNT(DISTINCT store_id) as store_count
        FROM products
        WHERE brand IS NOT NULL AND brand != ''
        GROUP BY brand
        ORDER BY product_count DESC
        LIMIT 20
      `),

      // Category statistics
      pool.query(`
        SELECT
          c.name as category_name,
          COUNT(p.id) as product_count,
          AVG(p.price) as avg_price
        FROM categories c
        LEFT JOIN products p ON c.id = p.category_id
        GROUP BY c.id, c.name
        ORDER BY product_count DESC
        LIMIT 15
      `),

      // Recent activity (last 24 hours)
      pool.query(`
        SELECT
          s.name as store_name,
          COUNT(*) as total_changes,
          COUNT(CASE WHEN p.created_at >= NOW() - INTERVAL '24 hours' THEN 1 END) as new_products,
          COUNT(CASE WHEN p.updated_at >= NOW() - INTERVAL '24 hours' AND p.created_at < NOW() - INTERVAL '24 hours' THEN 1 END) as updated_products,
          COUNT(CASE WHEN p.updated_at >= NOW() - INTERVAL '1 hour' THEN 1 END) as changes_last_hour
        FROM products p
        LEFT JOIN stores s ON p.store_id = s.id
        WHERE p.updated_at >= NOW() - INTERVAL '24 hours'
        GROUP BY s.id, s.name
        ORDER BY total_changes DESC
      `),

      // Error logs (if available)
      pool.query(`
        SELECT
          'No error log table' as error_type,
          0 as error_count,
          NOW() as last_error
        LIMIT 1
      `).catch(() => ({ rows: [] })),

      // Price ranges by category
      pool.query(`
        SELECT
          c.name as category_name,
          MIN(p.price) as min_price,
          MAX(p.price) as max_price,
          AVG(p.price) as avg_price,
          COUNT(p.id) as product_count
        FROM categories c
        LEFT JOIN products p ON c.id = p.category_id
        WHERE p.price > 0
        GROUP BY c.id, c.name
        HAVING COUNT(p.id) > 0
        ORDER BY avg_price DESC
        LIMIT 10
      `)
    ]);

    await pool.end();

    return {
      totalStats: totalStats.rows[0] || {},
      storeStats: storeStats.rows || [],
      brandStats: brandStats.rows || [],
      categoryStats: categoryStats.rows || [],
      recentActivity: recentActivity.rows || [],
      errorLogs: errorLogs.rows || [],
      priceRanges: priceRanges.rows || []
    };

  } catch (error) {
    console.log(`⚠️ Database stats error: ${error.message}`);
    return {
      totalStats: {},
      storeStats: [],
      brandStats: [],
      categoryStats: [],
      recentActivity: [],
      errorLogs: [],
      priceRanges: []
    };
  }
}

// Comprehensive email notification function
async function sendScraperCompletionEmail(scraperName, results = {}) {
  try {
    console.log(`📧 Starting comprehensive post-fetch processing for ${scraperName}...`);

    // 1. Get comprehensive database statistics
    console.log('📊 Gathering comprehensive database statistics...');
    const dbStats = await getComprehensiveStats();

    // 2. Run normalization (simplified for now)
    console.log('🔄 Running post-fetch normalization...');
    let normalizationResults = {
      brandsConsolidated: 0,
      categoriesConsolidated: 0,
      status: 'Skipped - will be enhanced in next update'
    };

    // 3. Send comprehensive completion email
    console.log(`📧 Sending comprehensive completion email for ${scraperName}...`);

    try {
      const nodemailer = await import('nodemailer');

      const transporter = nodemailer.default.createTransporter({
        service: 'gmail',
        auth: {
          user: '<EMAIL>',
          pass: 'scbrwbunxuljiwje',
        }
      });

      const timestamp = new Date().toLocaleString();
      const errors = results.errors || [];

      const emailHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; }
            .section { margin: 25px 0; padding: 20px; border: 1px solid #e0e0e0; border-radius: 8px; }
            .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0; }
            .stat-card { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 15px; border-radius: 8px; text-align: center; }
            .stat-number { font-size: 28px; font-weight: bold; margin-bottom: 5px; }
            .stat-label { font-size: 14px; opacity: 0.9; }
            table { width: 100%; border-collapse: collapse; margin: 15px 0; }
            th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
            th { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; font-weight: bold; }
            tr:nth-child(even) { background-color: #f9f9f9; }
            .success { color: #28a745; font-weight: bold; }
            .warning { color: #ffc107; font-weight: bold; }
            .error { color: #dc3545; font-weight: bold; }
            .info { color: #17a2b8; font-weight: bold; }
            .footer { margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px; text-align: center; }
            .error-section { background-color: #fff5f5; border-left: 4px solid #dc3545; }
            .success-section { background-color: #f0fff4; border-left: 4px solid #28a745; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>🚀 ProductPricePro - Comprehensive Data Summary</h1>
            <p><strong>Scraper:</strong> ${scraperName}</p>
            <p><strong>Completed at:</strong> ${timestamp}</p>
          </div>

          <div class="section success-section">
            <h2>📦 Scraper Results - ${scraperName}</h2>
            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-number">${results.totalProducts || 0}</div>
                <div class="stat-label">Total Products</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">${results.newProducts || 0}</div>
                <div class="stat-label">New Products</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">${results.updatedProducts || 0}</div>
                <div class="stat-label">Updated Products</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">${results.duration || 'Unknown'}</div>
                <div class="stat-label">Duration</div>
              </div>
            </div>
          </div>

          <div class="section">
            <h2>📊 Complete Database Statistics</h2>
            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-number">${dbStats.totalStats.total_products || 0}</div>
                <div class="stat-label">Total Products</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">${dbStats.totalStats.total_brands || 0}</div>
                <div class="stat-label">Total Brands</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">${dbStats.totalStats.total_categories || 0}</div>
                <div class="stat-label">Total Categories</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">${dbStats.totalStats.total_stores || 0}</div>
                <div class="stat-label">Active Stores</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">$${parseFloat(dbStats.totalStats.avg_price || 0).toFixed(2)}</div>
                <div class="stat-label">Average Price</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">$${parseFloat(dbStats.totalStats.max_price || 0).toFixed(2)}</div>
                <div class="stat-label">Highest Price</div>
              </div>
            </div>
          </div>

          <div class="section">
            <h2>🏪 Complete Store Statistics</h2>
            <table>
              <thead>
                <tr>
                  <th>Store Name</th>
                  <th>Total Products</th>
                  <th>Avg Price</th>
                  <th>New (24h)</th>
                  <th>Updated (24h)</th>
                  <th>In Stock</th>
                  <th>Out of Stock</th>
                </tr>
              </thead>
              <tbody>
                ${dbStats.storeStats.map(store => `
                  <tr>
                    <td><strong>${store.store_name || 'Unknown'}</strong></td>
                    <td class="info">${store.product_count || 0}</td>
                    <td>$${parseFloat(store.avg_price || 0).toFixed(2)}</td>
                    <td class="success">${store.new_24h || 0}</td>
                    <td class="warning">${store.updated_24h || 0}</td>
                    <td class="success">${store.in_stock_count || 0}</td>
                    <td class="error">${store.out_of_stock_count || 0}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>

          <div class="section">
            <h2>🔄 Recent Activity (Last 24 Hours)</h2>
            <table>
              <thead>
                <tr>
                  <th>Store</th>
                  <th>Total Changes</th>
                  <th>New Products</th>
                  <th>Updated Products</th>
                  <th>Changes (Last Hour)</th>
                </tr>
              </thead>
              <tbody>
                ${dbStats.recentActivity.map(activity => `
                  <tr>
                    <td><strong>${activity.store_name || 'Unknown'}</strong></td>
                    <td class="info">${activity.total_changes || 0}</td>
                    <td class="success">${activity.new_products || 0}</td>
                    <td class="warning">${activity.updated_products || 0}</td>
                    <td class="info">${activity.changes_last_hour || 0}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>

          <div class="section">
            <h2>🏷️ Top Brands (Complete List)</h2>
            <table>
              <thead>
                <tr>
                  <th>Brand Name</th>
                  <th>Product Count</th>
                  <th>Average Price</th>
                  <th>Store Coverage</th>
                </tr>
              </thead>
              <tbody>
                ${dbStats.brandStats.map(brand => `
                  <tr>
                    <td><strong>${brand.brand}</strong></td>
                    <td class="info">${brand.product_count}</td>
                    <td>$${parseFloat(brand.avg_price || 0).toFixed(2)}</td>
                    <td class="success">${brand.store_count} stores</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>

          <div class="section">
            <h2>📂 Category Analysis (Complete)</h2>
            <table>
              <thead>
                <tr>
                  <th>Category Name</th>
                  <th>Product Count</th>
                  <th>Average Price</th>
                </tr>
              </thead>
              <tbody>
                ${dbStats.categoryStats.map(category => `
                  <tr>
                    <td><strong>${category.category_name || 'Uncategorized'}</strong></td>
                    <td class="info">${category.product_count || 0}</td>
                    <td>$${parseFloat(category.avg_price || 0).toFixed(2)}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>

          <div class="section">
            <h2>💰 Price Range Analysis</h2>
            <table>
              <thead>
                <tr>
                  <th>Category</th>
                  <th>Min Price</th>
                  <th>Max Price</th>
                  <th>Average Price</th>
                  <th>Product Count</th>
                </tr>
              </thead>
              <tbody>
                ${dbStats.priceRanges.map(range => `
                  <tr>
                    <td><strong>${range.category_name}</strong></td>
                    <td class="success">$${parseFloat(range.min_price || 0).toFixed(2)}</td>
                    <td class="error">$${parseFloat(range.max_price || 0).toFixed(2)}</td>
                    <td class="info">$${parseFloat(range.avg_price || 0).toFixed(2)}</td>
                    <td>${range.product_count}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>

          ${errors.length > 0 ? `
          <div class="section error-section">
            <h2>⚠️ Complete Error Log</h2>
            <p><strong>Total Errors:</strong> <span class="error">${errors.length}</span></p>
            <table>
              <thead>
                <tr>
                  <th>#</th>
                  <th>Error Details</th>
                  <th>Timestamp</th>
                </tr>
              </thead>
              <tbody>
                ${errors.map((error, index) => `
                  <tr>
                    <td>${index + 1}</td>
                    <td class="error">${error}</td>
                    <td>${timestamp}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
          ` : `
          <div class="section success-section">
            <h2>✅ No Errors Detected</h2>
            <p>All operations completed successfully without any errors.</p>
          </div>
          `}

          <div class="section">
            <h2>🔄 Data Normalization Status</h2>
            <p><strong>Status:</strong> ${normalizationResults.status}</p>
            <p><strong>Brands Consolidated:</strong> ${normalizationResults.brandsConsolidated}</p>
            <p><strong>Categories Consolidated:</strong> ${normalizationResults.categoriesConsolidated}</p>
          </div>

          <div class="footer">
            <h3>📧 Complete System Summary</h3>
            <p><strong>Email sent to:</strong> <EMAIL></p>
            <p><strong>Generated at:</strong> ${timestamp}</p>
            <p><strong>System:</strong> ProductPricePro Comprehensive Monitoring</p>
            <p><strong>Coverage:</strong> All Stores • All Applications • All Errors • Complete Statistics</p>
          </div>
        </body>
        </html>
      `;

      const emailOptions = {
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: `ProductPricePro - COMPREHENSIVE SUMMARY - ${scraperName} (${new Date().toLocaleDateString()})`,
        html: emailHTML
      };

      const info = await transporter.sendMail(emailOptions);
      console.log(`✅ Comprehensive completion email sent <NAME_EMAIL>`);
      console.log(`📧 Message ID: ${info.messageId}`);
      console.log(`📊 Email includes: All stores, all brands, all categories, all errors, complete statistics`);

    } catch (emailError) {
      console.log(`⚠️ Email sending failed: ${emailError.message}`);
    }

  } catch (error) {
    console.log(`⚠️ Could not send comprehensive completion email: ${error.message}`);
  }
}

// Import utilities from storeScraper.js
import { extractSizeAndUOM, createCategorySlug, normalizeCategoryName } from './storeScraper.js';

// Import storage for database operations
import { storage } from './storage.js';

/**
 * Get safe ISO timestamp to prevent invalid date errors
 */
function getSafeTimestamp() {
  try {
    const now = new Date();
    if (isNaN(now.getTime())) {
      console.warn('⚠️ Invalid date detected, using fallback timestamp');
      return new Date('2025-01-10T00:00:00.000Z').toISOString();
    }
    return now.toISOString();
  } catch (error) {
    console.warn('⚠️ Error creating timestamp, using fallback:', error.message);
    return new Date('2025-01-10T00:00:00.000Z').toISOString();
  }
}

// Global counter for unique ID generation
let globalProductCounter = 0;

// Extra.com Unbxd API configuration
const EXTRA_CONFIG = {
  name: 'Extra',
  slug: 'extra',
  url: 'https://www.extra.com/en-sa/',
  baseUrl: 'https://www.extra.com',
  englishUrl: 'https://www.extra.com/en-sa',
  // Unbxd API configuration
  apiBaseUrl: 'https://search.unbxd.io',
  apiKey: '8fb45132f31d81ab46966cc135c24430',
  siteKey: 'ss-unbxd-auk-extra-saudi-ar-prod11541714990564',
  apiEndpoint: '/search',
  timeout: 30000,
  maxRetries: 3,
  retryDelay: 2000,
  // Performance optimization settings - OPTIMIZED FOR 10K+ PRODUCTS
  maxPagesPerCategory: 50, // Increased from 20 to capture more products per category
  requestDelay: 500, // Reduced from 1000ms to 500ms for faster scraping
  batchSize: 200, // Increased from 100 to 200 for better database performance
  maxConcurrentRequests: 6, // Increased from 3 to 6 for parallel processing
  rowsPerPage: 1000, // Keep at maximum API limit
  maxConsecutiveEmptyPages: 5, // Increased from 3 to be more thorough
  enableBulkInserts: true, // Enable bulk database operations
  enableRetryLogic: true // Enable enhanced retry logic
};

// Extra.com categories configuration - using API-based approach for comprehensive coverage
const EXTRA_CATEGORIES = {
  'air-conditioners': {
    name: 'Air Conditioners',
    searchTerms: ['air conditioner', 'split air conditioner', 'window air conditioner', 'portable air conditioner', 'ac', 'cooling'],
    subcategories: [
      { name: 'Split Air Conditioners', searchTerms: ['split air conditioner', 'split ac'] },
      { name: 'Window Air Conditioners', searchTerms: ['window air conditioner', 'window ac'] },
      { name: 'Portable Air Conditioners', searchTerms: ['portable air conditioner', 'portable ac'] }
    ]
  },
  'televisions': {
    name: 'Televisions',
    searchTerms: ['television', 'tv', 'smart tv', 'led tv', 'oled tv', 'qled tv', 'screen'],
    subcategories: [
      { name: 'Smart TVs', searchTerms: ['smart tv', 'smart television'] },
      { name: 'LED TVs', searchTerms: ['led tv', 'led television'] },
      { name: 'OLED TVs', searchTerms: ['oled tv', 'oled television'] },
      { name: 'QLED TVs', searchTerms: ['qled tv', 'qled television'] }
    ]
  },
  'refrigerators': {
    name: 'Refrigerators',
    searchTerms: [
      // English terms
      'refrigerator', 'fridge', 'freezer', 'side by side refrigerator', 'cooling',
      // Arabic terms for Saudi market
      'ثلاجة', 'مجمد', 'برادة',
      // Brand-specific terms
      'samsung refrigerator', 'lg refrigerator', 'bosch refrigerator', 'whirlpool refrigerator', 'hitachi refrigerator',
      // Model-specific terms
      'side by side', 'top mount', 'bottom mount', 'french door', 'mini fridge'
    ],
    subcategories: [
      { name: 'Side by Side Refrigerators', searchTerms: ['side by side refrigerator', 'side by side fridge', 'samsung side by side', 'lg side by side'] },
      { name: 'Top Mount Refrigerators', searchTerms: ['top mount refrigerator', 'top freezer', 'single door fridge'] },
      { name: 'Bottom Mount Refrigerators', searchTerms: ['bottom mount refrigerator', 'bottom freezer', 'french door'] }
    ]
  },
  'washing-machines': {
    name: 'Washing Machines',
    searchTerms: [
      // English terms
      'washing machine', 'washer', 'front load washing machine', 'top load washing machine', 'laundry',
      // Arabic terms for Saudi market
      'غسالة', 'غسالة ملابس', 'غسالة اتوماتيك',
      // Brand-specific terms
      'samsung washing machine', 'lg washing machine', 'bosch washing machine', 'whirlpool washing machine', 'electrolux washing machine',
      // Model-specific terms
      'front load', 'top load', 'automatic washing machine', 'semi automatic', 'washer dryer'
    ],
    subcategories: [
      { name: 'Front Load Washing Machines', searchTerms: ['front load washing machine', 'front load washer', 'samsung front load', 'lg front load'] },
      { name: 'Top Load Washing Machines', searchTerms: ['top load washing machine', 'top load washer', 'automatic top load'] }
    ]
  },

 
 
  'kitchen-appliances': {
    name: 'Kitchen Appliances',
    searchTerms: ['microwave', 'oven', 'dishwasher', 'coffee machine', 'blender', 'food processor', 'kitchen'],
    subcategories: [
      { name: 'Microwaves', searchTerms: ['microwave', 'microwave oven'] },
      { name: 'Ovens', searchTerms: ['oven', 'electric oven', 'gas oven'] },
      { name: 'Dishwashers', searchTerms: ['dishwasher', 'dish washer'] },
      { name: 'Coffee Machines', searchTerms: ['coffee machine', 'espresso machine'] }
    ]
  },
  'small-appliances': {
    name: 'Small Appliances',
    searchTerms: ['vacuum cleaner', 'iron', 'hair dryer', 'electric kettle', 'toaster', 'small appliance'],
    subcategories: [
      { name: 'Vacuum Cleaners', searchTerms: ['vacuum cleaner', 'vacuum'] },
      { name: 'Irons', searchTerms: ['iron', 'steam iron'] },
      { name: 'Hair Dryers', searchTerms: ['hair dryer', 'blow dryer'] },
      { name: 'Electric Kettles', searchTerms: ['electric kettle', 'kettle'] }
    ]
  },
  
  'audio': {
    name: 'Audio & Headphones',
    searchTerms: ['headphones', 'earphones', 'speakers', 'soundbar', 'bluetooth speaker', 'audio'],
    subcategories: [
      { name: 'Headphones', searchTerms: ['headphones', 'earphones'] },
      { name: 'Speakers', searchTerms: ['speakers', 'bluetooth speaker'] },
      { name: 'Soundbars', searchTerms: ['soundbar', 'sound bar'] }
    ]
  },

};

// Create axios instance for Unbxd API calls
const axiosInstance = axios.create({
  timeout: EXTRA_CONFIG.timeout,
  headers: {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Origin': 'https://www.extra.com',
    'Referer': 'https://www.extra.com/en-sa/'
  },
  httpsAgent: new https.Agent({
    rejectUnauthorized: false,
    secureProtocol: 'TLSv1_2_method'
  }),
  httpAgent: new http.Agent({
    keepAlive: true
  })
});

/**
 * Build Unbxd API URL for search
 */
function buildUnbxdApiUrl(searchQuery, page = 1, rows = 1000) {
  const baseUrl = `${EXTRA_CONFIG.apiBaseUrl}/${EXTRA_CONFIG.apiKey}/${EXTRA_CONFIG.siteKey}${EXTRA_CONFIG.apiEndpoint}`;
  const params = new URLSearchParams({
    q: searchQuery,
    rows: rows.toString(),
    version: 'V2',
    filter: 'type:PRODUCT',
    page: page.toString()
  });

  return `${baseUrl}?${params.toString()}`;
}

/**
 * Utility function to delay execution
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Retry wrapper for HTTP requests
 */
async function retryRequest(requestFn, maxRetries = EXTRA_CONFIG.maxRetries) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await requestFn();
    } catch (error) {
      const isRateLimited = error.response?.status === 429;
      const isServerError = error.response?.status >= 500;
      const isTimeout = error.code === 'ECONNABORTED' || error.code === 'ETIMEDOUT';

      console.log(`🔄 [Extra] Request attempt ${attempt}/${maxRetries} failed:`, {
        message: error.message,
        status: error.response?.status,
        code: error.code,
        isRateLimited,
        isServerError,
        isTimeout
      });

      if (attempt === maxRetries) {
        throw error;
      }

      // Enhanced exponential backoff with jitter
      let delayMs = EXTRA_CONFIG.retryDelay * Math.pow(2, attempt - 1);

      // Longer delay for rate limiting
      if (isRateLimited) {
        delayMs = Math.max(delayMs, 5000); // Minimum 5 seconds for rate limits
      }

      // Add jitter to prevent thundering herd
      delayMs += Math.random() * 1000;

      console.log(`⏳ [Extra] Waiting ${Math.round(delayMs)}ms before retry...`);
      await delay(delayMs);
    }
  }
}

/**
 * Extract brand from API product data
 */
function extractBrandFromApi(apiProduct) {
  // Try to get brand from API fields
  if (apiProduct.brand && Array.isArray(apiProduct.brand) && apiProduct.brand.length > 0) {
    return apiProduct.brand[0];
  }

  if (apiProduct.brandEn) {
    return apiProduct.brandEn;
  }

  // Fallback to extracting from product name
  const productName = apiProduct.nameEn || apiProduct.title || '';
  return extractBrandFromName(productName);
}

/**
 * Extract brand from product name (fallback method)
 */
function extractBrandFromName(productName) {
  if (!productName) return 'Extra';

  const brandPatterns = [
    /^(Apple|Samsung|LG|Sony|Huawei|Honor|Xiaomi|Motorola|Tecno|Infinix|Oppo|Vivo|OnePlus|Nokia|Realme)/i,
    /^(HP|Dell|Lenovo|Asus|Acer|MSI|Microsoft|MacBook|iMac)/i,
    /^(Gree|Midea|Haier|TCL|Bosch|Siemens|Whirlpool|Electrolux|Frigidaire)/i,
    /^(Philips|Braun|Panasonic|Sharp|Toshiba|Hitachi|JVC|Pioneer)/i
  ];

  for (const pattern of brandPatterns) {
    const match = productName.match(pattern);
    if (match) {
      return match[1];
    }
  }

  // Fallback: use first word if it looks like a brand
  const firstWord = productName.split(' ')[0];
  if (firstWord && firstWord.length > 2 && /^[A-Za-z]+$/.test(firstWord)) {
    return firstWord;
  }

  return 'Extra';
}

/**
 * Extract brand from product name (alias for HTML parsing compatibility)
 */
function extractBrand(productName) {
  return extractBrandFromName(productName);
}

/**
 * Extract model from product name (alias for HTML parsing compatibility)
 */
function extractModel(productName, brand) {
  return extractModelFromName(productName, brand);
}

/**
 * Extract price information from HTML element (for HTML parsing) with Enhanced Multi-Price Support
 * Handles Extra store's multiple pricing tiers: Standard, Jood Gold, Original prices
 */
function extractPriceInfo(element) {
  try {
    let currentPrice = '0';
    let regularPrice = '0';
    let memberPrice = '0'; // NEW: Jood Gold/membership price
    let salePrice = null;

    // Enhanced price selectors for Extra store's multi-price structure
    const priceSelectors = [
      '.price', '.current-price', '.sale-price',
      '[class*="price"]', '[class*="Price"]',
      '[class*="standard"]', '[class*="Standard"]'
    ];

    // Look for standard price
    for (const selector of priceSelectors) {
      const priceElement = element.querySelector(selector);
      if (priceElement) {
        const priceText = priceElement.textContent.trim();
        const priceMatch = priceText.match(/[\d,]+\.?\d*/);
        if (priceMatch) {
          currentPrice = priceMatch[0].replace(/,/g, '');
          regularPrice = currentPrice;
          break;
        }
      }
    }

    // Look for member/Jood Gold price specifically - Enhanced selectors based on Extra website structure
    const memberPriceSelectors = [
      '[class*="jood"]', '[class*="Jood"]', '[class*="gold"]', '[class*="Gold"]',
      '[class*="member"]', '[class*="Member"]', '[class*="loyalty"]',
      '.member-price', '.jood-price', '.gold-price', '.loyalty-price',
      // Additional selectors based on Extra website analysis
      '[data-testid*="jood"]', '[data-testid*="gold"]', '[data-testid*="member"]',
      '.jood-gold-price', '.membership-price', '.loyalty-member-price'
    ];

    for (const selector of memberPriceSelectors) {
      const memberElement = element.querySelector(selector);
      if (memberElement) {
        const memberText = memberElement.textContent.trim();
        const memberMatch = memberText.match(/[\d,]+\.?\d*/);
        if (memberMatch) {
          memberPrice = memberMatch[0].replace(/,/g, '');
          console.log(`🎯 [Extra] Found member price using selector "${selector}": ${memberPrice}`);
          break;
        }
      }
    }



    // Look for original/was price (crossed out price)
    const originalPriceSelectors = [
      '.was-price', '.original-price', '.crossed-price',
      '[class*="was"]', '[class*="original"]', '[class*="crossed"]',
      '.line-through', '[style*="line-through"]', '[style*="text-decoration"]'
    ];

    for (const selector of originalPriceSelectors) {
      const originalElement = element.querySelector(selector);
      if (originalElement) {
        const originalText = originalElement.textContent.trim();
        const originalMatch = originalText.match(/[\d,]+\.?\d*/);
        if (originalMatch) {
          regularPrice = originalMatch[0].replace(/,/g, '');
          break;
        }
      }
    }

    // Price precedence logic: Use the best available price as current price
    // CRITICAL FIX: For Extra store, prioritize Jood Gold/member price when available
    let finalCurrentPrice = currentPrice;
    if (memberPrice !== '0' && parseFloat(memberPrice) > 0) {
      // Use member price if it's valid, regardless of comparison with current price
      // This ensures we get the Jood Gold price instead of standard price
      finalCurrentPrice = memberPrice;
      console.log(`💰 [Extra] Using member/Jood Gold price: ${finalCurrentPrice} instead of standard: ${currentPrice}`);
    } else if (parseFloat(currentPrice) > 0) {
      finalCurrentPrice = currentPrice;
      console.log(`💰 [Extra] Using standard price: ${finalCurrentPrice}`);
    }

    return {
      currentPrice: finalCurrentPrice,
      regularPrice: regularPrice,
      memberPrice: memberPrice, // NEW: Member/loyalty price
      salePrice: salePrice
    };
  } catch (error) {
    return { currentPrice: '0', regularPrice: '0', memberPrice: '0', salePrice: null };
  }
}

/**
 * Extract stock information from HTML element (for HTML parsing)
 */
function extractStockInfo(element) {
  try {
    console.log(`🔍 [Extra] Analyzing HTML stock information`);

    // Look for stock-related elements and text
    const stockSelectors = [
      '.stock-status',
      '.availability',
      '.inventory-status',
      '.stock-info',
      '.product-availability',
      '[class*="stock"]',
      '[class*="availability"]'
    ];

    let stockText = '';

    // Try to find stock information in various elements
    for (const selector of stockSelectors) {
      const stockElement = element.querySelector(selector);
      if (stockElement) {
        stockText = stockElement.textContent.trim();
        console.log(`   📝 Found stock text in ${selector}: "${stockText}"`);
        break;
      }
    }

    // If no specific stock element found, check the entire element text
    if (!stockText) {
      const allText = element.textContent || '';
      const stockKeywords = [
        /\d+\s*items?\s*left/i,
        /\d+\s*in\s*stock/i,
        /out\s*of\s*stock/i,
        /in\s*stock/i,
        /available/i,
        /unavailable/i,
        /متوفر/,
        /غير\s*متوفر/,
        /في\s*المخزن/
      ];

      for (const keyword of stockKeywords) {
        const match = allText.match(keyword);
        if (match) {
          stockText = match[0];
          console.log(`   📝 Found stock keyword in text: "${stockText}"`);
          break;
        }
      }
    }

    // Parse the stock text using the same logic as API extraction
    if (stockText) {
      // Use a simplified version of the API stock extraction logic
      const mockApiProduct = { stockText: stockText };
      const result = extractStockInfoFromApi(mockApiProduct);
      console.log(`   📋 HTML stock result: quantity=${result.quantity_available}, status=${result.stock_status}`);
      return result;
    }

    // Default fallback for HTML parsing
    console.log(`   🔄 No stock information found in HTML - using default`);
    return {
      stock: 10,
      stockStatus: 'In Stock',
      quantity_available: 10,
      stock_status: 'in_stock'
    };
  } catch (error) {
    console.log(`❌ [Extra] Error extracting HTML stock info:`, error.message);
    return {
      stock: 10,
      stockStatus: 'In Stock',
      quantity_available: 10,
      stock_status: 'in_stock'
    };
  }
}

/**
 * Extract image URL from HTML element (for HTML parsing)
 */
function extractImageUrl(element) {
  try {
    const imgElement = element.querySelector('img');
    if (imgElement) {
      let imageUrl = imgElement.getAttribute('src') || imgElement.getAttribute('data-src') || '';
      if (imageUrl && imageUrl.startsWith('//')) {
        imageUrl = 'https:' + imageUrl;
      } else if (imageUrl && imageUrl.startsWith('/')) {
        imageUrl = EXTRA_CONFIG.baseUrl + imageUrl;
      }
      return imageUrl;
    }
    return '';
  } catch (error) {
    return '';
  }
}

/**
 * Extract product URL from HTML element (for HTML parsing)
 */
function extractProductUrl(element) {
  try {
    const linkElement = element.querySelector('a[href]');
    if (linkElement) {
      const href = linkElement.getAttribute('href');
      if (href) {
        return href.startsWith('http') ? href : EXTRA_CONFIG.baseUrl + href;
      }
    }
    return '';
  } catch (error) {
    return '';
  }
}

/**
 * Extract and clean model from API product data
 */
function extractModelFromApi(apiProduct, brand) {
  // Try to get model from API fields first
  if (apiProduct.modelNumber) {
    let model = apiProduct.modelNumber;
    // Clean model according to requirements (remove dash and number after dash)
    model = model.replace(/\s*-\s*\d+$/, '');
    return model;
  }

  // Fallback to extracting from product name
  const productName = apiProduct.nameEn || apiProduct.title || '';
  return extractModelFromName(productName, brand);
}

/**
 * Extract and clean model from product name (fallback method)
 */
function extractModelFromName(productName, brand) {
  if (!productName) return '';

  // Remove brand from the beginning
  let cleanName = productName.replace(new RegExp(`^${brand}\\s*`, 'i'), '').trim();

  // Extract model patterns
  const modelPatterns = [
    /([A-Z0-9]{2,}[-_]?[A-Z0-9]{2,})/i,  // Alphanumeric model codes
    /(\d{2,}[A-Z]{1,3}\d*)/i,             // Number + letters pattern
    /([A-Z]{2,}\d{2,})/i,                 // Letters + numbers pattern
    /(iPhone\s*\d+[A-Za-z]*)/i,          // iPhone models
    /(Galaxy\s*[A-Z0-9\s]+)/i,           // Samsung Galaxy models
    /(MacBook\s*[A-Za-z0-9\s]*)/i        // MacBook models
  ];

  for (const pattern of modelPatterns) {
    const match = cleanName.match(pattern);
    if (match) {
      let model = match[1].trim();

      // Clean model according to requirements (remove dash and number after dash)
      model = model.replace(/\s*-\s*\d+$/, '');

      return model;
    }
  }

  // Fallback: use first few words
  const words = cleanName.split(' ').slice(0, 3);
  return words.join(' ').replace(/[^\w\s-]/g, '').trim();
}

/**
 * Extract stock information from API product data with comprehensive format support
 */
function extractStockInfoFromApi(apiProduct) {
  try {
    console.log(`🔍 [Extra] Analyzing stock for product: ${apiProduct.nameEn || apiProduct.title || 'Unknown'}`);

    // Initialize default values
    let quantity = null;
    let stockStatus = 'unknown';
    let stockText = '';

    // === STEP 1: Check explicit stock flags ===
    if (apiProduct.inStockFlag === 'false' || apiProduct.inStockFlag === false) {
      console.log(`   ❌ inStockFlag is false - marking as out of stock`);
      return {
        stock: 0,
        stockStatus: 'out_of_stock',
        quantity_available: 0,
        stock_status: 'out_of_stock'
      };
    }

    if (apiProduct.available === 'false' || apiProduct.available === false) {
      console.log(`   ❌ available flag is false - marking as out of stock`);
      return {
        stock: 0,
        stockStatus: 'out_of_stock',
        quantity_available: 0,
        stock_status: 'out_of_stock'
      };
    }

    // === STEP 2: Extract stock text from various fields ===
    const stockFields = [
      apiProduct.stockText,
      apiProduct.stockStatus,
      apiProduct.availabilityText,
      apiProduct.inventoryText,
      apiProduct.stockMessage,
      apiProduct.availability,
      apiProduct.stockInfo
    ];

    for (const field of stockFields) {
      if (field && typeof field === 'string') {
        stockText = field.trim();
        console.log(`   📝 Found stock text: "${stockText}"`);
        break;
      }
    }

    // === STEP 3: Parse stock text for specific quantities ===
    if (stockText) {
      // English patterns
      const englishPatterns = [
        /(\d+)\s*items?\s*left/i,           // "5 items left", "1 item left"
        /(\d+)\s*in\s*stock/i,              // "10 in stock"
        /(\d+)\s*available/i,               // "3 available"
        /only\s*(\d+)\s*left/i,             // "only 2 left"
        /(\d+)\s*remaining/i,               // "7 remaining"
        /limited\s*stock.*?(\d+)/i,         // "limited stock - 4"
        /(\d+)\s*units?\s*available/i       // "5 units available"
      ];

      // Arabic patterns
      const arabicPatterns = [
        /(\d+)\s*في\s*المخزن/,              // "5 في المخزن" (5 in stock)
        /(\d+)\s*متوفر/,                    // "3 متوفر" (3 available)
        /(\d+)\s*قطعة\s*متبقية/,            // "2 قطعة متبقية" (2 pieces remaining)
        /(\d+)\s*وحدة\s*متاحة/,             // "4 وحدة متاحة" (4 units available)
        /مخزون\s*محدود.*?(\d+)/,            // "مخزون محدود - 3" (limited stock - 3)
        /متبقي\s*(\d+)/,                   // "متبقي 6" (6 remaining)
        /(\d+)\s*فقط\s*متبقي/               // "2 فقط متبقي" (only 2 remaining)
      ];

      // Try English patterns first
      for (const pattern of englishPatterns) {
        const match = stockText.match(pattern);
        if (match) {
          quantity = parseInt(match[1]);
          console.log(`   ✅ Extracted quantity from English pattern: ${quantity}`);
          break;
        }
      }

      // Try Arabic patterns if no English match
      if (quantity === null) {
        for (const pattern of arabicPatterns) {
          const match = stockText.match(pattern);
          if (match) {
            quantity = parseInt(match[1]);
            console.log(`   ✅ Extracted quantity from Arabic pattern: ${quantity}`);
            break;
          }
        }
      }

      // === STEP 4: Check for general stock status indicators ===
      if (quantity === null) {
        const outOfStockIndicators = [
          /out\s*of\s*stock/i,
          /not\s*available/i,
          /unavailable/i,
          /sold\s*out/i,
          /غير\s*متوفر/,                     // "غير متوفر" (not available)
          /نفد\s*المخزون/,                   // "نفد المخزون" (out of stock)
          /غير\s*متاح/                      // "غير متاح" (unavailable)
        ];

        const lowStockIndicators = [
          /limited\s*stock/i,
          /low\s*stock/i,
          /few\s*left/i,
          /hurry/i,
          /مخزون\s*محدود/,                   // "مخزون محدود" (limited stock)
          /مخزون\s*قليل/,                    // "مخزون قليل" (low stock)
          /كمية\s*محدودة/                    // "كمية محدودة" (limited quantity)
        ];

        const inStockIndicators = [
          /in\s*stock/i,
          /available/i,
          /ready\s*to\s*ship/i,
          /متوفر/,                          // "متوفر" (available)
          /في\s*المخزن/,                    // "في المخزن" (in stock)
          /جاهز\s*للشحن/                    // "جاهز للشحن" (ready to ship)
        ];

        // Check for out of stock
        for (const pattern of outOfStockIndicators) {
          if (pattern.test(stockText)) {
            quantity = 0;
            stockStatus = 'out_of_stock';
            console.log(`   ❌ Detected out of stock indicator: "${stockText}"`);
            break;
          }
        }

        // Check for low stock (if not already out of stock)
        if (quantity === null) {
          for (const pattern of lowStockIndicators) {
            if (pattern.test(stockText)) {
              quantity = 3; // Assume low stock means ~3 items
              stockStatus = 'low_stock';
              console.log(`   ⚠️ Detected low stock indicator: "${stockText}"`);
              break;
            }
          }
        }

        // Check for in stock (if not already determined)
        if (quantity === null) {
          for (const pattern of inStockIndicators) {
            if (pattern.test(stockText)) {
              quantity = 10; // Assume good stock level
              stockStatus = 'in_stock';
              console.log(`   ✅ Detected in stock indicator: "${stockText}"`);
              break;
            }
          }
        }
      }
    }

    // === STEP 5: Check numeric stock fields ===
    if (quantity === null) {
      const numericFields = [
        apiProduct.stockQuantity,
        apiProduct.availableQuantity,
        apiProduct.inventoryCount,
        apiProduct.quantity,
        apiProduct.stock,
        apiProduct.availableStock
      ];

      for (const field of numericFields) {
        if (typeof field === 'number' && field >= 0) {
          quantity = field;
          console.log(`   📊 Found numeric stock field: ${quantity}`);
          break;
        } else if (typeof field === 'string') {
          const parsed = parseInt(field);
          if (!isNaN(parsed) && parsed >= 0) {
            quantity = parsed;
            console.log(`   📊 Parsed numeric stock from string: ${quantity}`);
            break;
          }
        }
      }
    }

    // === STEP 6: Check inStockCities array ===
    if (quantity === null && apiProduct.inStockCities && Array.isArray(apiProduct.inStockCities)) {
      if (apiProduct.inStockCities.length > 0) {
        quantity = 10; // Assume good availability if in multiple cities
        stockStatus = 'in_stock';
        console.log(`   🏙️ Available in ${apiProduct.inStockCities.length} cities - assuming in stock`);
      } else {
        quantity = 0;
        stockStatus = 'out_of_stock';
        console.log(`   🏙️ Not available in any cities - marking as out of stock`);
      }
    }

    // === STEP 7: Use soldQuantity as availability indicator ===
    if (quantity === null && apiProduct.soldQuantity && typeof apiProduct.soldQuantity === 'number') {
      // High sold quantity might indicate low remaining stock
      if (apiProduct.soldQuantity > 100) {
        quantity = 2; // Assume low stock for popular items
        stockStatus = 'low_stock';
        console.log(`   📈 High sold quantity (${apiProduct.soldQuantity}) - assuming low stock`);
      } else {
        quantity = Math.max(10, 50 - apiProduct.soldQuantity);
        stockStatus = 'in_stock';
        console.log(`   📈 Moderate sold quantity (${apiProduct.soldQuantity}) - assuming good stock`);
      }
    }

    // === STEP 8: Final fallback - DEFAULT TO IN STOCK ===
    if (quantity === null) {
      if (apiProduct.inStockFlag === 'true' || apiProduct.inStockFlag === true) {
        quantity = 10;
        stockStatus = 'in_stock';
        console.log(`   🔄 Fallback: inStockFlag is true - assuming in stock`);
      } else {
        // CRITICAL FIX: Default to in_stock instead of unknown
        quantity = 10; // Default to reasonable stock level
        stockStatus = 'in_stock';
        console.log(`   ✅ FALLBACK: No stock information found - defaulting to IN STOCK (quantity: 10)`);
      }
    }

    // === STEP 9: Determine final stock status based on quantity ===
    if (stockStatus === 'unknown' && quantity !== null) {
      if (quantity === 0) {
        stockStatus = 'out_of_stock';
      } else if (quantity >= 1 && quantity <= 5) {
        stockStatus = 'low_stock';
      } else if (quantity > 5) {
        stockStatus = 'in_stock';
      }
    }

    console.log(`   📋 Final stock result: quantity=${quantity}, status=${stockStatus}`);

    return {
      stock: quantity || 0,                    // Legacy field for compatibility
      stockStatus: stockStatus === 'out_of_stock' ? 'Out of Stock' :
                  stockStatus === 'low_stock' ? 'Low Stock' :
                  stockStatus === 'in_stock' ? 'In Stock' : 'Unknown',
      quantity_available: quantity,            // New enhanced field
      stock_status: stockStatus                // New standardized enum field
    };

  } catch (error) {
    console.log(`❌ [Extra] Error extracting stock info:`, error.message);
    console.log(`✅ [Extra] FALLBACK: Defaulting to IN STOCK due to error`);
    return {
      stock: 10,                    // Default to reasonable stock level
      stockStatus: 'In Stock',      // Default to in stock
      quantity_available: 10,       // Default quantity
      stock_status: 'in_stock'      // Default to in_stock status
    };
  }
}

/**
 * Extract price information from API product data with Enhanced Multi-Price Support
 * Handles Extra store's multiple pricing tiers: Standard, Jood Gold, Original prices
 */
function extractPriceInfoFromApi(apiProduct) {
  try {
    let currentPrice = 0;
    let regularPrice = 0;
    let memberPrice = 0; // NEW: Jood Gold/membership price
    let salePrice = null;

    // Try to get current/selling price (Standard price) - Enhanced for Extra.com API
    if (apiProduct.sellingPrice && typeof apiProduct.sellingPrice === 'number') {
      currentPrice = apiProduct.sellingPrice;
      console.log(`📘 [Extra API] Found sellingPrice (standard): ${currentPrice}`);
    } else if (apiProduct.price && typeof apiProduct.price === 'number') {
      currentPrice = apiProduct.price;
      console.log(`📘 [Extra API] Found price (standard): ${currentPrice}`);
    } else if (apiProduct.wasTechnoPrice && typeof apiProduct.wasTechnoPrice === 'number') {
      currentPrice = apiProduct.wasTechnoPrice;
      console.log(`📘 [Extra API] Found wasTechnoPrice (standard): ${currentPrice}`);
    }

    // Try to get member/loyalty price (Jood Gold price) - Enhanced extraction based on actual Extra.com API
    if (apiProduct.vipPriceValueDiscount && typeof apiProduct.vipPriceValueDiscount === 'number') {
      memberPrice = apiProduct.vipPriceValueDiscount;
      console.log(`🏆 [Extra API] Found VIP/Jood Gold price: ${memberPrice}`);
    } else if (apiProduct.memberPrice && typeof apiProduct.memberPrice === 'number') {
      memberPrice = apiProduct.memberPrice;
    } else if (apiProduct.joodGoldPrice && typeof apiProduct.joodGoldPrice === 'number') {
      memberPrice = apiProduct.joodGoldPrice;
    } else if (apiProduct.loyaltyPrice && typeof apiProduct.loyaltyPrice === 'number') {
      memberPrice = apiProduct.loyaltyPrice;
    } else if (apiProduct.vipPrice && typeof apiProduct.vipPrice === 'number') {
      memberPrice = apiProduct.vipPrice;
    }



    // Try to get regular/was price (Original price before discount) - Enhanced for Extra.com API
    if (apiProduct.wasPrice && typeof apiProduct.wasPrice === 'number') {
      regularPrice = apiProduct.wasPrice;
      console.log(`📄 [Extra API] Found wasPrice (original): ${regularPrice}`);
    } else if (apiProduct.basicPrimePrice && typeof apiProduct.basicPrimePrice === 'number') {
      regularPrice = apiProduct.basicPrimePrice;
    } else if (apiProduct.originalPrice && typeof apiProduct.originalPrice === 'number') {
      regularPrice = apiProduct.originalPrice;
    } else if (apiProduct.listPrice && typeof apiProduct.listPrice === 'number') {
      regularPrice = apiProduct.listPrice;
    } else {
      // Calculate original price from discount information if available
      if (apiProduct.discount && apiProduct.discount > 0 && currentPrice > 0) {
        // If discount is percentage
        if (apiProduct.discount < 1) {
          regularPrice = currentPrice / (1 - apiProduct.discount);
        } else if (apiProduct.discount > 1) {
          // If discount is absolute amount
          regularPrice = currentPrice + apiProduct.discount;
        }
      } else {
        regularPrice = currentPrice; // Use current price as regular if no separate regular price
      }
    }

    // Check if there's a discount (sale price)
    if (apiProduct.discount && apiProduct.discount > 0 && regularPrice > currentPrice) {
      salePrice = currentPrice;
    }

    // Handle price value discount
    if (apiProduct.priceValueDiscount && typeof apiProduct.priceValueDiscount === 'number') {
      currentPrice = apiProduct.priceValueDiscount;
    }

    // Price precedence logic: Use the best available price as current price
    // CRITICAL FIX: For Extra store API, prioritize member price when available
    let finalCurrentPrice = currentPrice;
    if (memberPrice > 0) {
      // Use member price if it's valid, prioritizing customer savings
      finalCurrentPrice = memberPrice;
      console.log(`💰 [Extra API] Using member/Jood Gold price: ${finalCurrentPrice} instead of standard: ${currentPrice}`);
    } else if (salePrice && salePrice > 0 && salePrice < currentPrice) {
      finalCurrentPrice = salePrice;
      console.log(`💰 [Extra API] Using sale price: ${finalCurrentPrice}`);
    } else {
      console.log(`💰 [Extra API] Using standard price: ${finalCurrentPrice}`);
    }

    // Comprehensive logging for three-tier pricing debugging
    console.log(`🔍 [Extra API] Price extraction summary:`);
    console.log(`   📘 Standard Price: ${currentPrice} (finalCurrentPrice: ${finalCurrentPrice})`);
    console.log(`   🏆 Member/Jood Gold Price: ${memberPrice}`);
    console.log(`   📄 Regular/Was Price: ${regularPrice}`);
    console.log(`   💰 Sale Price: ${salePrice}`);

    // Log raw API fields for debugging
    if (apiProduct.vipPriceValueDiscount) console.log(`   🔧 API vipPriceValueDiscount: ${apiProduct.vipPriceValueDiscount}`);
    if (apiProduct.wasPrice) console.log(`   🔧 API wasPrice: ${apiProduct.wasPrice}`);
    if (apiProduct.wasTechnoPrice) console.log(`   🔧 API wasTechnoPrice: ${apiProduct.wasTechnoPrice}`);
    if (apiProduct.sellingPrice) console.log(`   🔧 API sellingPrice: ${apiProduct.sellingPrice}`);
    if (apiProduct.price) console.log(`   🔧 API price: ${apiProduct.price}`);

    return {
      currentPrice: finalCurrentPrice || 0,
      regularPrice: regularPrice || currentPrice || 0,
      memberPrice: memberPrice || 0, // NEW: Member/loyalty price
      salePrice: salePrice
    };
  } catch (error) {
    console.log(`⚠️ [Extra] Error extracting price info from API:`, error.message);
    return { currentPrice: 0, regularPrice: 0, memberPrice: 0, salePrice: null };
  }
}

/**
 * Extract product data from API response
 */
function extractProductFromApi(apiProduct, categoryName) {
  try {
    // Extract basic product information
    const productName = apiProduct.nameEn || apiProduct.title || apiProduct.autosuggest || '';
    const description = apiProduct.descriptionEn || apiProduct.description || productName;

    if (!productName || productName.length < 3) {
      return null; // Skip products without valid names
    }

    // Extract brand and model
    const brand = extractBrandFromApi(apiProduct);
    const model = extractModelFromApi(apiProduct, brand);

    // Extract price information
    const priceInfo = extractPriceInfoFromApi(apiProduct);

    // Extract stock information
    const stockInfo = extractStockInfoFromApi(apiProduct);

    // Extract image URL
    let imageUrl = '';
    if (apiProduct.imageUrl && Array.isArray(apiProduct.imageUrl) && apiProduct.imageUrl.length > 0) {
      imageUrl = apiProduct.imageUrl[0];
      if (imageUrl.startsWith('//')) {
        imageUrl = 'https:' + imageUrl;
      }
    } else if (apiProduct.amplienceProductBaseUrl) {
      imageUrl = apiProduct.amplienceProductBaseUrl;
      if (imageUrl.startsWith('//')) {
        imageUrl = 'https:' + imageUrl;
      }
    }

    // Extract product URL
    let productUrl = '';
    if (apiProduct.productUrl) {
      productUrl = apiProduct.productUrl;
    } else if (apiProduct.urlEn) {
      productUrl = EXTRA_CONFIG.baseUrl + apiProduct.urlEn;
    }

    // Extract size and UOM
    const { size, uom } = extractSizeAndUOM(productName);

    // Generate unique SKU
    globalProductCounter++;
    const sku = apiProduct.productCode || `EXTRA-${model || brand}-${globalProductCounter}`;

    // Normalize category
    const normalizedCategory = normalizeCategoryName(categoryName);
    const extraCategorySlug = createCategorySlug(categoryName);

    // Create product object following existing scraper patterns with enhanced stock tracking
    const product = {
      name: productName,
      description: description,
      brand: brand,
      model: model || '',
      sku: sku,
      price: priceInfo.salePrice, // Promotional/discount price (null if no promotion)
      current_price: priceInfo.currentPrice.toString(), // Current effective price
      regular_price: priceInfo.regularPrice.toString(), // Regular/list price
      member_price: priceInfo.memberPrice || 0, // NEW: Member/Jood Gold price
      externalId: apiProduct.productCode || apiProduct.uniqueId || `extra-${globalProductCounter}`,
      url: productUrl,
      imageUrl: imageUrl,
      // Legacy stock fields for compatibility
      stock: stockInfo.stock,
      stockStatus: stockInfo.stockStatus,
      // New enhanced stock fields
      quantity_available: stockInfo.quantity_available,
      stock_status: stockInfo.stock_status,
      inventory_last_updated: getSafeTimestamp(),
      category: normalizedCategory,
      categorySlug: extraCategorySlug,
      store: EXTRA_CONFIG.name,
      size: size || '',
      uom: uom || ''
    };

    return product;
  } catch (error) {
    console.log(`⚠️ [Extra] Error extracting product from API:`, error.message);
    return null;
  }
}

/**
 * Search products using Unbxd API
 */
async function searchProductsApi(searchQuery, page = 1, rows = 1000) {
  try {
    const apiUrl = buildUnbxdApiUrl(searchQuery, page, rows);
    console.log(`🔍 [Extra] API Request: ${apiUrl}`);

    const response = await retryRequest(async () => {
      return await axiosInstance.get(apiUrl);
    });

    if (response.status !== 200) {
      console.log(`⚠️ [Extra] API returned status ${response.status}`);
      return { products: [], totalProducts: 0, hasMore: false };
    }

    const data = response.data;

    if (!data.response || !data.response.products) {
      console.log(`⚠️ [Extra] Invalid API response structure`);
      return { products: [], totalProducts: 0, hasMore: false };
    }

    const products = data.response.products;
    const totalProducts = data.searchMetaData?.totalProducts || products.length;
    const hasMore = products.length === rows && (page * rows) < totalProducts;

    console.log(`📦 [Extra] API returned ${products.length} products (total: ${totalProducts})`);

    return {
      products: products,
      totalProducts: totalProducts,
      hasMore: hasMore
    };

  } catch (error) {
    console.log(`❌ [Extra] API search failed for "${searchQuery}":`, error.message);
    return { products: [], totalProducts: 0, hasMore: false };
  }
}

/**
 * Parse products from category page HTML
 */
function parseProductsFromHTML(html, categoryName) {
  try {
    const dom = new JSDOM(html);
    const document = dom.window.document;

    // Check if this is a product listing page by looking for specific Extra.com elements
    const pageTitle = document.querySelector('title')?.textContent || '';
    console.log(`📄 [Extra] Page title: ${pageTitle}`);

    // Extra.com specific product selectors based on their website structure
    const productSelectors = [
      '.product-item',
      '.product-tile',
      '.product-card',
      '.item-product',
      '.product-list-item',
      '.product-grid-item',
      '[data-product-code]',
      '[data-product-id]',
      '.js-product-item',
      '.product-wrapper'
    ];

    let products = [];

    // Try each selector to find products
    for (const selector of productSelectors) {
      const productElements = document.querySelectorAll(selector);

      if (productElements.length > 0) {
        console.log(`📦 [Extra] Found ${productElements.length} products using selector: ${selector}`);

        for (const productElement of productElements) {
          try {
            const product = extractProductFromElement(productElement, categoryName);
            if (product && product.name && product.name.trim().length > 0) {
              products.push(product);
            }
          } catch (error) {
            console.log(`⚠️ [Extra] Error parsing individual product:`, error.message);
          }
        }

        if (products.length > 0) {
          console.log(`✅ [Extra] Successfully extracted ${products.length} products`);
          break; // Found products with this selector, no need to try others
        }
      }
    }

    // If no products found with specific selectors, try to find any links that might be products
    if (products.length === 0) {
      console.log(`🔍 [Extra] No products found with standard selectors, trying alternative approach...`);

      // Look for product links in the page - Extra.com uses /p/ pattern
      const productLinks = document.querySelectorAll('a[href*="/p/"]');
      console.log(`🔗 [Extra] Found ${productLinks.length} potential product links`);

      if (productLinks.length > 0) {
        // Extract product info from links and their parent elements
        for (let i = 0; i < Math.min(productLinks.length, 24); i++) {
          const link = productLinks[i];
          const productUrl = link.getAttribute('href');

          // Skip gift cards and non-product links
          if (!productUrl || productUrl.includes('gift-card') || productUrl.includes('extra-gift-card')) {
            continue;
          }

          // Get product name from link text or nearby elements
          let productName = link.textContent?.trim() || link.getAttribute('title') || '';

          // If link text is empty or too short, look for product name in parent elements
          if (!productName || productName.length < 5) {
            const parentElement = link.closest('.product-item, .product-card, .item, .product-wrapper') || link.parentElement;
            if (parentElement) {
              // Look for product name in various elements
              const nameSelectors = [
                '.product-name', '.product-title', '.title', 'h3', 'h4', 'h5',
                '[class*="name"]', '[class*="title"]', '[data-product-name]'
              ];

              for (const selector of nameSelectors) {
                const nameElement = parentElement.querySelector(selector);
                if (nameElement && nameElement.textContent.trim().length > 5) {
                  productName = nameElement.textContent.trim();
                  break;
                }
              }
            }
          }

          // Extract price information from parent element
          let currentPrice = '0';
          let regularPrice = '0';

          const parentElement = link.closest('.product-item, .product-card, .item, .product-wrapper') || link.parentElement;
          if (parentElement) {
            const priceSelectors = [
              '.price', '.current-price', '.sale-price', '[class*="price"]', '[class*="Price"]'
            ];

            for (const selector of priceSelectors) {
              const priceElement = parentElement.querySelector(selector);
              if (priceElement) {
                const priceText = priceElement.textContent.trim();
                const priceMatch = priceText.match(/[\d,]+\.?\d*/);
                if (priceMatch) {
                  currentPrice = priceMatch[0].replace(/,/g, '');
                  regularPrice = currentPrice; // Use same price for both if no separate regular price
                  break;
                }
              }
            }
          }

          // Extract image URL
          let imageUrl = '';
          if (parentElement) {
            const imgElement = parentElement.querySelector('img');
            if (imgElement) {
              imageUrl = imgElement.getAttribute('src') || imgElement.getAttribute('data-src') || '';
              if (imageUrl && imageUrl.startsWith('//')) {
                imageUrl = 'https:' + imageUrl;
              } else if (imageUrl && imageUrl.startsWith('/')) {
                imageUrl = EXTRA_CONFIG.baseUrl + imageUrl;
              }
            }
          }

          if (productName && productName.length > 5) {
            const brand = extractBrand(productName);
            const model = extractModel(productName, brand);
            const { size, uom } = extractSizeAndUOM(productName);

            globalProductCounter++;
            const sku = `EXTRA-${model || brand}-${globalProductCounter}`;

            products.push({
              name: productName,
              description: productName,
              brand: brand,
              model: model || '',
              sku: sku,
              price: null, // Sale price (null if no promotion)
              current_price: currentPrice,
              regular_price: regularPrice,
              externalId: `extra-${globalProductCounter}`,
              url: productUrl.startsWith('http') ? productUrl : EXTRA_CONFIG.baseUrl + productUrl,
              imageUrl: imageUrl,
              // Legacy stock fields for compatibility
              stock: 10, // Default stock
              stockStatus: 'In Stock',
              // New enhanced stock fields
              quantity_available: 10,
              stock_status: 'in_stock',
              inventory_last_updated: getSafeTimestamp(),
              category: normalizeCategoryName(categoryName),
              categorySlug: createCategorySlug(categoryName),
              store: EXTRA_CONFIG.name,
              size: size || '',
              uom: uom || ''
            });
          }
        }
      }
    }

    return products;
  } catch (error) {
    console.log(`❌ [Extra] Error parsing products from HTML:`, error.message);
    return [];
  }
}

/**
 * Extract product data from individual product element
 */
function extractProductFromElement(productElement, categoryName) {
  try {
    // Extract product name
    const nameSelectors = [
      '.product-name',
      '.product-title',
      '.item-name',
      '.title',
      'h3',
      'h4',
      '.name'
    ];

    let productName = '';
    for (const selector of nameSelectors) {
      const nameElement = productElement.querySelector(selector);
      if (nameElement) {
        productName = nameElement.textContent.trim();
        if (productName) break;
      }
    }

    if (!productName) {
      return null; // Skip products without names
    }

    // Extract brand and model
    const brand = extractBrand(productName);
    const model = extractModel(productName, brand);

    // Extract price information
    const priceInfo = extractPriceInfo(productElement);

    // Extract stock information
    const stockInfo = extractStockInfo(productElement);

    // Extract image URL
    const imageUrl = extractImageUrl(productElement);

    // Extract product URL
    const productUrl = extractProductUrl(productElement);

    // Extract size and UOM
    const { size, uom } = extractSizeAndUOM(productName);

    // Generate unique SKU
    globalProductCounter++;
    const sku = `EXTRA-${model || brand}-${globalProductCounter}`;

    // Normalize category
    const normalizedCategory = normalizeCategoryName(categoryName);
    const extraCategorySlug = createCategorySlug(categoryName);

    // Create product object following existing scraper patterns with enhanced stock tracking
    const product = {
      name: productName,
      description: productName, // Use name as description for now
      brand: brand,
      model: model || '',
      sku: sku,
      price: priceInfo.salePrice, // Promotional/discount price (null if no promotion)
      current_price: priceInfo.currentPrice.toString(), // Current effective price
      regular_price: priceInfo.regularPrice.toString(), // Regular/list price
      member_price: priceInfo.memberPrice || 0, // NEW: Member/Jood Gold price
      externalId: `extra-${globalProductCounter}`,
      url: productUrl,
      imageUrl: imageUrl,
      // Legacy stock fields for compatibility
      stock: stockInfo.stock,
      stockStatus: stockInfo.stockStatus,
      // New enhanced stock fields
      quantity_available: stockInfo.quantity_available,
      stock_status: stockInfo.stock_status,
      inventory_last_updated: getSafeTimestamp(),
      category: normalizedCategory,
      categorySlug: extraCategorySlug,
      store: EXTRA_CONFIG.name,
      size: size || '',
      uom: uom || ''
    };

    return product;
  } catch (error) {
    console.log(`⚠️ [Extra] Error extracting product from element:`, error.message);
    return null;
  }
}

/**
 * Scrape products using API-based approach with comprehensive pagination
 */
async function scrapeApiCategoryProducts(searchTerms, categoryName) {
  console.log(`🔍 [Extra] API scraping category: ${categoryName}`);
  console.log(`🔍 [Extra] Search terms: ${searchTerms.join(', ')}`);

  let allProducts = [];
  const seenProductIds = new Set();

  // Process each search term to get comprehensive coverage
  for (const searchTerm of searchTerms) {
    try {
      console.log(`\n🔍 [Extra] Processing search term: "${searchTerm}"`);

      let currentPage = 1;
      let hasMorePages = true;
      let consecutiveEmptyPages = 0;
      const maxPages = EXTRA_CONFIG.maxPagesPerCategory;
      const maxConsecutiveEmptyPages = EXTRA_CONFIG.maxConsecutiveEmptyPages;

      while (hasMorePages && currentPage <= maxPages && consecutiveEmptyPages < maxConsecutiveEmptyPages) {
        try {
          // Enhanced progress monitoring
          const progressPercent = ((currentPage - 1) / maxPages * 100).toFixed(1);
          console.log(`📄 [Extra] Fetching page ${currentPage}/${maxPages} (${progressPercent}%) for "${searchTerm}"...`);

          const startTime = Date.now();
          const apiResult = await searchProductsApi(searchTerm, currentPage, EXTRA_CONFIG.rowsPerPage);
          const responseTime = Date.now() - startTime;

          console.log(`⏱️ [Extra] API response time: ${responseTime}ms`);

          if (apiResult.products.length === 0) {
            console.log(`📭 [Extra] No products found on page ${currentPage} for "${searchTerm}"`);
            consecutiveEmptyPages++;
            console.log(`⚠️ [Extra] Consecutive empty pages: ${consecutiveEmptyPages}/${maxConsecutiveEmptyPages}`);

            if (currentPage === 1) {
              // If first page is empty, no point continuing with this search term
              console.log(`🚫 [Extra] First page empty for "${searchTerm}" - skipping remaining pages`);
              break;
            }
          } else {
            // CRITICAL FIX: Properly reset consecutive empty pages counter
            if (consecutiveEmptyPages > 0) {
              console.log(`🔄 [Extra] Resetting consecutive empty pages counter (was ${consecutiveEmptyPages})`);
              consecutiveEmptyPages = 0;
            }
            console.log(`📦 [Extra] Found ${apiResult.products.length} products on page ${currentPage}`);

            // Process and deduplicate products
            let newProductsCount = 0;
            for (const apiProduct of apiResult.products) {
              try {
                const product = extractProductFromApi(apiProduct, categoryName);

                if (product && product.externalId) {
                  // Enhanced deduplication using external_id as primary key
                  const productKey = product.externalId;

                  if (!seenProductIds.has(productKey)) {
                    seenProductIds.add(productKey);
                    allProducts.push(product);
                    newProductsCount++;
                  } else {
                    // Log duplicate detection for debugging
                    console.log(`🔄 [Extra] Duplicate product detected: ${product.externalId} - ${product.name}`);
                  }
                }
              } catch (error) {
                console.log(`⚠️ [Extra] Error processing individual product:`, error.message);
              }
            }

            console.log(`✅ [Extra] Added ${newProductsCount} new products from page ${currentPage} (${apiResult.products.length - newProductsCount} duplicates skipped)`);
          }

          // Enhanced pagination logic with better hasMore detection
          hasMorePages = apiResult.hasMore;

          // Additional check: if we got fewer products than requested, likely no more pages
          if (apiResult.products.length < EXTRA_CONFIG.rowsPerPage) {
            console.log(`📊 [Extra] Got ${apiResult.products.length} < ${EXTRA_CONFIG.rowsPerPage} products - likely last page`);
            hasMorePages = false;
          }

          currentPage++;

          // Enhanced progress logging
          if (hasMorePages) {
            console.log(`➡️ [Extra] Proceeding to page ${currentPage} (${consecutiveEmptyPages} consecutive empty pages)`);
          } else {
            console.log(`🏁 [Extra] No more pages available for "${searchTerm}"`);
          }

          // Add delay between API requests with rate limit detection
          await delay(EXTRA_CONFIG.requestDelay);

        } catch (error) {
          console.log(`❌ [Extra] Error fetching page ${currentPage} for "${searchTerm}":`, error.message);
          consecutiveEmptyPages++;

          // Enhanced error handling with specific responses
          if (error.response?.status === 429) {
            console.log(`🚫 [Extra] Rate limited (HTTP 429) - waiting 10 seconds...`);
            await delay(10000);
          } else if (error.message.includes('timeout') || error.code === 'ECONNABORTED') {
            console.log(`⏳ [Extra] Timeout detected - waiting 5 seconds...`);
            await delay(5000);
          } else if (error.response?.status >= 500) {
            console.log(`🔧 [Extra] Server error (${error.response.status}) - waiting 3 seconds...`);
            await delay(3000);
          } else {
            console.log(`⚠️ [Extra] Other error - continuing with normal delay...`);
            await delay(EXTRA_CONFIG.requestDelay);
          }
        }
      }

      console.log(`🎯 [Extra] Completed search term "${searchTerm}": processed ${currentPage - 1} pages`);

      // Add delay between search terms
      await delay(2000);

    } catch (error) {
      console.log(`❌ [Extra] Error processing search term "${searchTerm}":`, error.message);
    }
  }

  console.log(`🎯 [Extra] Total unique products found for ${categoryName}: ${allProducts.length}`);
  return allProducts;
}

/**
 * Scrape products from a specific category (legacy HTML-based approach - kept as fallback)
 */
async function scrapeCategoryProducts(categoryPath, categoryName, maxPages = 3) {
  console.log(`🔍 [Extra] HTML scraping category: ${categoryName} (${categoryPath})`);

  let allProducts = [];
  let currentPage = 0;
  let hasMorePages = true;

  while (hasMorePages && currentPage < maxPages) {
    try {
      // Build category URL - try both faceted and direct category URLs
      let categoryUrl;
      if (currentPage === 0) {
        // First try the direct category URL
        categoryUrl = `${EXTRA_CONFIG.englishUrl}${categoryPath}`;
      } else {
        // For subsequent pages, use faceted search with pagination
        categoryUrl = `${EXTRA_CONFIG.englishUrl}${categoryPath}/facet/?q=%3Arelevance%3AinStock%3Atrue%3Atype%3APRODUCT&text=&pg=${currentPage}&pageSize=24&sort=relevance`;
      }

      console.log(`📄 [Extra] Fetching page ${currentPage + 1} for ${categoryName}...`);
      console.log(`🔗 [Extra] URL: ${categoryUrl}`);

      const response = await retryRequest(async () => {
        // Add random delay to avoid being detected as bot
        await delay(Math.random() * 2000 + 1000);
        return await axiosInstance.get(categoryUrl);
      });

      if (response.status !== 200) {
        console.log(`⚠️ [Extra] Non-200 status for ${categoryName} page ${currentPage + 1}: ${response.status}`);
        break;
      }

      console.log(`📊 [Extra] Response received: ${response.data.length} characters`);

      // Check if we're being blocked
      if (response.data.includes('blocked') || response.data.includes('captcha') || response.data.includes('Access Denied')) {
        console.log(`🚫 [Extra] Detected blocking/captcha for ${categoryName}`);
        hasMorePages = false;
        break;
      }

      // Parse products from HTML
      const products = parseProductsFromHTML(response.data, categoryName);

      if (products.length === 0) {
        console.log(`📭 [Extra] No products found on page ${currentPage + 1} for ${categoryName}`);
        hasMorePages = false;
      } else {
        console.log(`✅ [Extra] Found ${products.length} products on page ${currentPage + 1} for ${categoryName}`);
        allProducts.push(...products);
        currentPage++;

        // Add longer delay between requests to avoid rate limiting
        await delay(3000 + Math.random() * 2000);
      }

    } catch (error) {
      console.log(`❌ [Extra] Error scraping ${categoryName} page ${currentPage + 1}:`, error.message);

      // If it's a timeout or connection error, try once more
      if (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT') {
        console.log(`🔄 [Extra] Retrying due to connection issue...`);
        await delay(5000);
        continue;
      }

      hasMorePages = false;
    }
  }

  console.log(`🎯 [Extra] Total products found for ${categoryName}: ${allProducts.length}`);
  return allProducts;
}

/**
 * Main Extra.com scraping function
 */
async function scrapeExtra(useCache = false, useBulkOperations = true) {
  console.log(`🚀 Starting Extra.com scraper`);
  console.log(`   useCache: ${useCache}, useBulkOperations: ${useBulkOperations}`);

  const startTime = Date.now();
  let allProducts = [];
  let errors = [];

  try {
    // Ensure Extra store exists in database
    await ensureExtraStoreExists();

    // Get store information
    const store = await storage.getStoreBySlug(EXTRA_CONFIG.slug);
    if (!store) {
      throw new Error('Extra store not found in database');
    }

    console.log(`✅ [Extra] Store found: ${store.name} (ID: ${store.id})`);

    // CRITICAL FIX: Replace deletion logic with upsert preparation
    console.log(`🔄 [Extra] Preparing for upsert operations (preserving existing data)...`);

    // Get existing products for upsert logic
    const existingProducts = await storage.executeQuery(`
      SELECT id, external_id, sku, url, name FROM products WHERE store_id = $1
    `, [store.id]);

    const existingProductMap = new Map();
    if (existingProducts && existingProducts.length > 0) {
      existingProducts.forEach(product => {
        // Create multiple lookup keys for robust matching
        if (product.external_id) existingProductMap.set(`ext_${product.external_id}`, product);
        if (product.sku) existingProductMap.set(`sku_${product.sku}`, product);
        if (product.url) existingProductMap.set(`url_${product.url}`, product);
      });
      console.log(`📊 [Extra] Found ${existingProducts.length} existing products for potential updates`);
    } else {
      console.log(`📊 [Extra] No existing products found - all will be new insertions`);
    }

    // Store the map for use in bulk operations
    global.extraExistingProductMap = existingProductMap;

    // Scrape all categories using API-based approach
    for (const categoryData of Object.values(EXTRA_CATEGORIES)) {
      console.log(`\n🏷️ [Extra] Processing category group: ${categoryData.name}`);

      try {
        // First, scrape using main category search terms
        console.log(`\n📂 [Extra] Scraping main category: ${categoryData.name}`);
        const mainCategoryProducts = await scrapeApiCategoryProducts(categoryData.searchTerms, categoryData.name);

        if (mainCategoryProducts.length > 0) {
          allProducts.push(...mainCategoryProducts);
          console.log(`✅ [Extra] Added ${mainCategoryProducts.length} products from main category ${categoryData.name}`);
        }

        // Then scrape each subcategory for more comprehensive coverage
        for (const subcategory of categoryData.subcategories) {
          try {
            console.log(`\n📂 [Extra] Scraping subcategory: ${subcategory.name}`);

            const subcategoryProducts = await scrapeApiCategoryProducts(subcategory.searchTerms, subcategory.name);

            if (subcategoryProducts.length > 0) {
              allProducts.push(...subcategoryProducts);
              console.log(`✅ [Extra] Added ${subcategoryProducts.length} products from ${subcategory.name}`);
            }

            // Add delay between subcategories
            await delay(1500);

          } catch (error) {
            console.log(`❌ [Extra] Error scraping subcategory ${subcategory.name}:`, error.message);
            errors.push({
              category: subcategory.name,
              error: error.message
            });
          }
        }

        // Add longer delay between main categories
        await delay(3000);

      } catch (error) {
        console.log(`❌ [Extra] Error scraping category ${categoryData.name}:`, error.message);
        errors.push({
          category: categoryData.name,
          error: error.message
        });
      }
    }

    console.log(`\n📊 [Extra] Scraping completed. Total products found: ${allProducts.length}`);

    // Store products in database
    if (allProducts.length > 0) {
      console.log(`💾 [Extra] Storing products in database...`);

      if (useBulkOperations) {
        await storeBulkProducts(allProducts, store.id);
      } else {
        await storeIndividualProducts(allProducts, store.id);
      }

      console.log(`✅ [Extra] Products stored successfully`);
    }

    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);

    console.log(`\n🎉 [Extra] Scraping completed in ${duration} seconds`);
    console.log(`📈 [Extra] Final stats:`);
    console.log(`   - Products found: ${allProducts.length}`);
    console.log(`   - Errors: ${errors.length}`);

    // Send completion email
    await sendScraperCompletionEmail('Extra', {
      totalProducts: allProducts.length,
      newProducts: allProducts.length, // Assuming all are new for now
      updatedProducts: 0,
      errors: errors,
      duration: `${duration} seconds`
    });

    return {
      products: allProducts,
      count: allProducts.length,
      errors: errors,
      duration: duration
    };

  } catch (error) {
    console.log(`❌ [Extra] Fatal error during scraping:`, error.message);

    return {
      products: [],
      count: 0,
      errors: [{ store: 'extra', error: error.message }],
      duration: Math.round((Date.now() - startTime) / 1000)
    };
  }
}

/**
 * Ensure Extra store exists in database
 */
async function ensureExtraStoreExists() {
  try {
    const existingStore = await storage.getStoreBySlug(EXTRA_CONFIG.slug);

    if (!existingStore) {
      console.log(`🏪 [Extra] Creating store in database...`);

      const newStore = await storage.createStore({
        name: EXTRA_CONFIG.name,
        slug: EXTRA_CONFIG.slug,
        url: EXTRA_CONFIG.url,
        active: true
      });

      console.log(`✅ [Extra] Store created successfully with ID: ${newStore.id}`);
    } else {
      console.log(`✅ [Extra] Store already exists: ${existingStore.name} (ID: ${existingStore.id})`);
    }
  } catch (error) {
    console.log(`❌ [Extra] Error ensuring store exists:`, error.message);
    throw error;
  }
}

/**
 * Store products using bulk operations
 */
async function storeBulkProducts(products, storeId) {
  try {
    console.log(`💾 [Extra] Storing ${products.length} products using bulk operations...`);

    // Prepare products for bulk creation with enhanced stock tracking
    const productsForBulk = products.map(product => ({
      name: product.name,
      description: product.description,
      brand: product.brand,
      model: product.model,
      sku: product.sku,
      price: product.current_price,
      regular_price: product.regular_price,
      member_price: product.member_price,                  // NEW: Member/Jood Gold price
      storeId: storeId,
      categoryId: null, // Will be set later if needed
      externalId: product.externalId,
      url: product.url,
      imageUrl: product.imageUrl,
      stock: product.stock || 0,                           // Legacy stock field
      quantity_available: product.quantity_available,      // New enhanced quantity field
      stock_status: product.stock_status || 'unknown',     // New standardized stock status
      inventory_last_updated: product.inventory_last_updated || new Date()
    }));

    // CRITICAL FIX: Final deduplication to prevent database conflicts
    console.log(`🔄 [Extra] Performing final deduplication to prevent database conflicts...`);

    const uniqueProductsForBulk = [];
    const seenExternalIds = new Set();

    for (const product of productsForBulk) {
      if (product.externalId && !seenExternalIds.has(product.externalId)) {
        seenExternalIds.add(product.externalId);
        uniqueProductsForBulk.push(product);
      } else if (product.externalId) {
        console.log(`🔄 [Extra] Final dedup: Skipping duplicate external_id ${product.externalId}`);
      } else {
        // Products without external_id can be added (though this shouldn't happen)
        uniqueProductsForBulk.push(product);
      }
    }

    console.log(`🔄 [Extra] After final deduplication: ${uniqueProductsForBulk.length} unique products (${productsForBulk.length - uniqueProductsForBulk.length} duplicates removed)`);

    // CRITICAL FIX: Use upsert logic instead of bulk create to preserve existing data
    console.log(`🔄 [Extra] Using upsert operations to preserve existing product data...`);

    const existingProductMap = global.extraExistingProductMap || new Map();
    const productsToCreate = [];
    const productsToUpdate = [];

    // Separate products into create vs update based on existing data
    for (const product of uniqueProductsForBulk) {
      let existingProduct = null;

      // Try to find existing product by multiple identifiers
      if (product.externalId) {
        existingProduct = existingProductMap.get(`ext_${product.externalId}`);
      }
      if (!existingProduct && product.sku) {
        existingProduct = existingProductMap.get(`sku_${product.sku}`);
      }
      if (!existingProduct && product.url) {
        existingProduct = existingProductMap.get(`url_${product.url}`);
      }

      if (existingProduct) {
        // Product exists - prepare for update
        productsToUpdate.push({
          id: existingProduct.id,
          ...product,
          // Preserve creation timestamp
          createdAt: existingProduct.created_at
        });
      } else {
        // New product - prepare for creation
        productsToCreate.push(product);
      }
    }

    console.log(`📊 [Extra] Products to create: ${productsToCreate.length}, to update: ${productsToUpdate.length}`);

    // Create new products
    let createdProducts = [];
    if (productsToCreate.length > 0) {
      try {
        createdProducts = await storage.bulkCreateProducts(productsToCreate);

        // Validate that we got valid results
        if (!createdProducts || !Array.isArray(createdProducts)) {
          console.log(`⚠️ [Extra] Invalid result from bulkCreateProducts:`, createdProducts);
          createdProducts = [];
        } else {
          // Filter out any invalid products
          createdProducts = createdProducts.filter(product => product && product.id);
          console.log(`✅ [Extra] Created ${createdProducts.length} new products`);
        }
      } catch (error) {
        console.log(`❌ [Extra] Error creating products in bulk:`, error.message);
        createdProducts = [];
      }
    }

    // Update existing products
    let updatedProducts = [];
    if (productsToUpdate.length > 0) {
      // Use individual updates for now (can be optimized to bulk updates later)
      for (const productUpdate of productsToUpdate) {
        try {
          const { id, ...updateData } = productUpdate;
          await storage.updateProduct(id, updateData);
          updatedProducts.push({ id, ...updateData });
        } catch (error) {
          console.log(`⚠️ [Extra] Error updating product ${productUpdate.id}:`, error.message);
        }
      }
      console.log(`✅ [Extra] Updated ${updatedProducts.length} existing products`);
    }

    // Combine created and updated products for price/quantity operations
    const allProcessedProducts = [...createdProducts, ...updatedProducts];

    // Filter out products without valid IDs to prevent errors
    const validProcessedProducts = allProcessedProducts.filter(product =>
      product && product.id && typeof product.id === 'number'
    );

    if (validProcessedProducts.length !== allProcessedProducts.length) {
      console.log(`⚠️ [Extra] Filtered out ${allProcessedProducts.length - validProcessedProducts.length} products without valid IDs`);
    }

    // Prepare price data for bulk creation with enhanced stock tracking
    const pricesForBulk = validProcessedProducts.map((product, index) => {
      // Find corresponding original product data
      const originalProduct = products.find(p =>
        p.externalId === product.externalId ||
        p.sku === product.sku ||
        p.url === product.url
      ) || products[index] || {};

      return {
        productId: product.id,
        price: parseFloat(originalProduct.current_price) || 0,
        currency: 'SAR',
        stock: originalProduct.quantity_available || originalProduct.stock || 10, // Default to 10 for in-stock
        status: originalProduct.stock_status || 'in_stock' // Default to in_stock
      };
    });

    // Use bulk create prices method (this will handle upserts internally)
    await storage.bulkCreateProductPrices(pricesForBulk);
    console.log(`✅ [Extra] Created/updated ${pricesForBulk.length} price records`);

    // Prepare quantity data for bulk creation with enhanced stock tracking
    const quantitiesForBulk = allProcessedProducts.map((product, index) => {
      // Find corresponding original product data
      const originalProduct = products.find(p =>
        p.externalId === product.externalId ||
        p.sku === product.sku ||
        p.url === product.url
      ) || products[index] || {};

      return {
        productId: product.id,
        quantity: originalProduct.quantity_available || originalProduct.stock || 10, // Default to 10 for in-stock
        status: originalProduct.stock_status || 'in_stock', // Default to in_stock
        source: 'extra-scraper'
      };
    });

    // Use bulk create quantities method (this will handle upserts internally)
    await storage.bulkCreateProductQuantities(quantitiesForBulk);
    console.log(`✅ [Extra] Created/updated ${quantitiesForBulk.length} quantity records`);

    console.log(`✅ [Extra] Bulk storage completed successfully`);
  } catch (error) {
    console.log(`❌ [Extra] Error in bulk storage:`, error.message);
    // Fall back to individual storage
    console.log(`🔄 [Extra] Falling back to individual storage...`);
    await storeIndividualProducts(products, storeId);
  }
}

/**
 * Store products individually (fallback method)
 */
async function storeIndividualProducts(products, storeId) {
  try {
    console.log(`💾 [Extra] Storing ${products.length} products individually...`);

    for (let i = 0; i < products.length; i++) {
      const product = products[i];

      try {
        // UPSERT product with enhanced stock tracking (using schema-compliant column names)
        const productResult = await storage.executeQuery(`
          INSERT INTO products (
            name, description, brand, model, sku, external_id, url, image_url, store_id, size, uom,
            price, regular_price, member_price, stock, quantity_available, stock_status, inventory_last_updated,
            created_at, updated_at
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, NOW(), NOW())
          ON CONFLICT (external_id) DO UPDATE SET
            name = EXCLUDED.name,
            description = EXCLUDED.description,
            brand = EXCLUDED.brand,
            model = EXCLUDED.model,
            sku = EXCLUDED.sku,
            price = EXCLUDED.price,
            regular_price = EXCLUDED.regular_price,
            member_price = EXCLUDED.member_price,
            url = EXCLUDED.url,
            image_url = EXCLUDED.image_url,
            size = EXCLUDED.size,
            uom = EXCLUDED.uom,
            stock = EXCLUDED.stock,
            quantity_available = EXCLUDED.quantity_available,
            stock_status = EXCLUDED.stock_status,
            inventory_last_updated = EXCLUDED.inventory_last_updated,
            updated_at = NOW()
          RETURNING id
        `, [
          product.name,
          product.description,
          product.brand,
          product.model,
          product.sku,
          product.externalId,
          product.url,
          product.imageUrl,
          storeId,
          product.size,
          product.uom,
          parseFloat(product.current_price) || 0,
          parseFloat(product.regular_price) || 0,
          parseFloat(product.member_price) || null,     // Member/Jood Gold price
          product.stock || 0,                           // Legacy stock field
          product.quantity_available,                   // New enhanced quantity field
          product.stock_status || 'unknown',            // New standardized stock status
          product.inventory_last_updated || new Date()  // Inventory timestamp
        ]);

        // Validate query result before accessing properties
        if (!productResult || !productResult.rows || productResult.rows.length === 0) {
          console.log(`⚠️ [Extra] No result returned for product ${i + 1}: ${product.name}`);
          console.log(`🔍 [Extra] Query result:`, productResult);
          continue; // Skip this product and move to the next one
        }

        const productId = productResult.rows[0].id;

        // Validate that we got a valid product ID
        if (!productId) {
          console.log(`⚠️ [Extra] No product ID returned for product ${i + 1}: ${product.name}`);
          continue; // Skip this product and move to the next one
        }

        // UPSERT price with enhanced stock status
        await storage.executeQuery(`
          INSERT INTO product_prices (product_id, price, currency, stock, status, timestamp)
          VALUES ($1, $2, $3, $4, $5, NOW())
          ON CONFLICT (product_id, timestamp) DO UPDATE SET
            price = EXCLUDED.price,
            stock = EXCLUDED.stock,
            status = EXCLUDED.status
        `, [
          productId,
          parseFloat(product.current_price) || 0,
          'SAR',
          product.quantity_available || product.stock || 0,
          product.stock_status || 'unknown'
        ]);

        // UPSERT quantity with enhanced stock status
        await storage.executeQuery(`
          INSERT INTO product_quantities (product_id, quantity, status, source, created_at, updated_at)
          VALUES ($1, $2, $3, $4, NOW(), NOW())
          ON CONFLICT (product_id, source) DO UPDATE SET
            quantity = EXCLUDED.quantity,
            status = EXCLUDED.status,
            updated_at = NOW()
        `, [
          productId,
          product.quantity_available || product.stock || 0,
          product.stock_status || 'unknown',
          'extra-scraper'
        ]);

        if ((i + 1) % 100 === 0) {
          console.log(`📊 [Extra] Stored ${i + 1}/${products.length} products`);
        }

      } catch (error) {
        console.log(`⚠️ [Extra] Error storing product ${i + 1}:`, error.message);
      }
    }

    console.log(`✅ [Extra] Individual storage completed`);
  } catch (error) {
    console.log(`❌ [Extra] Error in individual storage:`, error.message);
    throw error;
  }
}

// Export the main scraping function and configuration
export { scrapeExtra, scrapeCategoryProducts, scrapeApiCategoryProducts, searchProductsApi, extractProductFromApi, EXTRA_CONFIG, EXTRA_CATEGORIES };
