# 🔑 How to Get Fresh BlackBox Token

## Quick Method (Recommended)

1. **Open BlackBox website in Chrome/Edge**:
   ```
   https://www.blackbox.com.sa/en/category/small-appliances
   ```

2. **Open Developer Tools**:
   - Press `F12` or `Ctrl+Shift+I`
   - Go to **Network** tab
   - Check "Preserve log" option

3. **Refresh the page** (`F5` or `Ctrl+R`)

4. **Find API requests**:
   - Look for requests to `api.ops.blackbox.com.sa`
   - Filter by "XHR" or "Fetch" to see only API calls
   - Look for requests like `/search/products/facets/category/`

5. **Copy the token**:
   - Click on any API request
   - Go to **Request Headers** section
   - Find `Authorization: Bearer eyJ...`
   - Copy the entire token (starts with `eyJ`)

6. **Update the scraper**:
   ```javascript
   // In blackbox-scraper.js, update the bearerToken:
   bearerToken: 'YOUR_NEW_TOKEN_HERE'
   
   // Or add to backup tokens:
   backupTokens: [
     'YOUR_NEW_TOKEN_HERE'
   ]
   ```

## Alternative Method (Environment Variable)

Set the token as an environment variable:

```bash
# Windows
set BLACKBOX_BEARER_TOKEN=your_new_token_here

# Linux/Mac
export BLACKBOX_BEARER_TOKEN=your_new_token_here
```

## Test the Token

Run this to test if your token works:

```bash
node test-blackbox-api.js
```

## Expected Token Format

A valid BlackBox token looks like:
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTExODg4NTksInN0b3JlTmFtZSI6ImVuIn0.zTr3S8rtv-qlAShjqODCx2Cg65SRteVAA_9NBHaF13c
```

## Troubleshooting

### If you don't see API requests:
1. Make sure you're on a category page (not the homepage)
2. Try scrolling down to load more products
3. Check if JavaScript is enabled
4. Try a different category page

### If the token doesn't work:
1. Make sure you copied the complete token
2. Check that the token is recent (they expire quickly)
3. Try getting a token from a different browser session
4. Verify the token format (should start with `eyJ`)

### Common Issues:
- **Token too short**: Make sure you copied the complete token
- **Token expired**: Get a fresh token (they expire in 24-48 hours)
- **Wrong format**: Token should be three parts separated by dots

## Quick Fix Script

If you have a working token, you can quickly update it:

```bash
# Replace YOUR_TOKEN with your actual token
node -e "
const fs = require('fs');
const content = fs.readFileSync('blackbox-scraper.js', 'utf8');
const updated = content.replace(
  /bearerToken: process\.env\.BLACKBOX_BEARER_TOKEN \|\| '[^']+'/,
  'bearerToken: process.env.BLACKBOX_BEARER_TOKEN || \\'YOUR_TOKEN\\''
);
fs.writeFileSync('blackbox-scraper.js', updated);
console.log('✅ Token updated successfully');
"
```

## Notes

- BlackBox tokens expire frequently (usually 24-48 hours)
- The scraper will automatically try backup tokens if the main one fails
- You can add multiple backup tokens to the `backupTokens` array
- The scraper will test each backup token and use the first working one
