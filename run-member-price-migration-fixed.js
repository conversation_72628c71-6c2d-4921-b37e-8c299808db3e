const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

async function runMemberPriceMigration() {
  console.log('🚀 Running Member Price Migration...');
  
  // Read database URL from environment or use default
  const databaseUrl = process.env.DATABASE_URL || 'postgres://postgres:<EMAIL>:5432/ir2-ksa?sslmode=no-verify';
  
  const pool = new Pool({
    connectionString: databaseUrl,
    ssl: { rejectUnauthorized: false },
    connectionTimeoutMillis: 30000,
    idleTimeoutMillis: 30000,
    max: 1
  });

  try {
    console.log('✅ Connected to database');

    // Read the migration SQL file
    const migrationPath = path.join(__dirname, 'migrations', 'add-member-price-to-price-history.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('\n🔧 Running migration SQL...');
    
    // Split the SQL into individual statements and execute them
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    for (const statement of statements) {
      if (statement.trim()) {
        console.log(`   Executing: ${statement.substring(0, 50)}...`);
        await pool.query(statement);
      }
    }

    console.log('✅ Migration SQL executed successfully');

    // Verify the migration
    console.log('\n🔍 Verifying migration results...');
    
    const columnCheck = await pool.query(`
      SELECT table_name, column_name 
      FROM information_schema.columns 
      WHERE column_name = 'member_price' 
      AND table_name IN ('price_history', 'product_prices', 'products')
      ORDER BY table_name
    `);

    console.log('\n📊 Member price columns found:');
    columnCheck.rows.forEach(row => {
      console.log(`   ✅ ${row.table_name}.${row.column_name}`);
    });

    console.log('\n🎉 Member Price Migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  runMemberPriceMigration().catch(console.error);
}

module.exports = { runMemberPriceMigration };
